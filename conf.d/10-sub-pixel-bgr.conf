<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Enable sub-pixel rendering with the BGR stripes layout</description>
<!--  Enable sub-pixel rendering --> 
  <match target="pattern">
    <!--
      This sort of configuration is available on the major desktop environments
      and we don't have to break it with "assign" unconditionally. however, we
      want to set something for others. So we use "append" here to get this working
      in both cases so that most clients would takes a look at the first place only.
    -->
    <edit name="rgba" mode="append"><const>bgr</const></edit>
  </match>
</fontconfig>
