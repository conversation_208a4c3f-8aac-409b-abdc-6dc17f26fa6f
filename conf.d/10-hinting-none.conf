<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Set hintnone to hintstyle</description>

  <match target="pattern">
    <!--
      This sort of configuration is available on the major desktop environments
      and we don't have to break it with "assign" unconditionally. however, we
      want to set something for others. So we use "append" here to get this working
      in both cases so that most clients would takes a look at the first place only.
    -->
    <edit name="hintstyle" mode="append"><const>hintnone</const></edit>
  </match>
</fontconfig>
