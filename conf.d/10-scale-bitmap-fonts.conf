<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Bitmap scaling</description>
<!--
  If font is bitmap, calculate scale factor.
  Note that color bitmap fonts have scalable=true, while
  non-color ones have scalable=false.  Both groups have outline=false.
  -->
<match target="font">
  <test name="outline" compare="eq">
    <bool>false</bool>
  </test>
  <edit name="pixelsizefixupfactor" mode="assign">
    <divide>
      <name target="pattern">pixelsize</name>
      <name target="font"   >pixelsize</name>
    </divide>
  </edit>
</match>
<!--
  For non-scalable bitmap fonts (ie. non-color), skip
  minor scaling if hinting is enabled.
  -->
<match target="font">
  <test name="outline" compare="eq">
    <bool>false</bool>
  </test>
  <test name="scalable" compare="eq">
    <bool>false</bool>
  </test>
  <test name="hinting" compare="eq">
    <bool>true</bool>
  </test>
  <edit name="scalingnotneeded" mode="assign">
    <and>
      <less>
        <name>pixelsizefixupfactor</name>
        <double>1.2</double>
      </less>
      <more>
        <name>pixelsizefixupfactor</name>
        <double>0.8</double>
      </more>
    </and>
  </edit>
</match>
<match target="font">
  <test name="scalingnotneeded" compare="eq">
    <bool>true</bool>
  </test>
  <edit name="pixelsizefixupfactor" mode="assign">
    <double>1.0</double>
  </edit>
</match>
<!--
  If we *are* going to scale, go ahead and do it.
  -->
<match target="font">
  <test name="outline" compare="eq">
    <bool>false</bool>
  </test>
  <test name="pixelsizefixupfactor" compare="not_eq">
    <double>1.0</double>
  </test>
  <edit name="matrix" mode="assign">
    <times>
      <name>matrix</name>
      <matrix>
        <name>pixelsizefixupfactor</name> <double>0</double>
        <double>0</double> <name>pixelsizefixupfactor</name>
       </matrix>
    </times>
  </edit>
  <edit name="size" mode="assign">
    <divide>
      <name>size</name>
      <name>pixelsizefixupfactor</name>
    </divide>
  </edit>
</match>

</fontconfig>
