<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Guess a generic-family for substitution</description>
  <!-- sans-serif -->
  <match target="pattern">
    <test qual="all" name="family" compare="not_eq">
      <string>sans-serif</string>
    </test>
    <test name="family" compare="contains">
      <string>sans</string>
    </test>
    <edit name="family" mode="append_last">
      <string>sans-serif</string>
    </edit>
  </match>
  <!-- serif -->
  <match target="pattern">
    <test qual="all" name="family" compare="not_eq">
      <string>sans-serif</string>
    </test>
    <test qual="all" name="family" compare="not_eq">
      <string>serif</string>
    </test>
    <test name="family" compare="contains">
      <string>serif</string>
    </test>
    <edit name="family" mode="append_last">
      <string>serif</string>
    </edit>
  </match>
  <!-- monospace -->
  <match target="pattern">
    <test qual="all" name="family" compare="not_eq">
      <string>monospace</string>
    </test>
    <test name="family" compare="contains">
      <string>mono</string>
    </test>
    <edit name="family" mode="append_last">
      <string>monospace</string>
    </edit>
  </match>
</fontconfig>
