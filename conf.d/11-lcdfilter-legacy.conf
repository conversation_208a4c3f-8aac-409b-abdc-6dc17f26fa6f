<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Use lcdlegacy as default for LCD filter</description>
<!--  Use lcdlegacy as default for LCD filter -->
  <match target="pattern">
    <!--
      This sort of configuration is available on the major desktop environments
      and we don't have to break it with "assign" unconditionally. however, we
      want to set something for others. So we use "append" here to get this working
      in both cases so that most clients would takes a look at the first place only.
    -->
    <edit mode="append" name="lcdfilter">
      <const>lcdlegacy</const>
    </edit>
  </match>
</fontconfig>
