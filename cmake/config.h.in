/* config.h.in - Configuration header template for CMake */

/* Package information */
#define PACKAGE_NAME "@PACKAGE_NAME@"
#define PACKAGE_TARNAME "@PACKAGE_TARNAME@"
#define PACKAGE_VERSION "@PACKAGE_VERSION@"
#define PACKAGE_STRING "@PACKAGE_STRING@"
#define PACKAGE_BUGREPORT "@PACKAGE_BUGREPORT@"
#define PACKAGE_URL "@PACKAGE_URL@"

/* Version information */
#define VERSION "@PROJECT_VERSION@"
#define CACHE_VERSION "@CACHEVERSION@"

/* Paths */
#define CONFIGDIR "@FC_CONFIGDIR@"
#define FC_CACHEDIR "@FC_CACHEDIR@"
#define FC_TEMPLATEDIR "@FC_TEMPLATEDIR@"
#define FONTCONFIG_PATH "@FC_BASECONFIGDIR@"
#define FC_DEFAULT_FONTS "@FC_DEFAULT_FONTS_ESCAPED@"
#define FC_FONTPATH "@FC_FONTPATH_ESCAPED@"

/* Features */
#cmakedefine ENABLE_LIBXML2 1
#cmakedefine ENABLE_NLS 1
#cmakedefine USE_ICONV 1
#cmakedefine HAVE_PTHREAD 1
#cmakedefine ENABLE_FONTATIONS 1

/* System characteristics */
#cmakedefine WORDS_BIGENDIAN 1
#define EXEEXT "@EXEEXT@"

/* Headers */
#cmakedefine HAVE_DIRENT_H 1
#cmakedefine HAVE_DLFCN_H 1
#cmakedefine HAVE_FCNTL_H 1
#cmakedefine HAVE_INTTYPES_H 1
#cmakedefine HAVE_STDINT_H 1
#cmakedefine HAVE_STDIO_H 1
#cmakedefine HAVE_STDLIB_H 1
#cmakedefine HAVE_STRINGS_H 1
#cmakedefine HAVE_STRING_H 1
#cmakedefine HAVE_UNISTD_H 1
#cmakedefine HAVE_SYS_STATVFS_H 1
#cmakedefine HAVE_SYS_VFS_H 1
#cmakedefine HAVE_SYS_STATFS_H 1
#cmakedefine HAVE_SYS_STAT_H 1
#cmakedefine HAVE_SYS_TYPES_H 1
#cmakedefine HAVE_SYS_PARAM_H 1
#cmakedefine HAVE_SYS_MOUNT_H 1
#cmakedefine HAVE_TIME_H 1
#cmakedefine HAVE_WCHAR_H 1

/* Functions */
#cmakedefine HAVE_LINK 1
#cmakedefine HAVE_MKSTEMP 1
#cmakedefine HAVE_MKOSTEMP 1
#cmakedefine HAVE__MKTEMP_S 1
#cmakedefine HAVE_MKDTEMP 1
#cmakedefine HAVE_GETOPT 1
#cmakedefine HAVE_GETOPT_LONG 1
#cmakedefine HAVE_GETPROGNAME 1
#cmakedefine HAVE_GETEXECNAME 1
#cmakedefine HAVE_RAND 1
#cmakedefine HAVE_RANDOM 1
#cmakedefine HAVE_LRAND48 1
#cmakedefine HAVE_RANDOM_R 1
#cmakedefine HAVE_RAND_R 1
#cmakedefine HAVE_READLINK 1
#cmakedefine HAVE_FSTATVFS 1
#cmakedefine HAVE_FSTATFS 1
#cmakedefine HAVE_LSTAT 1
#cmakedefine HAVE_STRERROR 1
#cmakedefine HAVE_STRERROR_R 1
#cmakedefine HAVE_MMAP 1
#cmakedefine HAVE_VPRINTF 1
#cmakedefine HAVE_VSNPRINTF 1
#cmakedefine HAVE_VSPRINTF 1
#cmakedefine HAVE_GETPAGESIZE 1
#cmakedefine HAVE_GETPID 1
#cmakedefine HAVE_DCGETTEXT 1
#cmakedefine HAVE_GETTEXT 1
#cmakedefine HAVE_LOCALTIME_R 1

/* FreeType functions */
#cmakedefine HAVE_FT_GET_BDF_PROPERTY 1
#cmakedefine HAVE_FT_GET_PS_FONT_INFO 1
#cmakedefine HAVE_FT_HAS_PS_GLYPH_NAMES 1
#cmakedefine HAVE_FT_GET_X11_FONT_FORMAT 1
#cmakedefine HAVE_FT_DONE_MM_VAR 1

/* Header symbols */
#cmakedefine HAVE_POSIX_FADVISE 1

/* Struct members */
#cmakedefine HAVE_STRUCT_STATVFS_F_BASETYPE 1
#cmakedefine HAVE_STRUCT_STATVFS_F_FSTYPENAME 1
#cmakedefine HAVE_STRUCT_STATFS_F_FLAGS 1
#cmakedefine HAVE_STRUCT_STATFS_F_FSTYPENAME 1
#cmakedefine HAVE_STRUCT_STAT_ST_MTIM 1
#cmakedefine HAVE_STRUCT_DIRENT_D_TYPE 1

/* Type sizes and alignments */
#define SIZEOF_VOID_P @SIZEOF_VOID_P@
#define ALIGNOF_VOID_P @ALIGNOF_VOID_P@
#define ALIGNOF_DOUBLE @ALIGNOF_DOUBLE@

/* Compiler features */
#define FLEXIBLE_ARRAY_MEMBER @FLEXIBLE_ARRAY_MEMBER@
#cmakedefine HAVE_PTHREAD_PRIO_INHERIT 1
#cmakedefine HAVE_STDATOMIC_PRIMITIVES 1
#cmakedefine HAVE_INTEL_ATOMIC_PRIMITIVES 1
#cmakedefine HAVE_SOLARIS_ATOMIC_OPS 1

/* FreeType features */
#cmakedefine FREETYPE_PCF_LONG_FAMILY_NAMES 1

/* gperf configuration */
#define FC_GPERF_SIZE_T @FC_GPERF_SIZE_T@

/* Other defines */
#cmakedefine _GNU_SOURCE 1
#define GETTEXT_PACKAGE "@GETTEXT_PACKAGE@"

#include "config-fixups.h"