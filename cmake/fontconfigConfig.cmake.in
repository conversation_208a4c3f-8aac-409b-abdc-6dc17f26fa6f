@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# Find required dependencies
find_dependency(PkgConfig REQUIRED)
pkg_check_modules(FREETYPE2 REQUIRED freetype2)

# Include the targets file
include("${CMAKE_CURRENT_LIST_DIR}/fontconfigTargets.cmake")

# Set variables for compatibility
set(FONTCONFIG_FOUND TRUE)
set(FONTCONFIG_LIBRARIES fontconfig::fontconfig)
set(FONTCONFIG_INCLUDE_DIRS "@CMAKE_INSTALL_FULL_INCLUDEDIR@")

check_required_components(fontconfig)