# FIXME: fontconfig should probably get its own image
# In the meantime, the latest GStreamer image tag can be found here:
# https://gitlab.freedesktop.org/gstreamer/gstreamer/-/blob/main/.gitlab-image-tags.yml#L18
.build meson windows:
  image: "registry.freedesktop.org/gstreamer/gstreamer/amd64/windows:2024-11-12.0-main"
  stage: "distro"
  tags:
    - "docker"
    - "windows"
    - "2022"
  variables:
    # Make sure any failure in PowerShell scripts is fatal
    ErrorActionPreference: "Stop"
    WarningPreference: "Stop"
    # Uncomment the following key if need to pass custom args, as well with the
    # $env:MESON_ARGS line in the `script:` blocks
    # MESON_ARGS: >-
    #   -Dfoo=enabled
    #   -Dbar=disabled
  before_script:
    # Make sure meson is up to date, so we don't need to rebuild the image with each release
    - pip3 install -U meson>=1.6.1 certifi tomli requests
    # Test Rust availability. As of 11/25/2024, according to
    # https://gitlab.freedesktop.org/gstreamer/gstreamer/-/blob/main/ci/docker/windows/Dockerfile
    # Rust 1.82 is already installed.
    - rustc --version
  script:
    # For some reason, options are separated by newline instead of space, so we
    # have to replace them first.
    # - $env:MESON_ARGS = $env:MESON_ARGS.replace("`n"," ")
    # Gitlab executes PowerShell in docker, but VsDevCmd.bat is a batch script.
    # Environment variables substitutions is done by PowerShell before calling
    # cmd.exe, that's why we use $env:FOO instead of %FOO%
    - cmd.exe /C "C:\BuildTools\Common7\Tools\VsDevCmd.bat -host_arch=amd64 -arch=$env:ARCH &&
      SET CERT_PATH=$(python -m certifi) &&
      SET SSL_CERT_FILE=$(python -m certifi) &&
      SET REQUESTS_CA_BUNDLE=$(python -m certifi) &&
      meson setup -Dxml-backend=expat $env:MESON_ARGS build &&
      meson compile --verbose -C build &&
      meson test -C build"
  artifacts:
    name: fontconfig-$CI_COMMIT_SHA-$CI_JOB_ID
    when: always
    paths:
      - build-*/meson-logs/*txt
      - prefix-*


meson vs2019 x86:
  extends: ".build meson windows"
  variables:
    ARCH: "x86"
    MESON_ARGS: "-Dfontations=disabled"

# A Windows Fontations build would currently require
# libclang and llvm installation which is a several hundred megabyte download.
# Skip that configuration for now.

.meson macos test:
  stage: "distro"
  # See https://gitlab.freedesktop.org/gstreamer/gstreamer/-/blob/main/.gitlab-ci.yml
  # As of 2024-11-21, this is a Mac OS 15 Sequia image on Apple silicon.
  image: "registry.freedesktop.org/gstreamer/gstreamer/macos-arm64/15-sequoia:2024-10-28.0"
  tags:
    - gst-mac-arm
  artifacts:
    name: "${CI_JOB_NAME}_${CI_COMMIT_SHA}"
    expire_in: "5 days"
    when: "always"
    paths:
      - "build/meson-logs/*txt"
  before_script:
    - pip3 install --upgrade pip
    # Make sure meson is up to date
    - pip3 install -U 'meson>=1.6.1'
    # For Python < 3.11 we need tomli for Rust build support
    - pip3 install -U tomli
    # Need to install certificates for python
    - pip3 install --upgrade certifi
    # Get ninja
    - pip3 install --upgrade ninja
    # Install rust
    - curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    - . "$HOME/.cargo/env"
    # Test Rust availability.
    - rustup update
    - cargo install bindgen-cli
    - rustc --version
    - bindgen --version
  script:
    - CERT_PATH=$(python3 -m certifi) && export SSL_CERT_FILE=${CERT_PATH} && export REQUESTS_CA_BUNDLE=${CERT_PATH} && meson setup -Diconv=enabled $MESON_FONTATIONS_ARG build
    - meson compile --verbose -C build
    - meson test -C build

meson macos test:
  extends: ".meson macos test"
  variables:
    MESON_FONTATIONS_ARG: "-Dfontations=disabled"

meson macos test fontations:
  extends: ".meson macos test"
  variables:
    MESON_FONTATIONS_ARG: "-Dfontations=enabled"

# msys infrastructure is a bit broken, disable for now
meson msys2:
  extends: ".build meson windows"
  when: "manual"
  allow_failure: true
  script:
    # For some reason, options are separated by newline instead of space, so we
    # have to replace them first.
    # - $env:MESON_ARGS = $env:MESON_ARGS.replace("`n"," ")

    - $env:PATH += ";C:\msys64\usr\bin;C:\msys64\mingw64/bin;C:\msys64\mingw32/bin"
    # XXX: Copied from https://gitlab.freedesktop.org/gstreamer/gst-ci/blob/master/gitlab/ci_template.yml#L487
    # For some reason docker build hangs if this is included in the image, needs more troubleshooting
    - C:\msys64\usr\bin\bash -c "pacman-key --init && pacman-key --populate msys2 && pacman-key --refresh-keys || true"
    - C:\msys64\usr\bin\bash -c "pacman -Syuu --noconfirm"
    - C:\msys64\usr\bin\bash -c "pacman -Sy --noconfirm --needed mingw-w64-x86_64-toolchain ninja"
    - C:\msys64\usr\bin\bash -c "meson build $env:MESON_ARGS &&
      ninja -C build &&
      ninja -C build test"
