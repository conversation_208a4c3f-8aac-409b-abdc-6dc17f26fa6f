{"InstallScripts": ["/Users/<USER>/work/fontconfig/build/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fontconfig/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-case/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-lang/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/src/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-cache/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-cat/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-conflist/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-list/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-match/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-pattern/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-query/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-scan/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/fc-validate/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/test/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/conf.d/cmake_install.cmake", "/Users/<USER>/work/fontconfig/build/its/cmake_install.cmake"], "Parallel": false}