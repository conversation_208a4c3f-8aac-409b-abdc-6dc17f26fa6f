
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Darwin - 21.6.0 - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/clang 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/work/fontconfig/build/CMakeFiles/4.0.2/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting C compiler apple sysroot: "/usr/bin/clang" "-E" "apple-sdk.c"
        # 1 "apple-sdk.c"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 370 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.c" 2
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 242 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 165 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 166 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 167 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 243 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.c" 2
        
        
      Found apple sysroot: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xH5Qui"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xH5Qui"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xH5Qui'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_73a9c
        [1/2] /usr/bin/clang   -v -Wl,-v -MD -MT CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xH5Qui -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names -v -Wl,-v CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -o cmTC_73a9c   && :
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_73a9c -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld  PROJECT:ld64-820.1
        BUILD 20:07:01 Nov  7 2022
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        Library search paths:
        	/usr/local/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "x86_64"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/usr/local/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xH5Qui']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/ninja -v cmTC_73a9c]
        ignore line: [[1/2] /usr/bin/clang   -v -Wl -v -MD -MT CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xH5Qui -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [[2/2] : && /usr/bin/clang  -Wl -search_paths_first -Wl -headerpad_max_install_names -v -Wl -v CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -o cmTC_73a9c   && :]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_73a9c -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [x86_64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [12.0.0] ==> ignore
          arg [13.1] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_73a9c] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_73a9c.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld  PROJECT:ld64-820.1
      BUILD 20:07:01 Nov  7 2022
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 14.0.0, (clang-1400.0.29.202) (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 14.0.0 (tapi-1400.0.11)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindIntl.cmake:113 (check_c_source_compiles)"
      - "CMakeLists.txt:110 (find_package)"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-m3Eol0"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-m3Eol0"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "Intl_IS_BUILT_IN"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-m3Eol0'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_bec6f
        [1/2] /usr/bin/clang -DIntl_IS_BUILT_IN  -std=gnu11 -MD -MT CMakeFiles/cmTC_bec6f.dir/src.c.o -MF CMakeFiles/cmTC_bec6f.dir/src.c.o.d -o CMakeFiles/cmTC_bec6f.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-m3Eol0/src.c
        FAILED: CMakeFiles/cmTC_bec6f.dir/src.c.o 
        /usr/bin/clang -DIntl_IS_BUILT_IN  -std=gnu11 -MD -MT CMakeFiles/cmTC_bec6f.dir/src.c.o -MF CMakeFiles/cmTC_bec6f.dir/src.c.o.d -o CMakeFiles/cmTC_bec6f.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-m3Eol0/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-m3Eol0/src.c:1:10: fatal error: 'libintl.h' file not found
        #include <libintl.h>
                 ^~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:132 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-SC1p52"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-SC1p52"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-SC1p52'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4ce24
        [1/2] /usr/bin/clang -DCMAKE_HAVE_LIBC_PTHREAD  -std=gnu11 -MD -MT CMakeFiles/cmTC_4ce24.dir/src.c.o -MF CMakeFiles/cmTC_4ce24.dir/src.c.o.d -o CMakeFiles/cmTC_4ce24.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-SC1p52/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4ce24.dir/src.c.o -o cmTC_4ce24   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for dirent.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-zOLGh5"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-zOLGh5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DIRENT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-zOLGh5'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_67d89
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_67d89.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_67d89.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_67d89.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-zOLGh5/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_67d89.dir/CheckIncludeFile.c.o -o cmTC_67d89   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for dlfcn.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-mHfsIZ"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-mHfsIZ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DLFCN_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-mHfsIZ'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_e62a3
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_e62a3.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_e62a3.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_e62a3.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-mHfsIZ/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e62a3.dir/CheckIncludeFile.c.o -o cmTC_e62a3   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for fcntl.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bsG2AB"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bsG2AB"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FCNTL_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bsG2AB'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_bfac6
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_bfac6.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_bfac6.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_bfac6.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bsG2AB/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_bfac6.dir/CheckIncludeFile.c.o -o cmTC_bfac6   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for inttypes.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TehQeZ"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TehQeZ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INTTYPES_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TehQeZ'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5f698
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_5f698.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_5f698.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_5f698.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TehQeZ/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5f698.dir/CheckIncludeFile.c.o -o cmTC_5f698   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for stdint.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-33iQ00"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-33iQ00"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDINT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-33iQ00'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_e3274
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_e3274.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_e3274.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_e3274.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-33iQ00/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e3274.dir/CheckIncludeFile.c.o -o cmTC_e3274   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for stdio.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F94MDz"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F94MDz"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDIO_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F94MDz'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_8e82f
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_8e82f.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_8e82f.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_8e82f.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F94MDz/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_8e82f.dir/CheckIncludeFile.c.o -o cmTC_8e82f   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for stdlib.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Tklu8N"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Tklu8N"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDLIB_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Tklu8N'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_64e3a
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_64e3a.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_64e3a.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_64e3a.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Tklu8N/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_64e3a.dir/CheckIncludeFile.c.o -o cmTC_64e3a   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for strings.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-MvNx6E"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-MvNx6E"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRINGS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-MvNx6E'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_23dc6
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_23dc6.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_23dc6.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_23dc6.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-MvNx6E/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_23dc6.dir/CheckIncludeFile.c.o -o cmTC_23dc6   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for string.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nCy86D"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nCy86D"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRING_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nCy86D'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_2ced9
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_2ced9.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_2ced9.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_2ced9.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nCy86D/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_2ced9.dir/CheckIncludeFile.c.o -o cmTC_2ced9   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for unistd.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gQ5Jgu"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gQ5Jgu"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UNISTD_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gQ5Jgu'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_cbcdb
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_cbcdb.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_cbcdb.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_cbcdb.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gQ5Jgu/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_cbcdb.dir/CheckIncludeFile.c.o -o cmTC_cbcdb   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for sys/statvfs.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-8UJC3T"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-8UJC3T"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_STATVFS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-8UJC3T'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6161a
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_6161a.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_6161a.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_6161a.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-8UJC3T/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_6161a.dir/CheckIncludeFile.c.o -o cmTC_6161a   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for sys/vfs.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-0QJJye"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-0QJJye"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_VFS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-0QJJye'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4b50f
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_4b50f.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_4b50f.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_4b50f.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-0QJJye/CheckIncludeFile.c
        FAILED: CMakeFiles/cmTC_4b50f.dir/CheckIncludeFile.c.o 
        /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_4b50f.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_4b50f.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_4b50f.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-0QJJye/CheckIncludeFile.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-0QJJye/CheckIncludeFile.c:1:10: fatal error: 'sys/vfs.h' file not found
        #include <sys/vfs.h>
                 ^~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for sys/statfs.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pwBZ3R"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pwBZ3R"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_STATFS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pwBZ3R'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_801a0
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_801a0.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_801a0.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_801a0.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pwBZ3R/CheckIncludeFile.c
        FAILED: CMakeFiles/cmTC_801a0.dir/CheckIncludeFile.c.o 
        /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_801a0.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_801a0.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_801a0.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pwBZ3R/CheckIncludeFile.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pwBZ3R/CheckIncludeFile.c:1:10: fatal error: 'sys/statfs.h' file not found
        #include <sys/statfs.h>
                 ^~~~~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for sys/stat.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-h0lezF"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-h0lezF"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_STAT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-h0lezF'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4f5ad
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_4f5ad.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_4f5ad.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_4f5ad.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-h0lezF/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4f5ad.dir/CheckIncludeFile.c.o -o cmTC_4f5ad   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for sys/types.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oRSCMY"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oRSCMY"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_TYPES_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oRSCMY'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_22e18
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_22e18.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_22e18.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_22e18.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oRSCMY/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_22e18.dir/CheckIncludeFile.c.o -o cmTC_22e18   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for sys/param.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-VFh5dq"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-VFh5dq"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_PARAM_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-VFh5dq'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_b09ec
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_b09ec.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_b09ec.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_b09ec.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-VFh5dq/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_b09ec.dir/CheckIncludeFile.c.o -o cmTC_b09ec   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for sys/mount.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FSABF4"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FSABF4"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_MOUNT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FSABF4'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_769ac
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_769ac.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_769ac.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_769ac.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FSABF4/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_769ac.dir/CheckIncludeFile.c.o -o cmTC_769ac   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for time.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ukFjys"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ukFjys"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_TIME_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ukFjys'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c4aaa
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_c4aaa.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_c4aaa.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_c4aaa.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ukFjys/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_c4aaa.dir/CheckIncludeFile.c.o -o cmTC_c4aaa   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for wchar.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Xy9sPo"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Xy9sPo"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_WCHAR_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Xy9sPo'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_139f5
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_139f5.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_139f5.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_139f5.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Xy9sPo/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_139f5.dir/CheckIncludeFile.c.o -o cmTC_139f5   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for link"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-a5SqVr"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-a5SqVr"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LINK"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-a5SqVr'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6aec5
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=link -std=gnu11 -MD -MT CMakeFiles/cmTC_6aec5.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_6aec5.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_6aec5.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-a5SqVr/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=link -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_6aec5.dir/CheckFunctionExists.c.o -o cmTC_6aec5   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for mkstemp"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-exmYN4"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-exmYN4"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MKSTEMP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-exmYN4'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_b4524
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mkstemp -std=gnu11 -MD -MT CMakeFiles/cmTC_b4524.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_b4524.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_b4524.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-exmYN4/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mkstemp -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_b4524.dir/CheckFunctionExists.c.o -o cmTC_b4524   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for mkostemp"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3h69UH"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3h69UH"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MKOSTEMP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3h69UH'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5d1a4
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mkostemp -std=gnu11 -MD -MT CMakeFiles/cmTC_5d1a4.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_5d1a4.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_5d1a4.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3h69UH/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mkostemp -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5d1a4.dir/CheckFunctionExists.c.o -o cmTC_5d1a4   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for _mktemp_s"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-A81CLL"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-A81CLL"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE__MKTEMP_S"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-A81CLL'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5b58c
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=_mktemp_s -std=gnu11 -MD -MT CMakeFiles/cmTC_5b58c.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_5b58c.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_5b58c.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-A81CLL/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=_mktemp_s -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5b58c.dir/CheckFunctionExists.c.o -o cmTC_5b58c   && :
        FAILED: cmTC_5b58c 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=_mktemp_s -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5b58c.dir/CheckFunctionExists.c.o -o cmTC_5b58c   && :
        Undefined symbols for architecture x86_64:
          "__mktemp_s", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for mkdtemp"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-W8rFqL"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-W8rFqL"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MKDTEMP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-W8rFqL'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_35636
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mkdtemp -std=gnu11 -MD -MT CMakeFiles/cmTC_35636.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_35636.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_35636.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-W8rFqL/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mkdtemp -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_35636.dir/CheckFunctionExists.c.o -o cmTC_35636   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for getopt"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9w1VVL"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9w1VVL"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETOPT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9w1VVL'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_8d15a
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getopt -std=gnu11 -MD -MT CMakeFiles/cmTC_8d15a.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_8d15a.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_8d15a.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9w1VVL/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getopt -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_8d15a.dir/CheckFunctionExists.c.o -o cmTC_8d15a   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for getopt_long"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Ad0XUs"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Ad0XUs"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETOPT_LONG"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Ad0XUs'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_e0a53
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getopt_long -std=gnu11 -MD -MT CMakeFiles/cmTC_e0a53.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_e0a53.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_e0a53.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Ad0XUs/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getopt_long -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e0a53.dir/CheckFunctionExists.c.o -o cmTC_e0a53   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for getprogname"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-adgZer"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-adgZer"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPROGNAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-adgZer'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_9d9bb
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getprogname -std=gnu11 -MD -MT CMakeFiles/cmTC_9d9bb.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_9d9bb.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_9d9bb.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-adgZer/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getprogname -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_9d9bb.dir/CheckFunctionExists.c.o -o cmTC_9d9bb   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for getexecname"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ExJulJ"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ExJulJ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETEXECNAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ExJulJ'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_424a1
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getexecname -std=gnu11 -MD -MT CMakeFiles/cmTC_424a1.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_424a1.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_424a1.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ExJulJ/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getexecname -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_424a1.dir/CheckFunctionExists.c.o -o cmTC_424a1   && :
        FAILED: cmTC_424a1 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getexecname -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_424a1.dir/CheckFunctionExists.c.o -o cmTC_424a1   && :
        Undefined symbols for architecture x86_64:
          "_getexecname", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for rand"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-IjE06C"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-IjE06C"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RAND"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-IjE06C'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c1729
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=rand -std=gnu11 -MD -MT CMakeFiles/cmTC_c1729.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_c1729.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_c1729.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-IjE06C/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=rand -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_c1729.dir/CheckFunctionExists.c.o -o cmTC_c1729   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for random"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-csZUrX"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-csZUrX"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RANDOM"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-csZUrX'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_724d1
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=random -std=gnu11 -MD -MT CMakeFiles/cmTC_724d1.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_724d1.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_724d1.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-csZUrX/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=random -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_724d1.dir/CheckFunctionExists.c.o -o cmTC_724d1   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for lrand48"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qyuUX9"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qyuUX9"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LRAND48"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qyuUX9'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_e8e7b
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=lrand48 -std=gnu11 -MD -MT CMakeFiles/cmTC_e8e7b.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_e8e7b.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_e8e7b.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qyuUX9/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=lrand48 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e8e7b.dir/CheckFunctionExists.c.o -o cmTC_e8e7b   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for random_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-QpadOy"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-QpadOy"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RANDOM_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-QpadOy'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_eedbf
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=random_r -std=gnu11 -MD -MT CMakeFiles/cmTC_eedbf.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_eedbf.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_eedbf.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-QpadOy/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=random_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_eedbf.dir/CheckFunctionExists.c.o -o cmTC_eedbf   && :
        FAILED: cmTC_eedbf 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=random_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_eedbf.dir/CheckFunctionExists.c.o -o cmTC_eedbf   && :
        Undefined symbols for architecture x86_64:
          "_random_r", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for rand_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-2BaOwd"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-2BaOwd"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RAND_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-2BaOwd'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_90202
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=rand_r -std=gnu11 -MD -MT CMakeFiles/cmTC_90202.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_90202.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_90202.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-2BaOwd/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=rand_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_90202.dir/CheckFunctionExists.c.o -o cmTC_90202   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for readlink"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-XZYuam"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-XZYuam"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_READLINK"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-XZYuam'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_eff0a
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=readlink -std=gnu11 -MD -MT CMakeFiles/cmTC_eff0a.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_eff0a.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_eff0a.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-XZYuam/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=readlink -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_eff0a.dir/CheckFunctionExists.c.o -o cmTC_eff0a   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for fstatvfs"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-8kxxDu"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-8kxxDu"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FSTATVFS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-8kxxDu'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_8ea87
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=fstatvfs -std=gnu11 -MD -MT CMakeFiles/cmTC_8ea87.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_8ea87.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_8ea87.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-8kxxDu/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=fstatvfs -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_8ea87.dir/CheckFunctionExists.c.o -o cmTC_8ea87   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for fstatfs"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-1YvneD"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-1YvneD"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FSTATFS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-1YvneD'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5ac6c
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=fstatfs -std=gnu11 -MD -MT CMakeFiles/cmTC_5ac6c.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_5ac6c.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_5ac6c.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-1YvneD/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=fstatfs -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5ac6c.dir/CheckFunctionExists.c.o -o cmTC_5ac6c   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for lstat"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wzCXF5"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wzCXF5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LSTAT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wzCXF5'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_febcc
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=lstat -std=gnu11 -MD -MT CMakeFiles/cmTC_febcc.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_febcc.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_febcc.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wzCXF5/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=lstat -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_febcc.dir/CheckFunctionExists.c.o -o cmTC_febcc   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for strerror"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iBNbpm"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iBNbpm"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRERROR"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iBNbpm'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_3f2f2
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=strerror -std=gnu11 -MD -MT CMakeFiles/cmTC_3f2f2.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_3f2f2.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_3f2f2.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iBNbpm/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iBNbpm/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'strerror' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS strerror
                                      ^
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iBNbpm/CheckFunctionExists.c:7:3: note: 'strerror' is a builtin with type 'char *(int)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS strerror
                                      ^
        1 warning generated.
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=strerror -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_3f2f2.dir/CheckFunctionExists.c.o -o cmTC_3f2f2   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for strerror_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-w9ONg0"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-w9ONg0"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRERROR_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-w9ONg0'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_a5e1b
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=strerror_r -std=gnu11 -MD -MT CMakeFiles/cmTC_a5e1b.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_a5e1b.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_a5e1b.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-w9ONg0/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=strerror_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_a5e1b.dir/CheckFunctionExists.c.o -o cmTC_a5e1b   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for mmap"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ASca3E"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ASca3E"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MMAP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ASca3E'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_2a4c7
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mmap -std=gnu11 -MD -MT CMakeFiles/cmTC_2a4c7.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_2a4c7.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_2a4c7.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ASca3E/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mmap -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_2a4c7.dir/CheckFunctionExists.c.o -o cmTC_2a4c7   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for vprintf"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-cNmp1G"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-cNmp1G"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_VPRINTF"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-cNmp1G'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_937b8
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=vprintf -std=gnu11 -MD -MT CMakeFiles/cmTC_937b8.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_937b8.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_937b8.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-cNmp1G/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-cNmp1G/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'vprintf' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vprintf
                                      ^
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-cNmp1G/CheckFunctionExists.c:7:3: note: 'vprintf' is a builtin with type 'int (const char *, struct __va_list_tag *)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vprintf
                                      ^
        1 warning generated.
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=vprintf -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_937b8.dir/CheckFunctionExists.c.o -o cmTC_937b8   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for vsnprintf"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3vYW0A"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3vYW0A"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_VSNPRINTF"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3vYW0A'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_cf211
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=vsnprintf -std=gnu11 -MD -MT CMakeFiles/cmTC_cf211.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_cf211.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_cf211.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3vYW0A/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3vYW0A/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'vsnprintf' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsnprintf
                                      ^
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3vYW0A/CheckFunctionExists.c:7:3: note: 'vsnprintf' is a builtin with type 'int (char *, unsigned long, const char *, struct __va_list_tag *)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsnprintf
                                      ^
        1 warning generated.
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=vsnprintf -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_cf211.dir/CheckFunctionExists.c.o -o cmTC_cf211   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for vsprintf"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6iljvg"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6iljvg"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_VSPRINTF"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6iljvg'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_fc88a
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=vsprintf -std=gnu11 -MD -MT CMakeFiles/cmTC_fc88a.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_fc88a.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_fc88a.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6iljvg/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6iljvg/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'vsprintf' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsprintf
                                      ^
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6iljvg/CheckFunctionExists.c:7:3: note: 'vsprintf' is a builtin with type 'int (char *, const char *, struct __va_list_tag *)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsprintf
                                      ^
        1 warning generated.
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=vsprintf -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_fc88a.dir/CheckFunctionExists.c.o -o cmTC_fc88a   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for getpagesize"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-HqHfSh"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-HqHfSh"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPAGESIZE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-HqHfSh'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_19a08
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getpagesize -std=gnu11 -MD -MT CMakeFiles/cmTC_19a08.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_19a08.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_19a08.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-HqHfSh/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getpagesize -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_19a08.dir/CheckFunctionExists.c.o -o cmTC_19a08   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for getpid"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FpaKe0"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FpaKe0"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPID"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FpaKe0'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_1a125
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getpid -std=gnu11 -MD -MT CMakeFiles/cmTC_1a125.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_1a125.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_1a125.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FpaKe0/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getpid -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_1a125.dir/CheckFunctionExists.c.o -o cmTC_1a125   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for dcgettext"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-P7YQZ6"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-P7YQZ6"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DCGETTEXT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-P7YQZ6'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_bfa02
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=dcgettext -std=gnu11 -MD -MT CMakeFiles/cmTC_bfa02.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_bfa02.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_bfa02.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-P7YQZ6/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=dcgettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_bfa02.dir/CheckFunctionExists.c.o -o cmTC_bfa02   && :
        FAILED: cmTC_bfa02 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=dcgettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_bfa02.dir/CheckFunctionExists.c.o -o cmTC_bfa02   && :
        Undefined symbols for architecture x86_64:
          "_dcgettext", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for gettext"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ehPZIy"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ehPZIy"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETTEXT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ehPZIy'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c8f38
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=gettext -std=gnu11 -MD -MT CMakeFiles/cmTC_c8f38.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_c8f38.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_c8f38.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ehPZIy/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=gettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_c8f38.dir/CheckFunctionExists.c.o -o cmTC_c8f38   && :
        FAILED: cmTC_c8f38 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=gettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_c8f38.dir/CheckFunctionExists.c.o -o cmTC_c8f38   && :
        Undefined symbols for architecture x86_64:
          "_gettext", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for localtime_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-rd3wMr"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-rd3wMr"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LOCALTIME_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-rd3wMr'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_d7ce6
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=localtime_r -std=gnu11 -MD -MT CMakeFiles/cmTC_d7ce6.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_d7ce6.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_d7ce6.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-rd3wMr/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=localtime_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_d7ce6.dir/CheckFunctionExists.c.o -o cmTC_d7ce6   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:76 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for FT_Get_BDF_Property"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-XOFwCe"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-XOFwCe"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_GET_BDF_PROPERTY"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-XOFwCe'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_ecc11
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Get_BDF_Property -std=gnu11 -MD -MT CMakeFiles/cmTC_ecc11.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_ecc11.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_ecc11.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-XOFwCe/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Get_BDF_Property -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_ecc11.dir/CheckFunctionExists.c.o -o cmTC_ecc11  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:77 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for FT_Get_PS_Font_Info"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-480kG2"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-480kG2"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_GET_PS_FONT_INFO"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-480kG2'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_45d40
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Get_PS_Font_Info -std=gnu11 -MD -MT CMakeFiles/cmTC_45d40.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_45d40.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_45d40.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-480kG2/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Get_PS_Font_Info -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_45d40.dir/CheckFunctionExists.c.o -o cmTC_45d40  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:78 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for FT_Has_PS_Glyph_Names"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xVc14O"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xVc14O"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_HAS_PS_GLYPH_NAMES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xVc14O'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_9d3af
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Has_PS_Glyph_Names -std=gnu11 -MD -MT CMakeFiles/cmTC_9d3af.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_9d3af.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_9d3af.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xVc14O/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Has_PS_Glyph_Names -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_9d3af.dir/CheckFunctionExists.c.o -o cmTC_9d3af  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:79 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for FT_Get_X11_Font_Format"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-eQw3DL"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-eQw3DL"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_GET_X11_FONT_FORMAT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-eQw3DL'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_16a66
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Get_X11_Font_Format -std=gnu11 -MD -MT CMakeFiles/cmTC_16a66.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_16a66.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_16a66.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-eQw3DL/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Get_X11_Font_Format -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_16a66.dir/CheckFunctionExists.c.o -o cmTC_16a66  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:80 (check_function_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for FT_Done_MM_Var"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EF08xI"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EF08xI"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_DONE_MM_VAR"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EF08xI'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_fd030
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Done_MM_Var -std=gnu11 -MD -MT CMakeFiles/cmTC_fd030.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_fd030.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_fd030.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EF08xI/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Done_MM_Var -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_fd030.dir/CheckFunctionExists.c.o -o cmTC_fd030  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "cmake/ConfigureChecks.cmake:87 (check_symbol_exists)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for posix_fadvise"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TBl1d3"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TBl1d3"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_POSIX_FADVISE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TBl1d3'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_09fa2
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_09fa2.dir/CheckSymbolExists.c.o -MF CMakeFiles/cmTC_09fa2.dir/CheckSymbolExists.c.o.d -o CMakeFiles/cmTC_09fa2.dir/CheckSymbolExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TBl1d3/CheckSymbolExists.c
        FAILED: CMakeFiles/cmTC_09fa2.dir/CheckSymbolExists.c.o 
        /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_09fa2.dir/CheckSymbolExists.c.o -MF CMakeFiles/cmTC_09fa2.dir/CheckSymbolExists.c.o.d -o CMakeFiles/cmTC_09fa2.dir/CheckSymbolExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TBl1d3/CheckSymbolExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-TBl1d3/CheckSymbolExists.c:8:19: error: use of undeclared identifier 'posix_fadvise'
          return ((int*)(&posix_fadvise))[argc];
                          ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:90 (check_struct_has_member)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATVFS_F_BASETYPE"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9l5Wgm"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9l5Wgm"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATVFS_F_BASETYPE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9l5Wgm'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_a4dd5
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STATVFS_F_BASETYPE  -std=gnu11 -MD -MT CMakeFiles/cmTC_a4dd5.dir/src.c.o -MF CMakeFiles/cmTC_a4dd5.dir/src.c.o.d -o CMakeFiles/cmTC_a4dd5.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9l5Wgm/src.c
        FAILED: CMakeFiles/cmTC_a4dd5.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STATVFS_F_BASETYPE  -std=gnu11 -MD -MT CMakeFiles/cmTC_a4dd5.dir/src.c.o -MF CMakeFiles/cmTC_a4dd5.dir/src.c.o.d -o CMakeFiles/cmTC_a4dd5.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9l5Wgm/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9l5Wgm/src.c:6:39: error: no member named 'f_basetype' in 'struct statvfs'
          (void)sizeof(((struct statvfs *)0)->f_basetype);
                       ~~~~~~~~~~~~~~~~~~~~~  ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:91 (check_struct_has_member)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATVFS_F_FSTYPENAME"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-t8CLaP"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-t8CLaP"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATVFS_F_FSTYPENAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-t8CLaP'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_1c747
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STATVFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_1c747.dir/src.c.o -MF CMakeFiles/cmTC_1c747.dir/src.c.o.d -o CMakeFiles/cmTC_1c747.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-t8CLaP/src.c
        FAILED: CMakeFiles/cmTC_1c747.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STATVFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_1c747.dir/src.c.o -MF CMakeFiles/cmTC_1c747.dir/src.c.o.d -o CMakeFiles/cmTC_1c747.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-t8CLaP/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-t8CLaP/src.c:6:39: error: no member named 'f_fstypename' in 'struct statvfs'
          (void)sizeof(((struct statvfs *)0)->f_fstypename);
                       ~~~~~~~~~~~~~~~~~~~~~  ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:92 (check_struct_has_member)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATFS_F_FLAGS"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6E9Xbn"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6E9Xbn"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATFS_F_FLAGS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6E9Xbn'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_ccf38
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STATFS_F_FLAGS  -std=gnu11 -MD -MT CMakeFiles/cmTC_ccf38.dir/src.c.o -MF CMakeFiles/cmTC_ccf38.dir/src.c.o.d -o CMakeFiles/cmTC_ccf38.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6E9Xbn/src.c
        FAILED: CMakeFiles/cmTC_ccf38.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STATFS_F_FLAGS  -std=gnu11 -MD -MT CMakeFiles/cmTC_ccf38.dir/src.c.o -MF CMakeFiles/cmTC_ccf38.dir/src.c.o.d -o CMakeFiles/cmTC_ccf38.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6E9Xbn/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6E9Xbn/src.c:2:10: fatal error: 'sys/vfs.h' file not found
        #include <sys/vfs.h>
                 ^~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:93 (check_struct_has_member)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATFS_F_FSTYPENAME"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-UfvyO1"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-UfvyO1"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATFS_F_FSTYPENAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-UfvyO1'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_01379
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STATFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_01379.dir/src.c.o -MF CMakeFiles/cmTC_01379.dir/src.c.o.d -o CMakeFiles/cmTC_01379.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-UfvyO1/src.c
        FAILED: CMakeFiles/cmTC_01379.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STATFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_01379.dir/src.c.o -MF CMakeFiles/cmTC_01379.dir/src.c.o.d -o CMakeFiles/cmTC_01379.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-UfvyO1/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-UfvyO1/src.c:2:10: fatal error: 'sys/vfs.h' file not found
        #include <sys/vfs.h>
                 ^~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:94 (check_struct_has_member)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STAT_ST_MTIM"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-yMJulF"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-yMJulF"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STAT_ST_MTIM"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-yMJulF'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_8e9e1
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STAT_ST_MTIM  -std=gnu11 -MD -MT CMakeFiles/cmTC_8e9e1.dir/src.c.o -MF CMakeFiles/cmTC_8e9e1.dir/src.c.o.d -o CMakeFiles/cmTC_8e9e1.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-yMJulF/src.c
        FAILED: CMakeFiles/cmTC_8e9e1.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STAT_ST_MTIM  -std=gnu11 -MD -MT CMakeFiles/cmTC_8e9e1.dir/src.c.o -MF CMakeFiles/cmTC_8e9e1.dir/src.c.o.d -o CMakeFiles/cmTC_8e9e1.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-yMJulF/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-yMJulF/src.c:6:36: error: no member named 'st_mtim' in 'struct stat'
          (void)sizeof(((struct stat *)0)->st_mtim);
                       ~~~~~~~~~~~~~~~~~~  ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:95 (check_struct_has_member)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_DIRENT_D_TYPE"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BPdP9m"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BPdP9m"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_DIRENT_D_TYPE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BPdP9m'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_72357
        [1/2] /usr/bin/clang -DHAVE_STRUCT_DIRENT_D_TYPE  -std=gnu11 -MD -MT CMakeFiles/cmTC_72357.dir/src.c.o -MF CMakeFiles/cmTC_72357.dir/src.c.o.d -o CMakeFiles/cmTC_72357.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BPdP9m/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_72357.dir/src.c.o -o cmTC_72357   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:320 (check_include_file)"
      - "cmake/ConfigureChecks.cmake:98 (check_type_size)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Looking for stddef.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ANOFul"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ANOFul"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDDEF_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ANOFul'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_e9e92
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_e9e92.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_e9e92.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_e9e92.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ANOFul/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e9e92.dir/CheckIncludeFile.c.o -o cmTC_e9e92   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:98 (check_type_size)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Check size of void *"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oxSP6e"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oxSP6e"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SIZEOF_VOID_P"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oxSP6e'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4bbab
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_4bbab.dir/SIZEOF_VOID_P.c.o -MF CMakeFiles/cmTC_4bbab.dir/SIZEOF_VOID_P.c.o.d -o CMakeFiles/cmTC_4bbab.dir/SIZEOF_VOID_P.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oxSP6e/SIZEOF_VOID_P.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4bbab.dir/SIZEOF_VOID_P.c.o -o cmTC_4bbab   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:106 (check_c_source_compiles)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_FLEXIBLE_ARRAY_MEMBER"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-2veYYe"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-2veYYe"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FLEXIBLE_ARRAY_MEMBER"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-2veYYe'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_71b62
        [1/2] /usr/bin/clang -DHAVE_FLEXIBLE_ARRAY_MEMBER  -std=gnu11 -MD -MT CMakeFiles/cmTC_71b62.dir/src.c.o -MF CMakeFiles/cmTC_71b62.dir/src.c.o.d -o CMakeFiles/cmTC_71b62.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-2veYYe/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_71b62.dir/src.c.o -o cmTC_71b62   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:122 (check_c_source_compiles)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_PTHREAD_PRIO_INHERIT"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-tW8tno"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-tW8tno"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_PTHREAD_PRIO_INHERIT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-tW8tno'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_bc23a
        [1/2] /usr/bin/clang -DHAVE_PTHREAD_PRIO_INHERIT  -std=gnu11 -MD -MT CMakeFiles/cmTC_bc23a.dir/src.c.o -MF CMakeFiles/cmTC_bc23a.dir/src.c.o.d -o CMakeFiles/cmTC_bc23a.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-tW8tno/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_bc23a.dir/src.c.o -o cmTC_bc23a   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:134 (check_c_source_compiles)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_STDATOMIC_PRIMITIVES"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-dCpQ7o"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-dCpQ7o"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDATOMIC_PRIMITIVES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-dCpQ7o'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_fbe8b
        [1/2] /usr/bin/clang -DHAVE_STDATOMIC_PRIMITIVES  -std=gnu11 -MD -MT CMakeFiles/cmTC_fbe8b.dir/src.c.o -MF CMakeFiles/cmTC_fbe8b.dir/src.c.o.d -o CMakeFiles/cmTC_fbe8b.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-dCpQ7o/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_fbe8b.dir/src.c.o -o cmTC_fbe8b   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:143 (check_c_source_compiles)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_INTEL_ATOMIC_PRIMITIVES"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Wran8C"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Wran8C"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INTEL_ATOMIC_PRIMITIVES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Wran8C'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_75bae
        [1/2] /usr/bin/clang -DHAVE_INTEL_ATOMIC_PRIMITIVES  -std=gnu11 -MD -MT CMakeFiles/cmTC_75bae.dir/src.c.o -MF CMakeFiles/cmTC_75bae.dir/src.c.o.d -o CMakeFiles/cmTC_75bae.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Wran8C/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_75bae.dir/src.c.o -o cmTC_75bae   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:151 (check_c_source_compiles)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test HAVE_SOLARIS_ATOMIC_OPS"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wHY34v"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wHY34v"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SOLARIS_ATOMIC_OPS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wHY34v'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_f1f53
        [1/2] /usr/bin/clang -DHAVE_SOLARIS_ATOMIC_OPS  -std=gnu11 -MD -MT CMakeFiles/cmTC_f1f53.dir/src.c.o -MF CMakeFiles/cmTC_f1f53.dir/src.c.o.d -o CMakeFiles/cmTC_f1f53.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wHY34v/src.c
        FAILED: CMakeFiles/cmTC_f1f53.dir/src.c.o 
        /usr/bin/clang -DHAVE_SOLARIS_ATOMIC_OPS  -std=gnu11 -MD -MT CMakeFiles/cmTC_f1f53.dir/src.c.o -MF CMakeFiles/cmTC_f1f53.dir/src.c.o.d -o CMakeFiles/cmTC_f1f53.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wHY34v/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-wHY34v/src.c:2:10: fatal error: 'atomic.h' file not found
        #include <atomic.h>
                 ^~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:165 (check_c_source_compiles)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Performing Test FREETYPE_PCF_LONG_FAMILY_NAMES"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ePTKxM"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ePTKxM"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "FREETYPE_PCF_LONG_FAMILY_NAMES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ePTKxM'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_33635
        [1/2] /usr/bin/clang -DFREETYPE_PCF_LONG_FAMILY_NAMES -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -std=gnu11 -MD -MT CMakeFiles/cmTC_33635.dir/src.c.o -MF CMakeFiles/cmTC_33635.dir/src.c.o.d -o CMakeFiles/cmTC_33635.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ePTKxM/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_33635.dir/src.c.o -o cmTC_33635  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:203 (check_type_size)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Check size of uint64_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3KGaV8"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3KGaV8"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UINT64_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3KGaV8'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_008d2
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_008d2.dir/UINT64_T.c.o -MF CMakeFiles/cmTC_008d2.dir/UINT64_T.c.o.d -o CMakeFiles/cmTC_008d2.dir/UINT64_T.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3KGaV8/UINT64_T.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_008d2.dir/UINT64_T.c.o -o cmTC_008d2   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:204 (check_type_size)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Check size of int32_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Kl0zPw"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Kl0zPw"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INT32_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Kl0zPw'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_86470
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_86470.dir/INT32_T.c.o -MF CMakeFiles/cmTC_86470.dir/INT32_T.c.o.d -o CMakeFiles/cmTC_86470.dir/INT32_T.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Kl0zPw/INT32_T.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_86470.dir/INT32_T.c.o -o cmTC_86470   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:205 (check_type_size)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Check size of uintptr_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3NphhF"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3NphhF"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UINTPTR_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3NphhF'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_68006
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_68006.dir/UINTPTR_T.c.o -MF CMakeFiles/cmTC_68006.dir/UINTPTR_T.c.o.d -o CMakeFiles/cmTC_68006.dir/UINTPTR_T.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3NphhF/UINTPTR_T.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_68006.dir/UINTPTR_T.c.o -o cmTC_68006   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:206 (check_type_size)"
      - "CMakeLists.txt:246 (include)"
    checks:
      - "Check size of intptr_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nxD6fr"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nxD6fr"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INTPTR_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nxD6fr'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_0f96f
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_0f96f.dir/INTPTR_T.c.o -MF CMakeFiles/cmTC_0f96f.dir/INTPTR_T.c.o.d -o CMakeFiles/cmTC_0f96f.dir/INTPTR_T.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nxD6fr/INTPTR_T.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_0f96f.dir/INTPTR_T.c.o -o cmTC_0f96f   && :
        
      exitCode: 0
...
