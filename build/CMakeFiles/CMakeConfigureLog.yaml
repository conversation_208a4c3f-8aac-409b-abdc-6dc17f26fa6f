
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Darwin - 21.6.0 - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/clang 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/work/fontconfig/build/CMakeFiles/4.0.2/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting C compiler apple sysroot: "/usr/bin/clang" "-E" "apple-sdk.c"
        # 1 "apple-sdk.c"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 370 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.c" 2
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 242 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 165 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 166 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 167 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 243 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.c" 2
        
        
      Found apple sysroot: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gHqmh4"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gHqmh4"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gHqmh4'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_8fdf3
        [1/2] /usr/bin/clang   -v -Wl,-v -MD -MT CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gHqmh4 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names -v -Wl,-v CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -o cmTC_8fdf3   && :
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_8fdf3 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld  PROJECT:ld64-820.1
        BUILD 20:07:01 Nov  7 2022
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        Library search paths:
        	/usr/local/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "x86_64"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/usr/local/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gHqmh4']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/ninja -v cmTC_8fdf3]
        ignore line: [[1/2] /usr/bin/clang   -v -Wl -v -MD -MT CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-gHqmh4 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [[2/2] : && /usr/bin/clang  -Wl -search_paths_first -Wl -headerpad_max_install_names -v -Wl -v CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -o cmTC_8fdf3   && :]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_8fdf3 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [x86_64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [12.0.0] ==> ignore
          arg [13.1] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_8fdf3] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_8fdf3.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld  PROJECT:ld64-820.1
      BUILD 20:07:01 Nov  7 2022
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 14.0.0, (clang-1400.0.29.202) (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 14.0.0 (tapi-1400.0.11)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindIntl.cmake:113 (check_c_source_compiles)"
      - "CMakeLists.txt:112 (find_package)"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qtEmHr"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qtEmHr"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "Intl_IS_BUILT_IN"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qtEmHr'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_2f016
        [1/2] /usr/bin/clang -DIntl_IS_BUILT_IN  -std=gnu11 -MD -MT CMakeFiles/cmTC_2f016.dir/src.c.o -MF CMakeFiles/cmTC_2f016.dir/src.c.o.d -o CMakeFiles/cmTC_2f016.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qtEmHr/src.c
        FAILED: CMakeFiles/cmTC_2f016.dir/src.c.o 
        /usr/bin/clang -DIntl_IS_BUILT_IN  -std=gnu11 -MD -MT CMakeFiles/cmTC_2f016.dir/src.c.o -MF CMakeFiles/cmTC_2f016.dir/src.c.o.d -o CMakeFiles/cmTC_2f016.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qtEmHr/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qtEmHr/src.c:1:10: fatal error: 'libintl.h' file not found
        #include <libintl.h>
                 ^~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:134 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-WpVolH"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-WpVolH"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-WpVolH'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4b28c
        [1/2] /usr/bin/clang -DCMAKE_HAVE_LIBC_PTHREAD  -std=gnu11 -MD -MT CMakeFiles/cmTC_4b28c.dir/src.c.o -MF CMakeFiles/cmTC_4b28c.dir/src.c.o.d -o CMakeFiles/cmTC_4b28c.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-WpVolH/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4b28c.dir/src.c.o -o cmTC_4b28c   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for dirent.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-weMlSq"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-weMlSq"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DIRENT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-weMlSq'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_a2104
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_a2104.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_a2104.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_a2104.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-weMlSq/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_a2104.dir/CheckIncludeFile.c.o -o cmTC_a2104   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for dlfcn.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bhFR3o"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bhFR3o"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DLFCN_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bhFR3o'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_53c9d
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_53c9d.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_53c9d.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_53c9d.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bhFR3o/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_53c9d.dir/CheckIncludeFile.c.o -o cmTC_53c9d   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for fcntl.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FlaCY3"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FlaCY3"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FCNTL_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FlaCY3'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c39ce
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_c39ce.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_c39ce.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_c39ce.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-FlaCY3/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_c39ce.dir/CheckIncludeFile.c.o -o cmTC_c39ce   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for inttypes.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vGpJaq"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vGpJaq"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INTTYPES_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vGpJaq'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c0333
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_c0333.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_c0333.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_c0333.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vGpJaq/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_c0333.dir/CheckIncludeFile.c.o -o cmTC_c0333   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for stdint.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Slqq3I"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Slqq3I"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDINT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Slqq3I'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_90116
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_90116.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_90116.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_90116.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Slqq3I/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_90116.dir/CheckIncludeFile.c.o -o cmTC_90116   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for stdio.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BsihE1"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BsihE1"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDIO_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BsihE1'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_b0296
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_b0296.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_b0296.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_b0296.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BsihE1/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_b0296.dir/CheckIncludeFile.c.o -o cmTC_b0296   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for stdlib.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nIMQvj"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nIMQvj"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDLIB_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nIMQvj'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_487e6
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_487e6.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_487e6.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_487e6.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-nIMQvj/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_487e6.dir/CheckIncludeFile.c.o -o cmTC_487e6   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for strings.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-WnI6sR"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-WnI6sR"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRINGS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-WnI6sR'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_3befe
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_3befe.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_3befe.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_3befe.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-WnI6sR/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_3befe.dir/CheckIncludeFile.c.o -o cmTC_3befe   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for string.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-lnQASi"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-lnQASi"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRING_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-lnQASi'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5ab2b
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_5ab2b.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_5ab2b.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_5ab2b.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-lnQASi/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5ab2b.dir/CheckIncludeFile.c.o -o cmTC_5ab2b   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for unistd.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bzV4fp"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bzV4fp"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UNISTD_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bzV4fp'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_fbf18
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_fbf18.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_fbf18.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_fbf18.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bzV4fp/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_fbf18.dir/CheckIncludeFile.c.o -o cmTC_fbf18   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for sys/statvfs.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-VX7dvN"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-VX7dvN"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_STATVFS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-VX7dvN'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_00524
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_00524.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_00524.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_00524.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-VX7dvN/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_00524.dir/CheckIncludeFile.c.o -o cmTC_00524   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for sys/vfs.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeKvN5"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeKvN5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_VFS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeKvN5'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_da709
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_da709.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_da709.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_da709.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeKvN5/CheckIncludeFile.c
        FAILED: CMakeFiles/cmTC_da709.dir/CheckIncludeFile.c.o 
        /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_da709.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_da709.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_da709.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeKvN5/CheckIncludeFile.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeKvN5/CheckIncludeFile.c:1:10: fatal error: 'sys/vfs.h' file not found
        #include <sys/vfs.h>
                 ^~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for sys/statfs.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9g6RM9"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9g6RM9"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_STATFS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9g6RM9'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_dbcc6
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_dbcc6.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_dbcc6.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_dbcc6.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9g6RM9/CheckIncludeFile.c
        FAILED: CMakeFiles/cmTC_dbcc6.dir/CheckIncludeFile.c.o 
        /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_dbcc6.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_dbcc6.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_dbcc6.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9g6RM9/CheckIncludeFile.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9g6RM9/CheckIncludeFile.c:1:10: fatal error: 'sys/statfs.h' file not found
        #include <sys/statfs.h>
                 ^~~~~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for sys/stat.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-uB2v7w"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-uB2v7w"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_STAT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-uB2v7w'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_cb444
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_cb444.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_cb444.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_cb444.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-uB2v7w/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_cb444.dir/CheckIncludeFile.c.o -o cmTC_cb444   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for sys/types.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-CHp5j0"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-CHp5j0"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_TYPES_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-CHp5j0'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_80735
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_80735.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_80735.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_80735.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-CHp5j0/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_80735.dir/CheckIncludeFile.c.o -o cmTC_80735   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for sys/param.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-sFQK0P"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-sFQK0P"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_PARAM_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-sFQK0P'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_2393d
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_2393d.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_2393d.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_2393d.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-sFQK0P/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_2393d.dir/CheckIncludeFile.c.o -o cmTC_2393d   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for sys/mount.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-sFif6l"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-sFif6l"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_MOUNT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-sFif6l'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_b3e8b
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_b3e8b.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_b3e8b.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_b3e8b.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-sFif6l/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_b3e8b.dir/CheckIncludeFile.c.o -o cmTC_b3e8b   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for time.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-63Sa86"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-63Sa86"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_TIME_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-63Sa86'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_7cad7
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_7cad7.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_7cad7.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_7cad7.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-63Sa86/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_7cad7.dir/CheckIncludeFile.c.o -o cmTC_7cad7   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for wchar.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-4fhfoA"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-4fhfoA"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_WCHAR_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-4fhfoA'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4345c
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_4345c.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_4345c.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_4345c.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-4fhfoA/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4345c.dir/CheckIncludeFile.c.o -o cmTC_4345c   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for link"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3nj4Tu"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3nj4Tu"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LINK"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3nj4Tu'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_8d5c8
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=link -std=gnu11 -MD -MT CMakeFiles/cmTC_8d5c8.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_8d5c8.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_8d5c8.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-3nj4Tu/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=link -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_8d5c8.dir/CheckFunctionExists.c.o -o cmTC_8d5c8   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for mkstemp"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qJhXJr"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qJhXJr"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MKSTEMP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qJhXJr'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_665e3
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mkstemp -std=gnu11 -MD -MT CMakeFiles/cmTC_665e3.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_665e3.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_665e3.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-qJhXJr/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mkstemp -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_665e3.dir/CheckFunctionExists.c.o -o cmTC_665e3   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for mkostemp"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ASHsCg"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ASHsCg"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MKOSTEMP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ASHsCg'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_d10ae
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mkostemp -std=gnu11 -MD -MT CMakeFiles/cmTC_d10ae.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_d10ae.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_d10ae.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ASHsCg/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mkostemp -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_d10ae.dir/CheckFunctionExists.c.o -o cmTC_d10ae   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for _mktemp_s"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeTsIm"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeTsIm"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE__MKTEMP_S"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeTsIm'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_a448f
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=_mktemp_s -std=gnu11 -MD -MT CMakeFiles/cmTC_a448f.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_a448f.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_a448f.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ZeTsIm/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=_mktemp_s -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_a448f.dir/CheckFunctionExists.c.o -o cmTC_a448f   && :
        FAILED: cmTC_a448f 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=_mktemp_s -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_a448f.dir/CheckFunctionExists.c.o -o cmTC_a448f   && :
        Undefined symbols for architecture x86_64:
          "__mktemp_s", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for mkdtemp"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pyXNmB"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pyXNmB"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MKDTEMP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pyXNmB'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_2bad5
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mkdtemp -std=gnu11 -MD -MT CMakeFiles/cmTC_2bad5.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_2bad5.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_2bad5.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pyXNmB/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mkdtemp -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_2bad5.dir/CheckFunctionExists.c.o -o cmTC_2bad5   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for getopt"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-knV0cY"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-knV0cY"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETOPT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-knV0cY'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_779ab
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getopt -std=gnu11 -MD -MT CMakeFiles/cmTC_779ab.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_779ab.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_779ab.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-knV0cY/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getopt -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_779ab.dir/CheckFunctionExists.c.o -o cmTC_779ab   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for getopt_long"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-v6tcdj"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-v6tcdj"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETOPT_LONG"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-v6tcdj'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6ecf8
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getopt_long -std=gnu11 -MD -MT CMakeFiles/cmTC_6ecf8.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_6ecf8.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_6ecf8.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-v6tcdj/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getopt_long -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_6ecf8.dir/CheckFunctionExists.c.o -o cmTC_6ecf8   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for getprogname"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RDVXm2"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RDVXm2"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPROGNAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RDVXm2'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_8940f
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getprogname -std=gnu11 -MD -MT CMakeFiles/cmTC_8940f.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_8940f.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_8940f.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RDVXm2/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getprogname -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_8940f.dir/CheckFunctionExists.c.o -o cmTC_8940f   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for getexecname"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-a7H181"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-a7H181"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETEXECNAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-a7H181'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_e7f41
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getexecname -std=gnu11 -MD -MT CMakeFiles/cmTC_e7f41.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_e7f41.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_e7f41.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-a7H181/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getexecname -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e7f41.dir/CheckFunctionExists.c.o -o cmTC_e7f41   && :
        FAILED: cmTC_e7f41 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getexecname -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e7f41.dir/CheckFunctionExists.c.o -o cmTC_e7f41   && :
        Undefined symbols for architecture x86_64:
          "_getexecname", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for rand"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-JdkxDV"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-JdkxDV"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RAND"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-JdkxDV'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_be8a9
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=rand -std=gnu11 -MD -MT CMakeFiles/cmTC_be8a9.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_be8a9.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_be8a9.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-JdkxDV/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=rand -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_be8a9.dir/CheckFunctionExists.c.o -o cmTC_be8a9   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for random"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-l6a7ZQ"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-l6a7ZQ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RANDOM"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-l6a7ZQ'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_bb4cf
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=random -std=gnu11 -MD -MT CMakeFiles/cmTC_bb4cf.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_bb4cf.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_bb4cf.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-l6a7ZQ/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=random -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_bb4cf.dir/CheckFunctionExists.c.o -o cmTC_bb4cf   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for lrand48"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-57OwWJ"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-57OwWJ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LRAND48"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-57OwWJ'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_03737
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=lrand48 -std=gnu11 -MD -MT CMakeFiles/cmTC_03737.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_03737.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_03737.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-57OwWJ/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=lrand48 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_03737.dir/CheckFunctionExists.c.o -o cmTC_03737   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for random_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-1PrALU"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-1PrALU"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RANDOM_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-1PrALU'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5494b
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=random_r -std=gnu11 -MD -MT CMakeFiles/cmTC_5494b.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_5494b.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_5494b.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-1PrALU/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=random_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5494b.dir/CheckFunctionExists.c.o -o cmTC_5494b   && :
        FAILED: cmTC_5494b 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=random_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5494b.dir/CheckFunctionExists.c.o -o cmTC_5494b   && :
        Undefined symbols for architecture x86_64:
          "_random_r", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for rand_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9yEDC6"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9yEDC6"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RAND_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9yEDC6'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_3c7f3
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=rand_r -std=gnu11 -MD -MT CMakeFiles/cmTC_3c7f3.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_3c7f3.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_3c7f3.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9yEDC6/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=rand_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_3c7f3.dir/CheckFunctionExists.c.o -o cmTC_3c7f3   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for readlink"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9D4uo7"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9D4uo7"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_READLINK"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9D4uo7'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_e7638
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=readlink -std=gnu11 -MD -MT CMakeFiles/cmTC_e7638.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_e7638.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_e7638.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-9D4uo7/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=readlink -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e7638.dir/CheckFunctionExists.c.o -o cmTC_e7638   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for fstatvfs"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iDoqpP"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iDoqpP"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FSTATVFS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iDoqpP'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_e820a
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=fstatvfs -std=gnu11 -MD -MT CMakeFiles/cmTC_e820a.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_e820a.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_e820a.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-iDoqpP/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=fstatvfs -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e820a.dir/CheckFunctionExists.c.o -o cmTC_e820a   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for fstatfs"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-N1qEKR"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-N1qEKR"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FSTATFS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-N1qEKR'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_cd382
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=fstatfs -std=gnu11 -MD -MT CMakeFiles/cmTC_cd382.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_cd382.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_cd382.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-N1qEKR/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=fstatfs -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_cd382.dir/CheckFunctionExists.c.o -o cmTC_cd382   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for lstat"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-01AfhT"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-01AfhT"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LSTAT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-01AfhT'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_55e88
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=lstat -std=gnu11 -MD -MT CMakeFiles/cmTC_55e88.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_55e88.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_55e88.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-01AfhT/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=lstat -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_55e88.dir/CheckFunctionExists.c.o -o cmTC_55e88   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for strerror"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-teOmjI"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-teOmjI"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRERROR"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-teOmjI'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_1c5af
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=strerror -std=gnu11 -MD -MT CMakeFiles/cmTC_1c5af.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_1c5af.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_1c5af.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-teOmjI/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-teOmjI/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'strerror' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS strerror
                                      ^
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-teOmjI/CheckFunctionExists.c:7:3: note: 'strerror' is a builtin with type 'char *(int)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS strerror
                                      ^
        1 warning generated.
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=strerror -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_1c5af.dir/CheckFunctionExists.c.o -o cmTC_1c5af   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for strerror_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-dwZrWR"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-dwZrWR"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRERROR_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-dwZrWR'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_0a1b3
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=strerror_r -std=gnu11 -MD -MT CMakeFiles/cmTC_0a1b3.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_0a1b3.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_0a1b3.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-dwZrWR/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=strerror_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_0a1b3.dir/CheckFunctionExists.c.o -o cmTC_0a1b3   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for mmap"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-PU5MGy"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-PU5MGy"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MMAP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-PU5MGy'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_95226
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mmap -std=gnu11 -MD -MT CMakeFiles/cmTC_95226.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_95226.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_95226.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-PU5MGy/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mmap -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_95226.dir/CheckFunctionExists.c.o -o cmTC_95226   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for vprintf"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EaN2EW"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EaN2EW"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_VPRINTF"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EaN2EW'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_b41c9
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=vprintf -std=gnu11 -MD -MT CMakeFiles/cmTC_b41c9.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_b41c9.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_b41c9.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EaN2EW/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EaN2EW/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'vprintf' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vprintf
                                      ^
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-EaN2EW/CheckFunctionExists.c:7:3: note: 'vprintf' is a builtin with type 'int (const char *, struct __va_list_tag *)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vprintf
                                      ^
        1 warning generated.
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=vprintf -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_b41c9.dir/CheckFunctionExists.c.o -o cmTC_b41c9   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for vsnprintf"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-n0xWJm"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-n0xWJm"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_VSNPRINTF"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-n0xWJm'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4fc88
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=vsnprintf -std=gnu11 -MD -MT CMakeFiles/cmTC_4fc88.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_4fc88.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_4fc88.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-n0xWJm/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-n0xWJm/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'vsnprintf' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsnprintf
                                      ^
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-n0xWJm/CheckFunctionExists.c:7:3: note: 'vsnprintf' is a builtin with type 'int (char *, unsigned long, const char *, struct __va_list_tag *)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsnprintf
                                      ^
        1 warning generated.
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=vsnprintf -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4fc88.dir/CheckFunctionExists.c.o -o cmTC_4fc88   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for vsprintf"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-myz1Gp"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-myz1Gp"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_VSPRINTF"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-myz1Gp'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_894e5
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=vsprintf -std=gnu11 -MD -MT CMakeFiles/cmTC_894e5.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_894e5.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_894e5.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-myz1Gp/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-myz1Gp/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'vsprintf' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsprintf
                                      ^
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-myz1Gp/CheckFunctionExists.c:7:3: note: 'vsprintf' is a builtin with type 'int (char *, const char *, struct __va_list_tag *)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsprintf
                                      ^
        1 warning generated.
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=vsprintf -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_894e5.dir/CheckFunctionExists.c.o -o cmTC_894e5   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for getpagesize"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-7nkhNC"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-7nkhNC"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPAGESIZE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-7nkhNC'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_18393
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getpagesize -std=gnu11 -MD -MT CMakeFiles/cmTC_18393.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_18393.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_18393.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-7nkhNC/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getpagesize -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_18393.dir/CheckFunctionExists.c.o -o cmTC_18393   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for getpid"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-NGgMPr"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-NGgMPr"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPID"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-NGgMPr'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_022c1
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getpid -std=gnu11 -MD -MT CMakeFiles/cmTC_022c1.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_022c1.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_022c1.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-NGgMPr/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getpid -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_022c1.dir/CheckFunctionExists.c.o -o cmTC_022c1   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for dcgettext"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-exgbF9"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-exgbF9"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DCGETTEXT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-exgbF9'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5f606
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=dcgettext -std=gnu11 -MD -MT CMakeFiles/cmTC_5f606.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_5f606.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_5f606.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-exgbF9/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=dcgettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5f606.dir/CheckFunctionExists.c.o -o cmTC_5f606   && :
        FAILED: cmTC_5f606 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=dcgettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5f606.dir/CheckFunctionExists.c.o -o cmTC_5f606   && :
        Undefined symbols for architecture x86_64:
          "_dcgettext", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for gettext"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-HlhbXd"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-HlhbXd"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETTEXT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-HlhbXd'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_77772
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=gettext -std=gnu11 -MD -MT CMakeFiles/cmTC_77772.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_77772.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_77772.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-HlhbXd/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=gettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_77772.dir/CheckFunctionExists.c.o -o cmTC_77772   && :
        FAILED: cmTC_77772 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=gettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_77772.dir/CheckFunctionExists.c.o -o cmTC_77772   && :
        Undefined symbols for architecture x86_64:
          "_gettext", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for localtime_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pmur2J"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pmur2J"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LOCALTIME_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pmur2J'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_82d33
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=localtime_r -std=gnu11 -MD -MT CMakeFiles/cmTC_82d33.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_82d33.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_82d33.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-pmur2J/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=localtime_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_82d33.dir/CheckFunctionExists.c.o -o cmTC_82d33   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:76 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for FT_Get_BDF_Property"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ImdlJG"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ImdlJG"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_GET_BDF_PROPERTY"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ImdlJG'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_db9cb
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Get_BDF_Property -std=gnu11 -MD -MT CMakeFiles/cmTC_db9cb.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_db9cb.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_db9cb.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-ImdlJG/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Get_BDF_Property -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_db9cb.dir/CheckFunctionExists.c.o -o cmTC_db9cb  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:77 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for FT_Get_PS_Font_Info"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-jp4Xwh"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-jp4Xwh"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_GET_PS_FONT_INFO"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-jp4Xwh'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_89c67
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Get_PS_Font_Info -std=gnu11 -MD -MT CMakeFiles/cmTC_89c67.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_89c67.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_89c67.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-jp4Xwh/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Get_PS_Font_Info -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_89c67.dir/CheckFunctionExists.c.o -o cmTC_89c67  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:78 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for FT_Has_PS_Glyph_Names"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-52T58e"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-52T58e"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_HAS_PS_GLYPH_NAMES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-52T58e'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_3d189
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Has_PS_Glyph_Names -std=gnu11 -MD -MT CMakeFiles/cmTC_3d189.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_3d189.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_3d189.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-52T58e/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Has_PS_Glyph_Names -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_3d189.dir/CheckFunctionExists.c.o -o cmTC_3d189  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:79 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for FT_Get_X11_Font_Format"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-56NX8k"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-56NX8k"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_GET_X11_FONT_FORMAT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-56NX8k'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_d94b0
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Get_X11_Font_Format -std=gnu11 -MD -MT CMakeFiles/cmTC_d94b0.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_d94b0.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_d94b0.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-56NX8k/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Get_X11_Font_Format -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_d94b0.dir/CheckFunctionExists.c.o -o cmTC_d94b0  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:80 (check_function_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for FT_Done_MM_Var"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6LWctK"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6LWctK"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_DONE_MM_VAR"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6LWctK'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4a1c3
        [1/2] /usr/bin/clang  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Done_MM_Var -std=gnu11 -MD -MT CMakeFiles/cmTC_4a1c3.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_4a1c3.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_4a1c3.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-6LWctK/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=FT_Done_MM_Var -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4a1c3.dir/CheckFunctionExists.c.o -o cmTC_4a1c3  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "cmake/ConfigureChecks.cmake:87 (check_symbol_exists)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for posix_fadvise"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vCduBV"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vCduBV"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_POSIX_FADVISE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vCduBV'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_170fe
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_170fe.dir/CheckSymbolExists.c.o -MF CMakeFiles/cmTC_170fe.dir/CheckSymbolExists.c.o.d -o CMakeFiles/cmTC_170fe.dir/CheckSymbolExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vCduBV/CheckSymbolExists.c
        FAILED: CMakeFiles/cmTC_170fe.dir/CheckSymbolExists.c.o 
        /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_170fe.dir/CheckSymbolExists.c.o -MF CMakeFiles/cmTC_170fe.dir/CheckSymbolExists.c.o.d -o CMakeFiles/cmTC_170fe.dir/CheckSymbolExists.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vCduBV/CheckSymbolExists.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-vCduBV/CheckSymbolExists.c:8:19: error: use of undeclared identifier 'posix_fadvise'
          return ((int*)(&posix_fadvise))[argc];
                          ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:90 (check_struct_has_member)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATVFS_F_BASETYPE"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BU1C0w"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BU1C0w"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATVFS_F_BASETYPE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BU1C0w'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_db1d1
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STATVFS_F_BASETYPE  -std=gnu11 -MD -MT CMakeFiles/cmTC_db1d1.dir/src.c.o -MF CMakeFiles/cmTC_db1d1.dir/src.c.o.d -o CMakeFiles/cmTC_db1d1.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BU1C0w/src.c
        FAILED: CMakeFiles/cmTC_db1d1.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STATVFS_F_BASETYPE  -std=gnu11 -MD -MT CMakeFiles/cmTC_db1d1.dir/src.c.o -MF CMakeFiles/cmTC_db1d1.dir/src.c.o.d -o CMakeFiles/cmTC_db1d1.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BU1C0w/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-BU1C0w/src.c:6:39: error: no member named 'f_basetype' in 'struct statvfs'
          (void)sizeof(((struct statvfs *)0)->f_basetype);
                       ~~~~~~~~~~~~~~~~~~~~~  ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:91 (check_struct_has_member)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATVFS_F_FSTYPENAME"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Oriwhi"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Oriwhi"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATVFS_F_FSTYPENAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Oriwhi'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_fd51c
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STATVFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_fd51c.dir/src.c.o -MF CMakeFiles/cmTC_fd51c.dir/src.c.o.d -o CMakeFiles/cmTC_fd51c.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Oriwhi/src.c
        FAILED: CMakeFiles/cmTC_fd51c.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STATVFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_fd51c.dir/src.c.o -MF CMakeFiles/cmTC_fd51c.dir/src.c.o.d -o CMakeFiles/cmTC_fd51c.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Oriwhi/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Oriwhi/src.c:6:39: error: no member named 'f_fstypename' in 'struct statvfs'
          (void)sizeof(((struct statvfs *)0)->f_fstypename);
                       ~~~~~~~~~~~~~~~~~~~~~  ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:92 (check_struct_has_member)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATFS_F_FLAGS"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-whYpxj"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-whYpxj"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATFS_F_FLAGS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-whYpxj'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5a45b
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STATFS_F_FLAGS  -std=gnu11 -MD -MT CMakeFiles/cmTC_5a45b.dir/src.c.o -MF CMakeFiles/cmTC_5a45b.dir/src.c.o.d -o CMakeFiles/cmTC_5a45b.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-whYpxj/src.c
        FAILED: CMakeFiles/cmTC_5a45b.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STATFS_F_FLAGS  -std=gnu11 -MD -MT CMakeFiles/cmTC_5a45b.dir/src.c.o -MF CMakeFiles/cmTC_5a45b.dir/src.c.o.d -o CMakeFiles/cmTC_5a45b.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-whYpxj/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-whYpxj/src.c:2:10: fatal error: 'sys/vfs.h' file not found
        #include <sys/vfs.h>
                 ^~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:93 (check_struct_has_member)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATFS_F_FSTYPENAME"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xG3yCs"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xG3yCs"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATFS_F_FSTYPENAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xG3yCs'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_0f00c
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STATFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_0f00c.dir/src.c.o -MF CMakeFiles/cmTC_0f00c.dir/src.c.o.d -o CMakeFiles/cmTC_0f00c.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xG3yCs/src.c
        FAILED: CMakeFiles/cmTC_0f00c.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STATFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_0f00c.dir/src.c.o -MF CMakeFiles/cmTC_0f00c.dir/src.c.o.d -o CMakeFiles/cmTC_0f00c.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xG3yCs/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-xG3yCs/src.c:2:10: fatal error: 'sys/vfs.h' file not found
        #include <sys/vfs.h>
                 ^~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:94 (check_struct_has_member)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STAT_ST_MTIM"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e3uSLN"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e3uSLN"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STAT_ST_MTIM"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e3uSLN'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_53745
        [1/2] /usr/bin/clang -DHAVE_STRUCT_STAT_ST_MTIM  -std=gnu11 -MD -MT CMakeFiles/cmTC_53745.dir/src.c.o -MF CMakeFiles/cmTC_53745.dir/src.c.o.d -o CMakeFiles/cmTC_53745.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e3uSLN/src.c
        FAILED: CMakeFiles/cmTC_53745.dir/src.c.o 
        /usr/bin/clang -DHAVE_STRUCT_STAT_ST_MTIM  -std=gnu11 -MD -MT CMakeFiles/cmTC_53745.dir/src.c.o -MF CMakeFiles/cmTC_53745.dir/src.c.o.d -o CMakeFiles/cmTC_53745.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e3uSLN/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e3uSLN/src.c:6:36: error: no member named 'st_mtim' in 'struct stat'
          (void)sizeof(((struct stat *)0)->st_mtim);
                       ~~~~~~~~~~~~~~~~~~  ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:95 (check_struct_has_member)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_DIRENT_D_TYPE"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-LT6Q2R"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-LT6Q2R"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_DIRENT_D_TYPE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-LT6Q2R'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_3650a
        [1/2] /usr/bin/clang -DHAVE_STRUCT_DIRENT_D_TYPE  -std=gnu11 -MD -MT CMakeFiles/cmTC_3650a.dir/src.c.o -MF CMakeFiles/cmTC_3650a.dir/src.c.o.d -o CMakeFiles/cmTC_3650a.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-LT6Q2R/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_3650a.dir/src.c.o -o cmTC_3650a   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:320 (check_include_file)"
      - "cmake/ConfigureChecks.cmake:98 (check_type_size)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for stddef.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-QdAG5a"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-QdAG5a"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDDEF_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-QdAG5a'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6795a
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_6795a.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_6795a.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_6795a.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-QdAG5a/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_6795a.dir/CheckIncludeFile.c.o -o cmTC_6795a   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:98 (check_type_size)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Check size of void *"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e6AaEV"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e6AaEV"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SIZEOF_VOID_P"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e6AaEV'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_53243
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_53243.dir/SIZEOF_VOID_P.c.o -MF CMakeFiles/cmTC_53243.dir/SIZEOF_VOID_P.c.o.d -o CMakeFiles/cmTC_53243.dir/SIZEOF_VOID_P.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-e6AaEV/SIZEOF_VOID_P.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_53243.dir/SIZEOF_VOID_P.c.o -o cmTC_53243   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:106 (check_c_source_compiles)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_FLEXIBLE_ARRAY_MEMBER"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oKxByK"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oKxByK"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FLEXIBLE_ARRAY_MEMBER"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oKxByK'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_96660
        [1/2] /usr/bin/clang -DHAVE_FLEXIBLE_ARRAY_MEMBER  -std=gnu11 -MD -MT CMakeFiles/cmTC_96660.dir/src.c.o -MF CMakeFiles/cmTC_96660.dir/src.c.o.d -o CMakeFiles/cmTC_96660.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-oKxByK/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_96660.dir/src.c.o -o cmTC_96660   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:122 (check_c_source_compiles)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_PTHREAD_PRIO_INHERIT"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-5iOFS3"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-5iOFS3"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_PTHREAD_PRIO_INHERIT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-5iOFS3'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6839f
        [1/2] /usr/bin/clang -DHAVE_PTHREAD_PRIO_INHERIT  -std=gnu11 -MD -MT CMakeFiles/cmTC_6839f.dir/src.c.o -MF CMakeFiles/cmTC_6839f.dir/src.c.o.d -o CMakeFiles/cmTC_6839f.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-5iOFS3/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_6839f.dir/src.c.o -o cmTC_6839f   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:134 (check_c_source_compiles)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_STDATOMIC_PRIMITIVES"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-11dJJH"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-11dJJH"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDATOMIC_PRIMITIVES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-11dJJH'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c3af7
        [1/2] /usr/bin/clang -DHAVE_STDATOMIC_PRIMITIVES  -std=gnu11 -MD -MT CMakeFiles/cmTC_c3af7.dir/src.c.o -MF CMakeFiles/cmTC_c3af7.dir/src.c.o.d -o CMakeFiles/cmTC_c3af7.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-11dJJH/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_c3af7.dir/src.c.o -o cmTC_c3af7   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:143 (check_c_source_compiles)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_INTEL_ATOMIC_PRIMITIVES"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-4qkVDV"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-4qkVDV"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INTEL_ATOMIC_PRIMITIVES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-4qkVDV'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_adb84
        [1/2] /usr/bin/clang -DHAVE_INTEL_ATOMIC_PRIMITIVES  -std=gnu11 -MD -MT CMakeFiles/cmTC_adb84.dir/src.c.o -MF CMakeFiles/cmTC_adb84.dir/src.c.o.d -o CMakeFiles/cmTC_adb84.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-4qkVDV/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_adb84.dir/src.c.o -o cmTC_adb84   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:151 (check_c_source_compiles)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test HAVE_SOLARIS_ATOMIC_OPS"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F8qgUE"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F8qgUE"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SOLARIS_ATOMIC_OPS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F8qgUE'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_60bf4
        [1/2] /usr/bin/clang -DHAVE_SOLARIS_ATOMIC_OPS  -std=gnu11 -MD -MT CMakeFiles/cmTC_60bf4.dir/src.c.o -MF CMakeFiles/cmTC_60bf4.dir/src.c.o.d -o CMakeFiles/cmTC_60bf4.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F8qgUE/src.c
        FAILED: CMakeFiles/cmTC_60bf4.dir/src.c.o 
        /usr/bin/clang -DHAVE_SOLARIS_ATOMIC_OPS  -std=gnu11 -MD -MT CMakeFiles/cmTC_60bf4.dir/src.c.o -MF CMakeFiles/cmTC_60bf4.dir/src.c.o.d -o CMakeFiles/cmTC_60bf4.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F8qgUE/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-F8qgUE/src.c:2:10: fatal error: 'atomic.h' file not found
        #include <atomic.h>
                 ^~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:165 (check_c_source_compiles)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Performing Test FREETYPE_PCF_LONG_FAMILY_NAMES"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Zw9dqw"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Zw9dqw"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "FREETYPE_PCF_LONG_FAMILY_NAMES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Zw9dqw'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4d6c8
        [1/2] /usr/bin/clang -DFREETYPE_PCF_LONG_FAMILY_NAMES -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -std=gnu11 -MD -MT CMakeFiles/cmTC_4d6c8.dir/src.c.o -MF CMakeFiles/cmTC_4d6c8.dir/src.c.o.d -o CMakeFiles/cmTC_4d6c8.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Zw9dqw/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4d6c8.dir/src.c.o -o cmTC_4d6c8  -lfreetype && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:203 (check_type_size)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Check size of uint64_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-DtSoCa"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-DtSoCa"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UINT64_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-DtSoCa'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_a7faf
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_a7faf.dir/UINT64_T.c.o -MF CMakeFiles/cmTC_a7faf.dir/UINT64_T.c.o.d -o CMakeFiles/cmTC_a7faf.dir/UINT64_T.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-DtSoCa/UINT64_T.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_a7faf.dir/UINT64_T.c.o -o cmTC_a7faf   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:204 (check_type_size)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Check size of int32_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-mxPPkW"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-mxPPkW"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INT32_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-mxPPkW'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6bba7
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_6bba7.dir/INT32_T.c.o -MF CMakeFiles/cmTC_6bba7.dir/INT32_T.c.o.d -o CMakeFiles/cmTC_6bba7.dir/INT32_T.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-mxPPkW/INT32_T.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_6bba7.dir/INT32_T.c.o -o cmTC_6bba7   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:205 (check_type_size)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Check size of uintptr_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Iep0Xw"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Iep0Xw"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UINTPTR_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Iep0Xw'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_8eb00
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_8eb00.dir/UINTPTR_T.c.o -MF CMakeFiles/cmTC_8eb00.dir/UINTPTR_T.c.o.d -o CMakeFiles/cmTC_8eb00.dir/UINTPTR_T.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-Iep0Xw/UINTPTR_T.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_8eb00.dir/UINTPTR_T.c.o -o cmTC_8eb00   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:206 (check_type_size)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Check size of intptr_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-W88jMD"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-W88jMD"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INTPTR_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-W88jMD'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_2b9f2
        [1/2] /usr/bin/clang   -std=gnu11 -MD -MT CMakeFiles/cmTC_2b9f2.dir/INTPTR_T.c.o -MF CMakeFiles/cmTC_2b9f2.dir/INTPTR_T.c.o.d -o CMakeFiles/cmTC_2b9f2.dir/INTPTR_T.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-W88jMD/INTPTR_T.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_2b9f2.dir/INTPTR_T.c.o -o cmTC_2b9f2   && :
        
      exitCode: 0
...
