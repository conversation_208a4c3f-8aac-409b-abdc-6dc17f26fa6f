
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Darwin - 21.6.0 - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is Apple<PERSON>lang, found in:
        /Users/<USER>/work/fontconfig/build/CMakeFiles/4.0.2/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting C compiler apple sysroot: "/usr/bin/cc" "-E" "apple-sdk.c"
        # 1 "apple-sdk.c"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 370 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.c" 2
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 242 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 165 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 166 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 167 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 243 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.c" 2
        
        
      Found apple sysroot: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RPOAma"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RPOAma"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RPOAma'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_57d5b/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_57d5b.dir/build.make CMakeFiles/cmTC_57d5b.dir/build
        Building C object CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -v -Wl,-v -MD -MT CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RPOAma -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking C executable cmTC_57d5b
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_57d5b.dir/link.txt --verbose=1
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_57d5b -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld  PROJECT:ld64-820.1
        BUILD 20:07:01 Nov  7 2022
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        Library search paths:
        	/usr/local/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -o cmTC_57d5b
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "x86_64"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/usr/local/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RPOAma']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_57d5b/fast]
        ignore line: [/Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_57d5b.dir/build.make CMakeFiles/cmTC_57d5b.dir/build]
        ignore line: [Building C object CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -v -Wl -v -MD -MT CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-RPOAma -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_57d5b]
        ignore line: [/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_57d5b.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_57d5b -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [x86_64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [12.0.0] ==> ignore
          arg [13.1] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_57d5b] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_57d5b.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld  PROJECT:ld64-820.1
      BUILD 20:07:01 Nov  7 2022
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 14.0.0, (clang-1400.0.29.202) (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 14.0.0 (tapi-1400.0.11)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindIntl.cmake:113 (check_c_source_compiles)"
      - "CMakeLists.txt:112 (find_package)"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bM46tY"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bM46tY"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "Intl_IS_BUILT_IN"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bM46tY'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_41ed3/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_41ed3.dir/build.make CMakeFiles/cmTC_41ed3.dir/build
        Building C object CMakeFiles/cmTC_41ed3.dir/src.c.o
        /usr/bin/cc -DIntl_IS_BUILT_IN  -std=gnu11 -MD -MT CMakeFiles/cmTC_41ed3.dir/src.c.o -MF CMakeFiles/cmTC_41ed3.dir/src.c.o.d -o CMakeFiles/cmTC_41ed3.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bM46tY/src.c
        /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-bM46tY/src.c:1:10: fatal error: 'libintl.h' file not found
        #include <libintl.h>
                 ^~~~~~~~~~~
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_41ed3.dir/src.c.o] Error 1
        make: *** [cmTC_41ed3/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:134 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-v5xnpl"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-v5xnpl"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-v5xnpl'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_e7bbd/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_e7bbd.dir/build.make CMakeFiles/cmTC_e7bbd.dir/build
        Building C object CMakeFiles/cmTC_e7bbd.dir/src.c.o
        /usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD  -std=gnu11 -MD -MT CMakeFiles/cmTC_e7bbd.dir/src.c.o -MF CMakeFiles/cmTC_e7bbd.dir/src.c.o.d -o CMakeFiles/cmTC_e7bbd.dir/src.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-v5xnpl/src.c
        Linking C executable cmTC_e7bbd
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e7bbd.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_e7bbd.dir/src.c.o -o cmTC_e7bbd
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:248 (include)"
    checks:
      - "Looking for dirent.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-s6g7Ms"
      binary: "/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-s6g7Ms"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DIRENT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-s6g7Ms'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_f88ad/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_f88ad.dir/build.make CMakeFiles/cmTC_f88ad.dir/build
        Building C object CMakeFiles/cmTC_f88ad.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_f88ad.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_f88ad.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_f88ad.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build/CMakeFiles/CMakeScratch/TryCompile-s6g7Ms/CheckIncludeFile.c
        Linking C executable cmTC_f88ad
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f88ad.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_f88ad.dir/CheckIncludeFile.c.o -o cmTC_f88ad
        
      exitCode: 0
