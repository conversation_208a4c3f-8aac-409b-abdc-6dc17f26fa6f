# Install script for directory: /Users/<USER>/work/fontconfig/conf.d

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/usr/local")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Debug")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set path to fallback-tool for dependency-resolution.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/usr/bin/objdump")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Runtime" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/usr/local/share/fontconfig/conf.avail/05-reset-dirs-sample.conf;/usr/local/share/fontconfig/conf.avail/09-autohint-if-no-hinting.conf;/usr/local/share/fontconfig/conf.avail/10-autohint.conf;/usr/local/share/fontconfig/conf.avail/10-hinting-full.conf;/usr/local/share/fontconfig/conf.avail/10-hinting-medium.conf;/usr/local/share/fontconfig/conf.avail/10-hinting-none.conf;/usr/local/share/fontconfig/conf.avail/10-hinting-slight.conf;/usr/local/share/fontconfig/conf.avail/10-no-antialias.conf;/usr/local/share/fontconfig/conf.avail/10-scale-bitmap-fonts.conf;/usr/local/share/fontconfig/conf.avail/10-sub-pixel-bgr.conf;/usr/local/share/fontconfig/conf.avail/10-sub-pixel-none.conf;/usr/local/share/fontconfig/conf.avail/10-sub-pixel-rgb.conf;/usr/local/share/fontconfig/conf.avail/10-sub-pixel-vbgr.conf;/usr/local/share/fontconfig/conf.avail/10-sub-pixel-vrgb.conf;/usr/local/share/fontconfig/conf.avail/10-unhinted.conf;/usr/local/share/fontconfig/conf.avail/10-yes-antialias.conf;/usr/local/share/fontconfig/conf.avail/11-lcdfilter-default.conf;/usr/local/share/fontconfig/conf.avail/11-lcdfilter-legacy.conf;/usr/local/share/fontconfig/conf.avail/11-lcdfilter-light.conf;/usr/local/share/fontconfig/conf.avail/11-lcdfilter-none.conf;/usr/local/share/fontconfig/conf.avail/20-unhint-small-vera.conf;/usr/local/share/fontconfig/conf.avail/25-unhint-nonlatin.conf;/usr/local/share/fontconfig/conf.avail/30-metric-aliases.conf;/usr/local/share/fontconfig/conf.avail/40-nonlatin.conf;/usr/local/share/fontconfig/conf.avail/45-generic.conf;/usr/local/share/fontconfig/conf.avail/45-latin.conf;/usr/local/share/fontconfig/conf.avail/48-guessfamily.conf;/usr/local/share/fontconfig/conf.avail/48-spacing.conf;/usr/local/share/fontconfig/conf.avail/49-sansserif.conf;/usr/local/share/fontconfig/conf.avail/50-user.conf;/usr/local/share/fontconfig/conf.avail/51-local.conf;/usr/local/share/fontconfig/conf.avail/60-generic.conf;/usr/local/share/fontconfig/conf.avail/60-latin.conf;/usr/local/share/fontconfig/conf.avail/65-fonts-persian.conf;/usr/local/share/fontconfig/conf.avail/65-khmer.conf;/usr/local/share/fontconfig/conf.avail/65-nonlatin.conf;/usr/local/share/fontconfig/conf.avail/69-unifont.conf;/usr/local/share/fontconfig/conf.avail/70-no-bitmaps.conf;/usr/local/share/fontconfig/conf.avail/70-no-bitmaps-and-emoji.conf;/usr/local/share/fontconfig/conf.avail/70-no-bitmaps-except-emoji.conf;/usr/local/share/fontconfig/conf.avail/70-yes-bitmaps.conf;/usr/local/share/fontconfig/conf.avail/80-delicious.conf;/usr/local/share/fontconfig/conf.avail/90-synthetic.conf")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/usr/local/share/fontconfig/conf.avail" TYPE FILE FILES
    "/Users/<USER>/work/fontconfig/conf.d/05-reset-dirs-sample.conf"
    "/Users/<USER>/work/fontconfig/conf.d/09-autohint-if-no-hinting.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-autohint.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-hinting-full.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-hinting-medium.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-hinting-none.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-hinting-slight.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-no-antialias.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-scale-bitmap-fonts.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-bgr.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-none.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-rgb.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-vbgr.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-vrgb.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-unhinted.conf"
    "/Users/<USER>/work/fontconfig/conf.d/10-yes-antialias.conf"
    "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-default.conf"
    "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-legacy.conf"
    "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-light.conf"
    "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-none.conf"
    "/Users/<USER>/work/fontconfig/conf.d/20-unhint-small-vera.conf"
    "/Users/<USER>/work/fontconfig/conf.d/25-unhint-nonlatin.conf"
    "/Users/<USER>/work/fontconfig/conf.d/30-metric-aliases.conf"
    "/Users/<USER>/work/fontconfig/conf.d/40-nonlatin.conf"
    "/Users/<USER>/work/fontconfig/conf.d/45-generic.conf"
    "/Users/<USER>/work/fontconfig/conf.d/45-latin.conf"
    "/Users/<USER>/work/fontconfig/conf.d/48-guessfamily.conf"
    "/Users/<USER>/work/fontconfig/conf.d/48-spacing.conf"
    "/Users/<USER>/work/fontconfig/conf.d/49-sansserif.conf"
    "/Users/<USER>/work/fontconfig/conf.d/50-user.conf"
    "/Users/<USER>/work/fontconfig/conf.d/51-local.conf"
    "/Users/<USER>/work/fontconfig/conf.d/60-generic.conf"
    "/Users/<USER>/work/fontconfig/conf.d/60-latin.conf"
    "/Users/<USER>/work/fontconfig/conf.d/65-fonts-persian.conf"
    "/Users/<USER>/work/fontconfig/conf.d/65-khmer.conf"
    "/Users/<USER>/work/fontconfig/conf.d/65-nonlatin.conf"
    "/Users/<USER>/work/fontconfig/conf.d/69-unifont.conf"
    "/Users/<USER>/work/fontconfig/conf.d/70-no-bitmaps.conf"
    "/Users/<USER>/work/fontconfig/conf.d/70-no-bitmaps-and-emoji.conf"
    "/Users/<USER>/work/fontconfig/conf.d/70-no-bitmaps-except-emoji.conf"
    "/Users/<USER>/work/fontconfig/conf.d/70-yes-bitmaps.conf"
    "/Users/<USER>/work/fontconfig/conf.d/80-delicious.conf"
    "/Users/<USER>/work/fontconfig/conf.d/90-synthetic.conf"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Runtime" OR NOT CMAKE_INSTALL_COMPONENT)
  
    file(MAKE_DIRECTORY "${CMAKE_INSTALL_PREFIX}//usr/local/etc/fonts/conf.d")
    foreach(conf_file 10-hinting-slight.conf;10-scale-bitmap-fonts.conf;10-sub-pixel-none.conf;10-yes-antialias.conf;11-lcdfilter-default.conf;20-unhint-small-vera.conf;30-metric-aliases.conf;40-nonlatin.conf;45-generic.conf;45-latin.conf;48-spacing.conf;49-sansserif.conf;50-user.conf;51-local.conf;60-generic.conf;60-latin.conf;65-fonts-persian.conf;65-nonlatin.conf;69-unifont.conf;80-delicious.conf;90-synthetic.conf;70-no-bitmaps-except-emoji.conf)
        set(source_file "${CMAKE_INSTALL_PREFIX}//usr/local/share/fontconfig/conf.avail/${conf_file}")
        set(target_file "${CMAKE_INSTALL_PREFIX}//usr/local/etc/fonts/conf.d/${conf_file}")

        # Remove existing link/file if it exists
        if(EXISTS "${target_file}" OR IS_SYMLINK "${target_file}")
            file(REMOVE "${target_file}")
        endif()

        # Create symbolic link
        execute_process(
            COMMAND ${CMAKE_COMMAND} -E create_symlink
                    "../conf.avail/${conf_file}"
                    "${target_file}"
            RESULT_VARIABLE link_result
        )

        if(NOT link_result EQUAL 0)
            message(WARNING "Failed to create symbolic link for ${conf_file}")
        endif()
    endforeach()

endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Runtime" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/usr/local/share/fontconfig/conf.avail/35-lang-normalize.conf")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/usr/local/share/fontconfig/conf.avail" TYPE FILE FILES "/Users/<USER>/work/fontconfig/build/conf.d/35-lang-normalize.conf")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Runtime" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/usr/local/etc/fonts/conf.d/README")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/usr/local/etc/fonts/conf.d" TYPE FILE FILES "/Users/<USER>/work/fontconfig/build/conf.d/README")
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
if(CMAKE_INSTALL_LOCAL_ONLY)
  file(WRITE "/Users/<USER>/work/fontconfig/build/conf.d/install_local_manifest.txt"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
endif()
