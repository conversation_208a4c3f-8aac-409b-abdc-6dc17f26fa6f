[{"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src/.. -I/Users/<USER>/work/fontconfig/build/src -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/patternlib_internal.dir/fcpat.c.o -c /Users/<USER>/work/fontconfig/src/fcpat.c", "file": "/Users/<USER>/work/fontconfig/src/fcpat.c", "output": "src/CMakeFiles/patternlib_internal.dir/fcpat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcatomic.c.o -c /Users/<USER>/work/fontconfig/src/fcatomic.c", "file": "/Users/<USER>/work/fontconfig/src/fcatomic.c", "output": "src/CMakeFiles/fontconfig.dir/fcatomic.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fccache.c.o -c /Users/<USER>/work/fontconfig/src/fccache.c", "file": "/Users/<USER>/work/fontconfig/src/fccache.c", "output": "src/CMakeFiles/fontconfig.dir/fccache.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fccfg.c.o -c /Users/<USER>/work/fontconfig/src/fccfg.c", "file": "/Users/<USER>/work/fontconfig/src/fccfg.c", "output": "src/CMakeFiles/fontconfig.dir/fccfg.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fccharset.c.o -c /Users/<USER>/work/fontconfig/src/fccharset.c", "file": "/Users/<USER>/work/fontconfig/src/fccharset.c", "output": "src/CMakeFiles/fontconfig.dir/fccharset.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fccompat.c.o -c /Users/<USER>/work/fontconfig/src/fccompat.c", "file": "/Users/<USER>/work/fontconfig/src/fccompat.c", "output": "src/CMakeFiles/fontconfig.dir/fccompat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcdbg.c.o -c /Users/<USER>/work/fontconfig/src/fcdbg.c", "file": "/Users/<USER>/work/fontconfig/src/fcdbg.c", "output": "src/CMakeFiles/fontconfig.dir/fcdbg.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcdefault.c.o -c /Users/<USER>/work/fontconfig/src/fcdefault.c", "file": "/Users/<USER>/work/fontconfig/src/fcdefault.c", "output": "src/CMakeFiles/fontconfig.dir/fcdefault.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcdir.c.o -c /Users/<USER>/work/fontconfig/src/fcdir.c", "file": "/Users/<USER>/work/fontconfig/src/fcdir.c", "output": "src/CMakeFiles/fontconfig.dir/fcdir.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcformat.c.o -c /Users/<USER>/work/fontconfig/src/fcformat.c", "file": "/Users/<USER>/work/fontconfig/src/fcformat.c", "output": "src/CMakeFiles/fontconfig.dir/fcformat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcfreetype.c.o -c /Users/<USER>/work/fontconfig/src/fcfreetype.c", "file": "/Users/<USER>/work/fontconfig/src/fcfreetype.c", "output": "src/CMakeFiles/fontconfig.dir/fcfreetype.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcfs.c.o -c /Users/<USER>/work/fontconfig/src/fcfs.c", "file": "/Users/<USER>/work/fontconfig/src/fcfs.c", "output": "src/CMakeFiles/fontconfig.dir/fcfs.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcptrlist.c.o -c /Users/<USER>/work/fontconfig/src/fcptrlist.c", "file": "/Users/<USER>/work/fontconfig/src/fcptrlist.c", "output": "src/CMakeFiles/fontconfig.dir/fcptrlist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fchash.c.o -c /Users/<USER>/work/fontconfig/src/fchash.c", "file": "/Users/<USER>/work/fontconfig/src/fchash.c", "output": "src/CMakeFiles/fontconfig.dir/fchash.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcinit.c.o -c /Users/<USER>/work/fontconfig/src/fcinit.c", "file": "/Users/<USER>/work/fontconfig/src/fcinit.c", "output": "src/CMakeFiles/fontconfig.dir/fcinit.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fclang.c.o -c /Users/<USER>/work/fontconfig/src/fclang.c", "file": "/Users/<USER>/work/fontconfig/src/fclang.c", "output": "src/CMakeFiles/fontconfig.dir/fclang.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fclist.c.o -c /Users/<USER>/work/fontconfig/src/fclist.c", "file": "/Users/<USER>/work/fontconfig/src/fclist.c", "output": "src/CMakeFiles/fontconfig.dir/fclist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcmatch.c.o -c /Users/<USER>/work/fontconfig/src/fcmatch.c", "file": "/Users/<USER>/work/fontconfig/src/fcmatch.c", "output": "src/CMakeFiles/fontconfig.dir/fcmatch.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcmatrix.c.o -c /Users/<USER>/work/fontconfig/src/fcmatrix.c", "file": "/Users/<USER>/work/fontconfig/src/fcmatrix.c", "output": "src/CMakeFiles/fontconfig.dir/fcmatrix.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcname.c.o -c /Users/<USER>/work/fontconfig/src/fcname.c", "file": "/Users/<USER>/work/fontconfig/src/fcname.c", "output": "src/CMakeFiles/fontconfig.dir/fcname.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcobjs.c.o -c /Users/<USER>/work/fontconfig/src/fcobjs.c", "file": "/Users/<USER>/work/fontconfig/src/fcobjs.c", "output": "src/CMakeFiles/fontconfig.dir/fcobjs.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcrange.c.o -c /Users/<USER>/work/fontconfig/src/fcrange.c", "file": "/Users/<USER>/work/fontconfig/src/fcrange.c", "output": "src/CMakeFiles/fontconfig.dir/fcrange.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcserialize.c.o -c /Users/<USER>/work/fontconfig/src/fcserialize.c", "file": "/Users/<USER>/work/fontconfig/src/fcserialize.c", "output": "src/CMakeFiles/fontconfig.dir/fcserialize.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcstat.c.o -c /Users/<USER>/work/fontconfig/src/fcstat.c", "file": "/Users/<USER>/work/fontconfig/src/fcstat.c", "output": "src/CMakeFiles/fontconfig.dir/fcstat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcstr.c.o -c /Users/<USER>/work/fontconfig/src/fcstr.c", "file": "/Users/<USER>/work/fontconfig/src/fcstr.c", "output": "src/CMakeFiles/fontconfig.dir/fcstr.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcweight.c.o -c /Users/<USER>/work/fontconfig/src/fcweight.c", "file": "/Users/<USER>/work/fontconfig/src/fcweight.c", "output": "src/CMakeFiles/fontconfig.dir/fcweight.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/fcxml.c.o -c /Users/<USER>/work/fontconfig/src/fcxml.c", "file": "/Users/<USER>/work/fontconfig/src/fcxml.c", "output": "src/CMakeFiles/fontconfig.dir/fcxml.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -Dfontconfig_EXPORTS -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -fPIC -Wno-excess-initializers -o src/CMakeFiles/fontconfig.dir/ftglue.c.o -c /Users/<USER>/work/fontconfig/src/ftglue.c", "file": "/Users/<USER>/work/fontconfig/src/ftglue.c", "output": "src/CMakeFiles/fontconfig.dir/ftglue.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcatomic.c.o -c /Users/<USER>/work/fontconfig/src/fcatomic.c", "file": "/Users/<USER>/work/fontconfig/src/fcatomic.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcatomic.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fccache.c.o -c /Users/<USER>/work/fontconfig/src/fccache.c", "file": "/Users/<USER>/work/fontconfig/src/fccache.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fccache.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fccfg.c.o -c /Users/<USER>/work/fontconfig/src/fccfg.c", "file": "/Users/<USER>/work/fontconfig/src/fccfg.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fccfg.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fccharset.c.o -c /Users/<USER>/work/fontconfig/src/fccharset.c", "file": "/Users/<USER>/work/fontconfig/src/fccharset.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fccharset.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fccompat.c.o -c /Users/<USER>/work/fontconfig/src/fccompat.c", "file": "/Users/<USER>/work/fontconfig/src/fccompat.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fccompat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcdbg.c.o -c /Users/<USER>/work/fontconfig/src/fcdbg.c", "file": "/Users/<USER>/work/fontconfig/src/fcdbg.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcdbg.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcdefault.c.o -c /Users/<USER>/work/fontconfig/src/fcdefault.c", "file": "/Users/<USER>/work/fontconfig/src/fcdefault.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcdefault.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcdir.c.o -c /Users/<USER>/work/fontconfig/src/fcdir.c", "file": "/Users/<USER>/work/fontconfig/src/fcdir.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcdir.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcformat.c.o -c /Users/<USER>/work/fontconfig/src/fcformat.c", "file": "/Users/<USER>/work/fontconfig/src/fcformat.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcformat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcfreetype.c.o -c /Users/<USER>/work/fontconfig/src/fcfreetype.c", "file": "/Users/<USER>/work/fontconfig/src/fcfreetype.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcfreetype.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcfs.c.o -c /Users/<USER>/work/fontconfig/src/fcfs.c", "file": "/Users/<USER>/work/fontconfig/src/fcfs.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcfs.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcptrlist.c.o -c /Users/<USER>/work/fontconfig/src/fcptrlist.c", "file": "/Users/<USER>/work/fontconfig/src/fcptrlist.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcptrlist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fchash.c.o -c /Users/<USER>/work/fontconfig/src/fchash.c", "file": "/Users/<USER>/work/fontconfig/src/fchash.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fchash.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcinit.c.o -c /Users/<USER>/work/fontconfig/src/fcinit.c", "file": "/Users/<USER>/work/fontconfig/src/fcinit.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcinit.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fclang.c.o -c /Users/<USER>/work/fontconfig/src/fclang.c", "file": "/Users/<USER>/work/fontconfig/src/fclang.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fclang.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fclist.c.o -c /Users/<USER>/work/fontconfig/src/fclist.c", "file": "/Users/<USER>/work/fontconfig/src/fclist.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fclist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcmatch.c.o -c /Users/<USER>/work/fontconfig/src/fcmatch.c", "file": "/Users/<USER>/work/fontconfig/src/fcmatch.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcmatch.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcmatrix.c.o -c /Users/<USER>/work/fontconfig/src/fcmatrix.c", "file": "/Users/<USER>/work/fontconfig/src/fcmatrix.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcmatrix.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcname.c.o -c /Users/<USER>/work/fontconfig/src/fcname.c", "file": "/Users/<USER>/work/fontconfig/src/fcname.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcname.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcobjs.c.o -c /Users/<USER>/work/fontconfig/src/fcobjs.c", "file": "/Users/<USER>/work/fontconfig/src/fcobjs.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcobjs.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcrange.c.o -c /Users/<USER>/work/fontconfig/src/fcrange.c", "file": "/Users/<USER>/work/fontconfig/src/fcrange.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcrange.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcserialize.c.o -c /Users/<USER>/work/fontconfig/src/fcserialize.c", "file": "/Users/<USER>/work/fontconfig/src/fcserialize.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcserialize.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcstat.c.o -c /Users/<USER>/work/fontconfig/src/fcstat.c", "file": "/Users/<USER>/work/fontconfig/src/fcstat.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcstat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcstr.c.o -c /Users/<USER>/work/fontconfig/src/fcstr.c", "file": "/Users/<USER>/work/fontconfig/src/fcstr.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcstr.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcweight.c.o -c /Users/<USER>/work/fontconfig/src/fcweight.c", "file": "/Users/<USER>/work/fontconfig/src/fcweight.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcweight.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/fcxml.c.o -c /Users/<USER>/work/fontconfig/src/fcxml.c", "file": "/Users/<USER>/work/fontconfig/src/fcxml.c", "output": "src/CMakeFiles/fontconfig_internal.dir/fcxml.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case -g -std=gnu11 -Wno-excess-initializers -o src/CMakeFiles/fontconfig_internal.dir/ftglue.c.o -c /Users/<USER>/work/fontconfig/src/ftglue.c", "file": "/Users/<USER>/work/fontconfig/src/ftglue.c", "output": "src/CMakeFiles/fontconfig_internal.dir/ftglue.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-cache/../src -I/Users/<USER>/work/fontconfig/build/fc-cache/.. -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o fc-cache/CMakeFiles/fc-cache.dir/fc-cache.c.o -c /Users/<USER>/work/fontconfig/fc-cache/fc-cache.c", "file": "/Users/<USER>/work/fontconfig/fc-cache/fc-cache.c", "output": "fc-cache/CMakeFiles/fc-cache.dir/fc-cache.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-cat/../src -I/Users/<USER>/work/fontconfig/build/fc-cat/.. -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o fc-cat/CMakeFiles/fc-cat.dir/fc-cat.c.o -c /Users/<USER>/work/fontconfig/fc-cat/fc-cat.c", "file": "/Users/<USER>/work/fontconfig/fc-cat/fc-cat.c", "output": "fc-cat/CMakeFiles/fc-cat.dir/fc-cat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-conflist/../src -I/Users/<USER>/work/fontconfig/build/fc-conflist/.. -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o fc-conflist/CMakeFiles/fc-conflist.dir/fc-conflist.c.o -c /Users/<USER>/work/fontconfig/fc-conflist/fc-conflist.c", "file": "/Users/<USER>/work/fontconfig/fc-conflist/fc-conflist.c", "output": "fc-conflist/CMakeFiles/fc-conflist.dir/fc-conflist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-list/../src -I/Users/<USER>/work/fontconfig/build/fc-list/.. -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o fc-list/CMakeFiles/fc-list.dir/fc-list.c.o -c /Users/<USER>/work/fontconfig/fc-list/fc-list.c", "file": "/Users/<USER>/work/fontconfig/fc-list/fc-list.c", "output": "fc-list/CMakeFiles/fc-list.dir/fc-list.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-match/../src -I/Users/<USER>/work/fontconfig/build/fc-match/.. -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o fc-match/CMakeFiles/fc-match.dir/fc-match.c.o -c /Users/<USER>/work/fontconfig/fc-match/fc-match.c", "file": "/Users/<USER>/work/fontconfig/fc-match/fc-match.c", "output": "fc-match/CMakeFiles/fc-match.dir/fc-match.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-pattern/../src -I/Users/<USER>/work/fontconfig/build/fc-pattern/.. -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o fc-pattern/CMakeFiles/fc-pattern.dir/fc-pattern.c.o -c /Users/<USER>/work/fontconfig/fc-pattern/fc-pattern.c", "file": "/Users/<USER>/work/fontconfig/fc-pattern/fc-pattern.c", "output": "fc-pattern/CMakeFiles/fc-pattern.dir/fc-pattern.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-query/../src -I/Users/<USER>/work/fontconfig/build/fc-query/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o fc-query/CMakeFiles/fc-query.dir/fc-query.c.o -c /Users/<USER>/work/fontconfig/fc-query/fc-query.c", "file": "/Users/<USER>/work/fontconfig/fc-query/fc-query.c", "output": "fc-query/CMakeFiles/fc-query.dir/fc-query.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-scan/../src -I/Users/<USER>/work/fontconfig/build/fc-scan/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o fc-scan/CMakeFiles/fc-scan.dir/fc-scan.c.o -c /Users/<USER>/work/fontconfig/fc-scan/fc-scan.c", "file": "/Users/<USER>/work/fontconfig/fc-scan/fc-scan.c", "output": "fc-scan/CMakeFiles/fc-scan.dir/fc-scan.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-validate/../src -I/Users/<USER>/work/fontconfig/build/fc-validate/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o fc-validate/CMakeFiles/fc-validate.dir/fc-validate.c.o -c /Users/<USER>/work/fontconfig/fc-validate/fc-validate.c", "file": "/Users/<USER>/work/fontconfig/fc-validate/fc-validate.c", "output": "fc-validate/CMakeFiles/fc-validate.dir/fc-validate.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -DSRCDIR=\\\"/Users/<USER>/work/fontconfig/test\\\" -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_bz89617.dir/test-bz89617.c.o -c /Users/<USER>/work/fontconfig/test/test-bz89617.c", "file": "/Users/<USER>/work/fontconfig/test/test-bz89617.c", "output": "test/CMakeFiles/test_bz89617.dir/test-bz89617.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_bz131804.dir/test-bz131804.c.o -c /Users/<USER>/work/fontconfig/test/test-bz131804.c", "file": "/Users/<USER>/work/fontconfig/test/test-bz131804.c", "output": "test/CMakeFiles/test_bz131804.dir/test-bz131804.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_bz96676.dir/test-bz96676.c.o -c /Users/<USER>/work/fontconfig/test/test-bz96676.c", "file": "/Users/<USER>/work/fontconfig/test/test-bz96676.c", "output": "test/CMakeFiles/test_bz96676.dir/test-bz96676.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_name_parse.dir/test-name-parse.c.o -c /Users/<USER>/work/fontconfig/test/test-name-parse.c", "file": "/Users/<USER>/work/fontconfig/test/test-name-parse.c", "output": "test/CMakeFiles/test_name_parse.dir/test-name-parse.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_bz106618.dir/test-bz106618.c.o -c /Users/<USER>/work/fontconfig/test/test-bz106618.c", "file": "/Users/<USER>/work/fontconfig/test/test-bz106618.c", "output": "test/CMakeFiles/test_bz106618.dir/test-bz106618.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_bz1744377.dir/test-bz1744377.c.o -c /Users/<USER>/work/fontconfig/test/test-bz1744377.c", "file": "/Users/<USER>/work/fontconfig/test/test-bz1744377.c", "output": "test/CMakeFiles/test_bz1744377.dir/test-bz1744377.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_issue180.dir/test-issue180.c.o -c /Users/<USER>/work/fontconfig/test/test-issue180.c", "file": "/Users/<USER>/work/fontconfig/test/test-issue180.c", "output": "test/CMakeFiles/test_issue180.dir/test-issue180.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_family_matching.dir/test-family-matching.c.o -c /Users/<USER>/work/fontconfig/test/test-family-matching.c", "file": "/Users/<USER>/work/fontconfig/test/test-family-matching.c", "output": "test/CMakeFiles/test_family_matching.dir/test-family-matching.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_ptrlist.dir/test-ptrlist.c.o -c /Users/<USER>/work/fontconfig/test/test-ptrlist.c", "file": "/Users/<USER>/work/fontconfig/test/test-ptrlist.c", "output": "test/CMakeFiles/test_ptrlist.dir/test-ptrlist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DFONTFILE=\\\"/Users/<USER>/work/fontconfig/test/4x6.pcf\\\" -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_bz106632.dir/test-bz106632.c.o -c /Users/<USER>/work/fontconfig/test/test-bz106632.c", "file": "/Users/<USER>/work/fontconfig/test/test-bz106632.c", "output": "test/CMakeFiles/test_bz106632.dir/test-bz106632.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_issue107.dir/test-issue107.c.o -c /Users/<USER>/work/fontconfig/test/test-issue107.c", "file": "/Users/<USER>/work/fontconfig/test/test-issue107.c", "output": "test/CMakeFiles/test_issue107.dir/test-issue107.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_crbug1004254.dir/test-crbug1004254.c.o -c /Users/<USER>/work/fontconfig/test/test-crbug1004254.c", "file": "/Users/<USER>/work/fontconfig/test/test-crbug1004254.c", "output": "test/CMakeFiles/test_crbug1004254.dir/test-crbug1004254.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/src -g -std=gnu11 -Wno-excess-initializers -o test/CMakeFiles/test_mt_fccfg.dir/test-mt-fccfg.c.o -c /Users/<USER>/work/fontconfig/test/test-mt-fccfg.c", "file": "/Users/<USER>/work/fontconfig/test/test-mt-fccfg.c", "output": "test/CMakeFiles/test_mt_fccfg.dir/test-mt-fccfg.c.o"}]