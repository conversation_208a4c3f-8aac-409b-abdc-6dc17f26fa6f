# CMake generated Testfile for 
# Source directory: /Users/<USER>/work/fontconfig/test
# Build directory: /Users/<USER>/work/fontconfig/build/test
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(test-bz89617 "/Users/<USER>/work/fontconfig/build/test/test_bz89617")
set_tests_properties(test-bz89617 PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-bz131804 "/Users/<USER>/work/fontconfig/build/test/test_bz131804")
set_tests_properties(test-bz131804 PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-bz96676 "/Users/<USER>/work/fontconfig/build/test/test_bz96676")
set_tests_properties(test-bz96676 PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-name-parse "/Users/<USER>/work/fontconfig/build/test/test_name_parse")
set_tests_properties(test-name-parse PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-bz106618 "/Users/<USER>/work/fontconfig/build/test/test_bz106618")
set_tests_properties(test-bz106618 PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-bz1744377 "/Users/<USER>/work/fontconfig/build/test/test_bz1744377")
set_tests_properties(test-bz1744377 PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-issue180 "/Users/<USER>/work/fontconfig/build/test/test_issue180")
set_tests_properties(test-issue180 PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-family-matching "/Users/<USER>/work/fontconfig/build/test/test_family_matching")
set_tests_properties(test-family-matching PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-ptrlist "/Users/<USER>/work/fontconfig/build/test/test_ptrlist")
set_tests_properties(test-ptrlist PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-bz106632 "/Users/<USER>/work/fontconfig/build/test/test_bz106632")
set_tests_properties(test-bz106632 PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-issue107 "/Users/<USER>/work/fontconfig/build/test/test_issue107")
set_tests_properties(test-issue107 PROPERTIES  TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;85;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-crbug1004254 "/Users/<USER>/work/fontconfig/build/test/test_crbug1004254")
set_tests_properties(test-crbug1004254 PROPERTIES  RUN_SERIAL "TRUE" TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;119;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(test-mt-fccfg "/Users/<USER>/work/fontconfig/build/test/test_mt_fccfg")
set_tests_properties(test-mt-fccfg PROPERTIES  RUN_SERIAL "TRUE" TIMEOUT "600" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;119;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
add_test(run_test_sh "/Users/<USER>/work/fontconfig/test/run-test.sh")
set_tests_properties(run_test_sh PROPERTIES  ENVIRONMENT "srcdir=/Users/<USER>/work/fontconfig/test;builddir=/Users/<USER>/work/fontconfig/build/test;EXEEXT=;VERBOSE=1" TIMEOUT "600" WORKING_DIRECTORY "/Users/<USER>/work/fontconfig/build/test" _BACKTRACE_TRIPLES "/Users/<USER>/work/fontconfig/test/CMakeLists.txt;144;add_test;/Users/<USER>/work/fontconfig/test/CMakeLists.txt;0;")
