/* config.h.in - Configuration header template for CMake */

/* Package information */
#define PACKAGE_NAME "fontconfig"
#define PACKAGE_TARNAME "fontconfig"
#define PACKAGE_VERSION "2.17.1"
#define PACKAGE_STRING "fontconfig 2.17.1"
#define PACKAGE_BUGREPORT "https://gitlab.freedesktop.org/fontconfig/fontconfig/issues/new"
#define PACKAGE_URL ""

/* Version information */
#define VERSION "2.17.1"
#define CACHE_VERSION "9"

/* Paths */
#define CONFIGDIR "/usr/local/etc/fonts/conf.d"
#define FC_CACHEDIR "/usr/local/var/cache/fontconfig"
#define FC_TEMPLATEDIR "/usr/local/share/fontconfig/conf.avail"
#define FONTCONFIG_PATH "/usr/local/etc/fonts"
#define FC_DEFAULT_FONTS "\t<dir>/System/Library/Fonts</dir>\n\t<dir>/Library/Fonts</dir>\n\t<dir>~/Library/Fonts</dir>\n\t<dir>/System/Library/Assets/com_apple_MobileAsset_Font3</dir>\n\t<dir>/System/Library/Assets/com_apple_MobileAsset_Font4</dir>\n"
#define FC_FONTPATH ""

/* Features */
/* #undef ENABLE_LIBXML2 */
/* #undef ENABLE_NLS */
/* #undef USE_ICONV */
#define HAVE_PTHREAD 1
/* #undef ENABLE_FONTATIONS */

/* System characteristics */
/* #undef WORDS_BIGENDIAN */
#define EXEEXT ""

/* Headers */
#define HAVE_DIRENT_H 1
#define HAVE_DLFCN_H 1
#define HAVE_FCNTL_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_STDIO_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRINGS_H 1
#define HAVE_STRING_H 1
#define HAVE_UNISTD_H 1
#define HAVE_SYS_STATVFS_H 1
/* #undef HAVE_SYS_VFS_H */
/* #undef HAVE_SYS_STATFS_H */
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_SYS_PARAM_H 1
#define HAVE_SYS_MOUNT_H 1
#define HAVE_TIME_H 1
#define HAVE_WCHAR_H 1

/* Functions */
#define HAVE_LINK 1
#define HAVE_MKSTEMP 1
#define HAVE_MKOSTEMP 1
/* #undef HAVE__MKTEMP_S */
#define HAVE_MKDTEMP 1
#define HAVE_GETOPT 1
#define HAVE_GETOPT_LONG 1
#define HAVE_GETPROGNAME 1
/* #undef HAVE_GETEXECNAME */
#define HAVE_RAND 1
#define HAVE_RANDOM 1
#define HAVE_LRAND48 1
/* #undef HAVE_RANDOM_R */
#define HAVE_RAND_R 1
#define HAVE_READLINK 1
#define HAVE_FSTATVFS 1
#define HAVE_FSTATFS 1
#define HAVE_LSTAT 1
#define HAVE_STRERROR 1
#define HAVE_STRERROR_R 1
#define HAVE_MMAP 1
#define HAVE_VPRINTF 1
#define HAVE_VSNPRINTF 1
#define HAVE_VSPRINTF 1
#define HAVE_GETPAGESIZE 1
#define HAVE_GETPID 1
/* #undef HAVE_DCGETTEXT */
/* #undef HAVE_GETTEXT */
#define HAVE_LOCALTIME_R 1

/* FreeType functions */
#define HAVE_FT_GET_BDF_PROPERTY 1
#define HAVE_FT_GET_PS_FONT_INFO 1
#define HAVE_FT_HAS_PS_GLYPH_NAMES 1
#define HAVE_FT_GET_X11_FONT_FORMAT 1
#define HAVE_FT_DONE_MM_VAR 1

/* Header symbols */
/* #undef HAVE_POSIX_FADVISE */

/* Struct members */
/* #undef HAVE_STRUCT_STATVFS_F_BASETYPE */
/* #undef HAVE_STRUCT_STATVFS_F_FSTYPENAME */
/* #undef HAVE_STRUCT_STATFS_F_FLAGS */
/* #undef HAVE_STRUCT_STATFS_F_FSTYPENAME */
/* #undef HAVE_STRUCT_STAT_ST_MTIM */
#define HAVE_STRUCT_DIRENT_D_TYPE 1

/* Type sizes and alignments */
#define SIZEOF_VOID_P 8
#define ALIGNOF_VOID_P 8
#define ALIGNOF_DOUBLE 8

/* Compiler features */
#define FLEXIBLE_ARRAY_MEMBER 
#define HAVE_PTHREAD_PRIO_INHERIT 1
#define HAVE_STDATOMIC_PRIMITIVES 1
#define HAVE_INTEL_ATOMIC_PRIMITIVES 1
/* #undef HAVE_SOLARIS_ATOMIC_OPS */

/* FreeType features */
#define FREETYPE_PCF_LONG_FAMILY_NAMES 1

/* gperf configuration */
#define FC_GPERF_SIZE_T unsigned

/* Other defines */
#define _GNU_SOURCE 1
#define GETTEXT_PACKAGE "fontconfig"

#include "config-fixups.h"
