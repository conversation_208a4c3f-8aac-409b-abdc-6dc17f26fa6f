# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: fontconfig
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/work/fontconfig/build/

#############################################
# Utility command for test

build CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build test: phony CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/work/fontconfig/build && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/work/fontconfig/build && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/work/fontconfig/build && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for test

build fontconfig/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fontconfig && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fontconfig/test: phony fontconfig/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fontconfig/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fontconfig && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fontconfig/edit_cache: phony fontconfig/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fontconfig/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fontconfig && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fontconfig/rebuild_cache: phony fontconfig/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fontconfig/list_install_components: phony


#############################################
# Utility command for install

build fontconfig/CMakeFiles/install.util: CUSTOM_COMMAND fontconfig/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fontconfig && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fontconfig/install: phony fontconfig/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fontconfig/CMakeFiles/install/local.util: CUSTOM_COMMAND fontconfig/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fontconfig && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fontconfig/install/local: phony fontconfig/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fontconfig/CMakeFiles/install/strip.util: CUSTOM_COMMAND fontconfig/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fontconfig && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fontconfig/install/strip: phony fontconfig/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for fccase_h

build fc-case/fccase_h: phony fc-case/CMakeFiles/fccase_h fc-case/fccase.h


#############################################
# Utility command for test

build fc-case/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-case && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-case/test: phony fc-case/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-case/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-case && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-case/edit_cache: phony fc-case/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-case/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-case && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-case/rebuild_cache: phony fc-case/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-case/list_install_components: phony


#############################################
# Utility command for install

build fc-case/CMakeFiles/install.util: CUSTOM_COMMAND fc-case/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-case && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-case/install: phony fc-case/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-case/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-case/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-case && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-case/install/local: phony fc-case/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-case/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-case/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-case && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-case/install/strip: phony fc-case/CMakeFiles/install/strip.util


#############################################
# Phony custom command for fc-case/CMakeFiles/fccase_h

build fc-case/CMakeFiles/fccase_h | ${cmake_ninja_workdir}fc-case/CMakeFiles/fccase_h: phony fc-case/fccase.h


#############################################
# Custom command for fc-case/fccase.h

build fc-case/fccase.h | ${cmake_ninja_workdir}fc-case/fccase.h: CUSTOM_COMMAND /Users/<USER>/work/fontconfig/fc-case/CaseFolding.txt /Users/<USER>/work/fontconfig/fc-case/fccase.tmpl.h /Users/<USER>/work/fontconfig/fc-case/fc-case.py
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-case && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/fc-case/fc-case.py /Users/<USER>/work/fontconfig/fc-case/CaseFolding.txt --template /Users/<USER>/work/fontconfig/fc-case/fccase.tmpl.h --output /Users/<USER>/work/fontconfig/build/fc-case/fccase.h
  DESC = Generating fccase.h
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for fclang_h

build fc-lang/fclang_h: phony fc-lang/CMakeFiles/fclang_h fc-lang/fclang.h


#############################################
# Utility command for test

build fc-lang/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-lang && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-lang/test: phony fc-lang/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-lang/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-lang && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-lang/edit_cache: phony fc-lang/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-lang/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-lang && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-lang/rebuild_cache: phony fc-lang/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-lang/list_install_components: phony


#############################################
# Utility command for install

build fc-lang/CMakeFiles/install.util: CUSTOM_COMMAND fc-lang/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-lang && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-lang/install: phony fc-lang/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-lang/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-lang/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-lang && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-lang/install/local: phony fc-lang/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-lang/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-lang/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-lang && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-lang/install/strip: phony fc-lang/CMakeFiles/install/strip.util


#############################################
# Phony custom command for fc-lang/CMakeFiles/fclang_h

build fc-lang/CMakeFiles/fclang_h | ${cmake_ninja_workdir}fc-lang/CMakeFiles/fclang_h: phony fc-lang/fclang.h


#############################################
# Custom command for fc-lang/fclang.h

build fc-lang/fclang.h | ${cmake_ninja_workdir}fc-lang/fclang.h: CUSTOM_COMMAND /Users/<USER>/work/fontconfig/fc-lang/aa.orth /Users/<USER>/work/fontconfig/fc-lang/ab.orth /Users/<USER>/work/fontconfig/fc-lang/af.orth /Users/<USER>/work/fontconfig/fc-lang/am.orth /Users/<USER>/work/fontconfig/fc-lang/ar.orth /Users/<USER>/work/fontconfig/fc-lang/as.orth /Users/<USER>/work/fontconfig/fc-lang/ast.orth /Users/<USER>/work/fontconfig/fc-lang/av.orth /Users/<USER>/work/fontconfig/fc-lang/ay.orth /Users/<USER>/work/fontconfig/fc-lang/az_az.orth /Users/<USER>/work/fontconfig/fc-lang/az_ir.orth /Users/<USER>/work/fontconfig/fc-lang/ba.orth /Users/<USER>/work/fontconfig/fc-lang/bm.orth /Users/<USER>/work/fontconfig/fc-lang/be.orth /Users/<USER>/work/fontconfig/fc-lang/bg.orth /Users/<USER>/work/fontconfig/fc-lang/bh.orth /Users/<USER>/work/fontconfig/fc-lang/bho.orth /Users/<USER>/work/fontconfig/fc-lang/bi.orth /Users/<USER>/work/fontconfig/fc-lang/bin.orth /Users/<USER>/work/fontconfig/fc-lang/bn.orth /Users/<USER>/work/fontconfig/fc-lang/bo.orth /Users/<USER>/work/fontconfig/fc-lang/br.orth /Users/<USER>/work/fontconfig/fc-lang/bs.orth /Users/<USER>/work/fontconfig/fc-lang/bua.orth /Users/<USER>/work/fontconfig/fc-lang/ca.orth /Users/<USER>/work/fontconfig/fc-lang/ce.orth /Users/<USER>/work/fontconfig/fc-lang/ch.orth /Users/<USER>/work/fontconfig/fc-lang/chm.orth /Users/<USER>/work/fontconfig/fc-lang/chr.orth /Users/<USER>/work/fontconfig/fc-lang/co.orth /Users/<USER>/work/fontconfig/fc-lang/cs.orth /Users/<USER>/work/fontconfig/fc-lang/cu.orth /Users/<USER>/work/fontconfig/fc-lang/cv.orth /Users/<USER>/work/fontconfig/fc-lang/cy.orth /Users/<USER>/work/fontconfig/fc-lang/da.orth /Users/<USER>/work/fontconfig/fc-lang/de.orth /Users/<USER>/work/fontconfig/fc-lang/dz.orth /Users/<USER>/work/fontconfig/fc-lang/el.orth /Users/<USER>/work/fontconfig/fc-lang/en.orth /Users/<USER>/work/fontconfig/fc-lang/eo.orth /Users/<USER>/work/fontconfig/fc-lang/es.orth /Users/<USER>/work/fontconfig/fc-lang/et.orth /Users/<USER>/work/fontconfig/fc-lang/eu.orth /Users/<USER>/work/fontconfig/fc-lang/fa.orth /Users/<USER>/work/fontconfig/fc-lang/fi.orth /Users/<USER>/work/fontconfig/fc-lang/fj.orth /Users/<USER>/work/fontconfig/fc-lang/fo.orth /Users/<USER>/work/fontconfig/fc-lang/fr.orth /Users/<USER>/work/fontconfig/fc-lang/ff.orth /Users/<USER>/work/fontconfig/fc-lang/fur.orth /Users/<USER>/work/fontconfig/fc-lang/fy.orth /Users/<USER>/work/fontconfig/fc-lang/ga.orth /Users/<USER>/work/fontconfig/fc-lang/gd.orth /Users/<USER>/work/fontconfig/fc-lang/gez.orth /Users/<USER>/work/fontconfig/fc-lang/gl.orth /Users/<USER>/work/fontconfig/fc-lang/gn.orth /Users/<USER>/work/fontconfig/fc-lang/gu.orth /Users/<USER>/work/fontconfig/fc-lang/gv.orth /Users/<USER>/work/fontconfig/fc-lang/ha.orth /Users/<USER>/work/fontconfig/fc-lang/haw.orth /Users/<USER>/work/fontconfig/fc-lang/he.orth /Users/<USER>/work/fontconfig/fc-lang/hi.orth /Users/<USER>/work/fontconfig/fc-lang/ho.orth /Users/<USER>/work/fontconfig/fc-lang/hr.orth /Users/<USER>/work/fontconfig/fc-lang/hu.orth /Users/<USER>/work/fontconfig/fc-lang/hy.orth /Users/<USER>/work/fontconfig/fc-lang/ia.orth /Users/<USER>/work/fontconfig/fc-lang/ig.orth /Users/<USER>/work/fontconfig/fc-lang/id.orth /Users/<USER>/work/fontconfig/fc-lang/ie.orth /Users/<USER>/work/fontconfig/fc-lang/ik.orth /Users/<USER>/work/fontconfig/fc-lang/io.orth /Users/<USER>/work/fontconfig/fc-lang/is.orth /Users/<USER>/work/fontconfig/fc-lang/it.orth /Users/<USER>/work/fontconfig/fc-lang/iu.orth /Users/<USER>/work/fontconfig/fc-lang/ja.orth /Users/<USER>/work/fontconfig/fc-lang/ka.orth /Users/<USER>/work/fontconfig/fc-lang/kaa.orth /Users/<USER>/work/fontconfig/fc-lang/ki.orth /Users/<USER>/work/fontconfig/fc-lang/kk.orth /Users/<USER>/work/fontconfig/fc-lang/kl.orth /Users/<USER>/work/fontconfig/fc-lang/km.orth /Users/<USER>/work/fontconfig/fc-lang/kn.orth /Users/<USER>/work/fontconfig/fc-lang/ko.orth /Users/<USER>/work/fontconfig/fc-lang/kok.orth /Users/<USER>/work/fontconfig/fc-lang/ks.orth /Users/<USER>/work/fontconfig/fc-lang/ku_am.orth /Users/<USER>/work/fontconfig/fc-lang/ku_ir.orth /Users/<USER>/work/fontconfig/fc-lang/kum.orth /Users/<USER>/work/fontconfig/fc-lang/kv.orth /Users/<USER>/work/fontconfig/fc-lang/kw.orth /Users/<USER>/work/fontconfig/fc-lang/ky.orth /Users/<USER>/work/fontconfig/fc-lang/la.orth /Users/<USER>/work/fontconfig/fc-lang/lb.orth /Users/<USER>/work/fontconfig/fc-lang/lez.orth /Users/<USER>/work/fontconfig/fc-lang/ln.orth /Users/<USER>/work/fontconfig/fc-lang/lo.orth /Users/<USER>/work/fontconfig/fc-lang/lt.orth /Users/<USER>/work/fontconfig/fc-lang/lv.orth /Users/<USER>/work/fontconfig/fc-lang/mg.orth /Users/<USER>/work/fontconfig/fc-lang/mh.orth /Users/<USER>/work/fontconfig/fc-lang/mi.orth /Users/<USER>/work/fontconfig/fc-lang/mk.orth /Users/<USER>/work/fontconfig/fc-lang/ml.orth /Users/<USER>/work/fontconfig/fc-lang/mn_cn.orth /Users/<USER>/work/fontconfig/fc-lang/mo.orth /Users/<USER>/work/fontconfig/fc-lang/mr.orth /Users/<USER>/work/fontconfig/fc-lang/mt.orth /Users/<USER>/work/fontconfig/fc-lang/my.orth /Users/<USER>/work/fontconfig/fc-lang/nb.orth /Users/<USER>/work/fontconfig/fc-lang/nds.orth /Users/<USER>/work/fontconfig/fc-lang/ne.orth /Users/<USER>/work/fontconfig/fc-lang/nl.orth /Users/<USER>/work/fontconfig/fc-lang/nn.orth /Users/<USER>/work/fontconfig/fc-lang/no.orth /Users/<USER>/work/fontconfig/fc-lang/nr.orth /Users/<USER>/work/fontconfig/fc-lang/nso.orth /Users/<USER>/work/fontconfig/fc-lang/ny.orth /Users/<USER>/work/fontconfig/fc-lang/oc.orth /Users/<USER>/work/fontconfig/fc-lang/om.orth /Users/<USER>/work/fontconfig/fc-lang/or.orth /Users/<USER>/work/fontconfig/fc-lang/os.orth /Users/<USER>/work/fontconfig/fc-lang/pa.orth /Users/<USER>/work/fontconfig/fc-lang/pl.orth /Users/<USER>/work/fontconfig/fc-lang/ps_af.orth /Users/<USER>/work/fontconfig/fc-lang/ps_pk.orth /Users/<USER>/work/fontconfig/fc-lang/pt.orth /Users/<USER>/work/fontconfig/fc-lang/rm.orth /Users/<USER>/work/fontconfig/fc-lang/ro.orth /Users/<USER>/work/fontconfig/fc-lang/ru.orth /Users/<USER>/work/fontconfig/fc-lang/sa.orth /Users/<USER>/work/fontconfig/fc-lang/sah.orth /Users/<USER>/work/fontconfig/fc-lang/sco.orth /Users/<USER>/work/fontconfig/fc-lang/se.orth /Users/<USER>/work/fontconfig/fc-lang/sel.orth /Users/<USER>/work/fontconfig/fc-lang/sh.orth /Users/<USER>/work/fontconfig/fc-lang/shs.orth /Users/<USER>/work/fontconfig/fc-lang/si.orth /Users/<USER>/work/fontconfig/fc-lang/sk.orth /Users/<USER>/work/fontconfig/fc-lang/sl.orth /Users/<USER>/work/fontconfig/fc-lang/sm.orth /Users/<USER>/work/fontconfig/fc-lang/sma.orth /Users/<USER>/work/fontconfig/fc-lang/smj.orth /Users/<USER>/work/fontconfig/fc-lang/smn.orth /Users/<USER>/work/fontconfig/fc-lang/sms.orth /Users/<USER>/work/fontconfig/fc-lang/so.orth /Users/<USER>/work/fontconfig/fc-lang/sq.orth /Users/<USER>/work/fontconfig/fc-lang/sr.orth /Users/<USER>/work/fontconfig/fc-lang/ss.orth /Users/<USER>/work/fontconfig/fc-lang/st.orth /Users/<USER>/work/fontconfig/fc-lang/sv.orth /Users/<USER>/work/fontconfig/fc-lang/sw.orth /Users/<USER>/work/fontconfig/fc-lang/syr.orth /Users/<USER>/work/fontconfig/fc-lang/ta.orth /Users/<USER>/work/fontconfig/fc-lang/te.orth /Users/<USER>/work/fontconfig/fc-lang/tg.orth /Users/<USER>/work/fontconfig/fc-lang/th.orth /Users/<USER>/work/fontconfig/fc-lang/ti_er.orth /Users/<USER>/work/fontconfig/fc-lang/ti_et.orth /Users/<USER>/work/fontconfig/fc-lang/tig.orth /Users/<USER>/work/fontconfig/fc-lang/tk.orth /Users/<USER>/work/fontconfig/fc-lang/tl.orth /Users/<USER>/work/fontconfig/fc-lang/tn.orth /Users/<USER>/work/fontconfig/fc-lang/to.orth /Users/<USER>/work/fontconfig/fc-lang/tr.orth /Users/<USER>/work/fontconfig/fc-lang/ts.orth /Users/<USER>/work/fontconfig/fc-lang/tt.orth /Users/<USER>/work/fontconfig/fc-lang/tw.orth /Users/<USER>/work/fontconfig/fc-lang/tyv.orth /Users/<USER>/work/fontconfig/fc-lang/ug.orth /Users/<USER>/work/fontconfig/fc-lang/uk.orth /Users/<USER>/work/fontconfig/fc-lang/ur.orth /Users/<USER>/work/fontconfig/fc-lang/uz.orth /Users/<USER>/work/fontconfig/fc-lang/ve.orth /Users/<USER>/work/fontconfig/fc-lang/vi.orth /Users/<USER>/work/fontconfig/fc-lang/vo.orth /Users/<USER>/work/fontconfig/fc-lang/vot.orth /Users/<USER>/work/fontconfig/fc-lang/wa.orth /Users/<USER>/work/fontconfig/fc-lang/wen.orth /Users/<USER>/work/fontconfig/fc-lang/wo.orth /Users/<USER>/work/fontconfig/fc-lang/xh.orth /Users/<USER>/work/fontconfig/fc-lang/yap.orth /Users/<USER>/work/fontconfig/fc-lang/yi.orth /Users/<USER>/work/fontconfig/fc-lang/yo.orth /Users/<USER>/work/fontconfig/fc-lang/zh_cn.orth /Users/<USER>/work/fontconfig/fc-lang/zh_hk.orth /Users/<USER>/work/fontconfig/fc-lang/zh_mo.orth /Users/<USER>/work/fontconfig/fc-lang/zh_sg.orth /Users/<USER>/work/fontconfig/fc-lang/zh_tw.orth /Users/<USER>/work/fontconfig/fc-lang/zu.orth /Users/<USER>/work/fontconfig/fc-lang/ak.orth /Users/<USER>/work/fontconfig/fc-lang/an.orth /Users/<USER>/work/fontconfig/fc-lang/ber_dz.orth /Users/<USER>/work/fontconfig/fc-lang/ber_ma.orth /Users/<USER>/work/fontconfig/fc-lang/byn.orth /Users/<USER>/work/fontconfig/fc-lang/crh.orth /Users/<USER>/work/fontconfig/fc-lang/csb.orth /Users/<USER>/work/fontconfig/fc-lang/dv.orth /Users/<USER>/work/fontconfig/fc-lang/ee.orth /Users/<USER>/work/fontconfig/fc-lang/fat.orth /Users/<USER>/work/fontconfig/fc-lang/fil.orth /Users/<USER>/work/fontconfig/fc-lang/hne.orth /Users/<USER>/work/fontconfig/fc-lang/hsb.orth /Users/<USER>/work/fontconfig/fc-lang/ht.orth /Users/<USER>/work/fontconfig/fc-lang/hz.orth /Users/<USER>/work/fontconfig/fc-lang/ii.orth /Users/<USER>/work/fontconfig/fc-lang/jv.orth /Users/<USER>/work/fontconfig/fc-lang/kab.orth /Users/<USER>/work/fontconfig/fc-lang/kj.orth /Users/<USER>/work/fontconfig/fc-lang/kr.orth /Users/<USER>/work/fontconfig/fc-lang/ku_iq.orth /Users/<USER>/work/fontconfig/fc-lang/ku_tr.orth /Users/<USER>/work/fontconfig/fc-lang/kwm.orth /Users/<USER>/work/fontconfig/fc-lang/lg.orth /Users/<USER>/work/fontconfig/fc-lang/li.orth /Users/<USER>/work/fontconfig/fc-lang/mai.orth /Users/<USER>/work/fontconfig/fc-lang/mn_mn.orth /Users/<USER>/work/fontconfig/fc-lang/ms.orth /Users/<USER>/work/fontconfig/fc-lang/na.orth /Users/<USER>/work/fontconfig/fc-lang/ng.orth /Users/<USER>/work/fontconfig/fc-lang/nv.orth /Users/<USER>/work/fontconfig/fc-lang/ota.orth /Users/<USER>/work/fontconfig/fc-lang/pa_pk.orth /Users/<USER>/work/fontconfig/fc-lang/pap_an.orth /Users/<USER>/work/fontconfig/fc-lang/pap_aw.orth /Users/<USER>/work/fontconfig/fc-lang/qu.orth /Users/<USER>/work/fontconfig/fc-lang/quz.orth /Users/<USER>/work/fontconfig/fc-lang/rn.orth /Users/<USER>/work/fontconfig/fc-lang/rw.orth /Users/<USER>/work/fontconfig/fc-lang/sc.orth /Users/<USER>/work/fontconfig/fc-lang/sd.orth /Users/<USER>/work/fontconfig/fc-lang/sg.orth /Users/<USER>/work/fontconfig/fc-lang/sid.orth /Users/<USER>/work/fontconfig/fc-lang/sn.orth /Users/<USER>/work/fontconfig/fc-lang/su.orth /Users/<USER>/work/fontconfig/fc-lang/ty.orth /Users/<USER>/work/fontconfig/fc-lang/wal.orth /Users/<USER>/work/fontconfig/fc-lang/za.orth /Users/<USER>/work/fontconfig/fc-lang/lah.orth /Users/<USER>/work/fontconfig/fc-lang/nqo.orth /Users/<USER>/work/fontconfig/fc-lang/brx.orth /Users/<USER>/work/fontconfig/fc-lang/sat.orth /Users/<USER>/work/fontconfig/fc-lang/doi.orth /Users/<USER>/work/fontconfig/fc-lang/mni.orth /Users/<USER>/work/fontconfig/fc-lang/und_zsye.orth /Users/<USER>/work/fontconfig/fc-lang/und_zmth.orth /Users/<USER>/work/fontconfig/fc-lang/anp.orth /Users/<USER>/work/fontconfig/fc-lang/bhb.orth /Users/<USER>/work/fontconfig/fc-lang/hif.orth /Users/<USER>/work/fontconfig/fc-lang/mag.orth /Users/<USER>/work/fontconfig/fc-lang/raj.orth /Users/<USER>/work/fontconfig/fc-lang/the.orth /Users/<USER>/work/fontconfig/fc-lang/agr.orth /Users/<USER>/work/fontconfig/fc-lang/ayc.orth /Users/<USER>/work/fontconfig/fc-lang/bem.orth /Users/<USER>/work/fontconfig/fc-lang/ckb.orth /Users/<USER>/work/fontconfig/fc-lang/cmn.orth /Users/<USER>/work/fontconfig/fc-lang/dsb.orth /Users/<USER>/work/fontconfig/fc-lang/hak.orth /Users/<USER>/work/fontconfig/fc-lang/lij.orth /Users/<USER>/work/fontconfig/fc-lang/lzh.orth /Users/<USER>/work/fontconfig/fc-lang/mfe.orth /Users/<USER>/work/fontconfig/fc-lang/mhr.orth /Users/<USER>/work/fontconfig/fc-lang/miq.orth /Users/<USER>/work/fontconfig/fc-lang/mjw.orth /Users/<USER>/work/fontconfig/fc-lang/mnw.orth /Users/<USER>/work/fontconfig/fc-lang/nan.orth /Users/<USER>/work/fontconfig/fc-lang/nhn.orth /Users/<USER>/work/fontconfig/fc-lang/niu.orth /Users/<USER>/work/fontconfig/fc-lang/rif.orth /Users/<USER>/work/fontconfig/fc-lang/sgs.orth /Users/<USER>/work/fontconfig/fc-lang/shn.orth /Users/<USER>/work/fontconfig/fc-lang/szl.orth /Users/<USER>/work/fontconfig/fc-lang/tcy.orth /Users/<USER>/work/fontconfig/fc-lang/tpi.orth /Users/<USER>/work/fontconfig/fc-lang/unm.orth /Users/<USER>/work/fontconfig/fc-lang/wae.orth /Users/<USER>/work/fontconfig/fc-lang/yue.orth /Users/<USER>/work/fontconfig/fc-lang/yuw.orth /Users/<USER>/work/fontconfig/fc-lang/got.orth /Users/<USER>/work/fontconfig/fc-lang/cop.orth /Users/<USER>/work/fontconfig/fc-lang/fclang.tmpl.h /Users/<USER>/work/fontconfig/fc-lang/fc-lang.py
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-lang && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/fc-lang/fc-lang.py /Users/<USER>/work/fontconfig/fc-lang/aa.orth /Users/<USER>/work/fontconfig/fc-lang/ab.orth /Users/<USER>/work/fontconfig/fc-lang/af.orth /Users/<USER>/work/fontconfig/fc-lang/am.orth /Users/<USER>/work/fontconfig/fc-lang/ar.orth /Users/<USER>/work/fontconfig/fc-lang/as.orth /Users/<USER>/work/fontconfig/fc-lang/ast.orth /Users/<USER>/work/fontconfig/fc-lang/av.orth /Users/<USER>/work/fontconfig/fc-lang/ay.orth /Users/<USER>/work/fontconfig/fc-lang/az_az.orth /Users/<USER>/work/fontconfig/fc-lang/az_ir.orth /Users/<USER>/work/fontconfig/fc-lang/ba.orth /Users/<USER>/work/fontconfig/fc-lang/bm.orth /Users/<USER>/work/fontconfig/fc-lang/be.orth /Users/<USER>/work/fontconfig/fc-lang/bg.orth /Users/<USER>/work/fontconfig/fc-lang/bh.orth /Users/<USER>/work/fontconfig/fc-lang/bho.orth /Users/<USER>/work/fontconfig/fc-lang/bi.orth /Users/<USER>/work/fontconfig/fc-lang/bin.orth /Users/<USER>/work/fontconfig/fc-lang/bn.orth /Users/<USER>/work/fontconfig/fc-lang/bo.orth /Users/<USER>/work/fontconfig/fc-lang/br.orth /Users/<USER>/work/fontconfig/fc-lang/bs.orth /Users/<USER>/work/fontconfig/fc-lang/bua.orth /Users/<USER>/work/fontconfig/fc-lang/ca.orth /Users/<USER>/work/fontconfig/fc-lang/ce.orth /Users/<USER>/work/fontconfig/fc-lang/ch.orth /Users/<USER>/work/fontconfig/fc-lang/chm.orth /Users/<USER>/work/fontconfig/fc-lang/chr.orth /Users/<USER>/work/fontconfig/fc-lang/co.orth /Users/<USER>/work/fontconfig/fc-lang/cs.orth /Users/<USER>/work/fontconfig/fc-lang/cu.orth /Users/<USER>/work/fontconfig/fc-lang/cv.orth /Users/<USER>/work/fontconfig/fc-lang/cy.orth /Users/<USER>/work/fontconfig/fc-lang/da.orth /Users/<USER>/work/fontconfig/fc-lang/de.orth /Users/<USER>/work/fontconfig/fc-lang/dz.orth /Users/<USER>/work/fontconfig/fc-lang/el.orth /Users/<USER>/work/fontconfig/fc-lang/en.orth /Users/<USER>/work/fontconfig/fc-lang/eo.orth /Users/<USER>/work/fontconfig/fc-lang/es.orth /Users/<USER>/work/fontconfig/fc-lang/et.orth /Users/<USER>/work/fontconfig/fc-lang/eu.orth /Users/<USER>/work/fontconfig/fc-lang/fa.orth /Users/<USER>/work/fontconfig/fc-lang/fi.orth /Users/<USER>/work/fontconfig/fc-lang/fj.orth /Users/<USER>/work/fontconfig/fc-lang/fo.orth /Users/<USER>/work/fontconfig/fc-lang/fr.orth /Users/<USER>/work/fontconfig/fc-lang/ff.orth /Users/<USER>/work/fontconfig/fc-lang/fur.orth /Users/<USER>/work/fontconfig/fc-lang/fy.orth /Users/<USER>/work/fontconfig/fc-lang/ga.orth /Users/<USER>/work/fontconfig/fc-lang/gd.orth /Users/<USER>/work/fontconfig/fc-lang/gez.orth /Users/<USER>/work/fontconfig/fc-lang/gl.orth /Users/<USER>/work/fontconfig/fc-lang/gn.orth /Users/<USER>/work/fontconfig/fc-lang/gu.orth /Users/<USER>/work/fontconfig/fc-lang/gv.orth /Users/<USER>/work/fontconfig/fc-lang/ha.orth /Users/<USER>/work/fontconfig/fc-lang/haw.orth /Users/<USER>/work/fontconfig/fc-lang/he.orth /Users/<USER>/work/fontconfig/fc-lang/hi.orth /Users/<USER>/work/fontconfig/fc-lang/ho.orth /Users/<USER>/work/fontconfig/fc-lang/hr.orth /Users/<USER>/work/fontconfig/fc-lang/hu.orth /Users/<USER>/work/fontconfig/fc-lang/hy.orth /Users/<USER>/work/fontconfig/fc-lang/ia.orth /Users/<USER>/work/fontconfig/fc-lang/ig.orth /Users/<USER>/work/fontconfig/fc-lang/id.orth /Users/<USER>/work/fontconfig/fc-lang/ie.orth /Users/<USER>/work/fontconfig/fc-lang/ik.orth /Users/<USER>/work/fontconfig/fc-lang/io.orth /Users/<USER>/work/fontconfig/fc-lang/is.orth /Users/<USER>/work/fontconfig/fc-lang/it.orth /Users/<USER>/work/fontconfig/fc-lang/iu.orth /Users/<USER>/work/fontconfig/fc-lang/ja.orth /Users/<USER>/work/fontconfig/fc-lang/ka.orth /Users/<USER>/work/fontconfig/fc-lang/kaa.orth /Users/<USER>/work/fontconfig/fc-lang/ki.orth /Users/<USER>/work/fontconfig/fc-lang/kk.orth /Users/<USER>/work/fontconfig/fc-lang/kl.orth /Users/<USER>/work/fontconfig/fc-lang/km.orth /Users/<USER>/work/fontconfig/fc-lang/kn.orth /Users/<USER>/work/fontconfig/fc-lang/ko.orth /Users/<USER>/work/fontconfig/fc-lang/kok.orth /Users/<USER>/work/fontconfig/fc-lang/ks.orth /Users/<USER>/work/fontconfig/fc-lang/ku_am.orth /Users/<USER>/work/fontconfig/fc-lang/ku_ir.orth /Users/<USER>/work/fontconfig/fc-lang/kum.orth /Users/<USER>/work/fontconfig/fc-lang/kv.orth /Users/<USER>/work/fontconfig/fc-lang/kw.orth /Users/<USER>/work/fontconfig/fc-lang/ky.orth /Users/<USER>/work/fontconfig/fc-lang/la.orth /Users/<USER>/work/fontconfig/fc-lang/lb.orth /Users/<USER>/work/fontconfig/fc-lang/lez.orth /Users/<USER>/work/fontconfig/fc-lang/ln.orth /Users/<USER>/work/fontconfig/fc-lang/lo.orth /Users/<USER>/work/fontconfig/fc-lang/lt.orth /Users/<USER>/work/fontconfig/fc-lang/lv.orth /Users/<USER>/work/fontconfig/fc-lang/mg.orth /Users/<USER>/work/fontconfig/fc-lang/mh.orth /Users/<USER>/work/fontconfig/fc-lang/mi.orth /Users/<USER>/work/fontconfig/fc-lang/mk.orth /Users/<USER>/work/fontconfig/fc-lang/ml.orth /Users/<USER>/work/fontconfig/fc-lang/mn_cn.orth /Users/<USER>/work/fontconfig/fc-lang/mo.orth /Users/<USER>/work/fontconfig/fc-lang/mr.orth /Users/<USER>/work/fontconfig/fc-lang/mt.orth /Users/<USER>/work/fontconfig/fc-lang/my.orth /Users/<USER>/work/fontconfig/fc-lang/nb.orth /Users/<USER>/work/fontconfig/fc-lang/nds.orth /Users/<USER>/work/fontconfig/fc-lang/ne.orth /Users/<USER>/work/fontconfig/fc-lang/nl.orth /Users/<USER>/work/fontconfig/fc-lang/nn.orth /Users/<USER>/work/fontconfig/fc-lang/no.orth /Users/<USER>/work/fontconfig/fc-lang/nr.orth /Users/<USER>/work/fontconfig/fc-lang/nso.orth /Users/<USER>/work/fontconfig/fc-lang/ny.orth /Users/<USER>/work/fontconfig/fc-lang/oc.orth /Users/<USER>/work/fontconfig/fc-lang/om.orth /Users/<USER>/work/fontconfig/fc-lang/or.orth /Users/<USER>/work/fontconfig/fc-lang/os.orth /Users/<USER>/work/fontconfig/fc-lang/pa.orth /Users/<USER>/work/fontconfig/fc-lang/pl.orth /Users/<USER>/work/fontconfig/fc-lang/ps_af.orth /Users/<USER>/work/fontconfig/fc-lang/ps_pk.orth /Users/<USER>/work/fontconfig/fc-lang/pt.orth /Users/<USER>/work/fontconfig/fc-lang/rm.orth /Users/<USER>/work/fontconfig/fc-lang/ro.orth /Users/<USER>/work/fontconfig/fc-lang/ru.orth /Users/<USER>/work/fontconfig/fc-lang/sa.orth /Users/<USER>/work/fontconfig/fc-lang/sah.orth /Users/<USER>/work/fontconfig/fc-lang/sco.orth /Users/<USER>/work/fontconfig/fc-lang/se.orth /Users/<USER>/work/fontconfig/fc-lang/sel.orth /Users/<USER>/work/fontconfig/fc-lang/sh.orth /Users/<USER>/work/fontconfig/fc-lang/shs.orth /Users/<USER>/work/fontconfig/fc-lang/si.orth /Users/<USER>/work/fontconfig/fc-lang/sk.orth /Users/<USER>/work/fontconfig/fc-lang/sl.orth /Users/<USER>/work/fontconfig/fc-lang/sm.orth /Users/<USER>/work/fontconfig/fc-lang/sma.orth /Users/<USER>/work/fontconfig/fc-lang/smj.orth /Users/<USER>/work/fontconfig/fc-lang/smn.orth /Users/<USER>/work/fontconfig/fc-lang/sms.orth /Users/<USER>/work/fontconfig/fc-lang/so.orth /Users/<USER>/work/fontconfig/fc-lang/sq.orth /Users/<USER>/work/fontconfig/fc-lang/sr.orth /Users/<USER>/work/fontconfig/fc-lang/ss.orth /Users/<USER>/work/fontconfig/fc-lang/st.orth /Users/<USER>/work/fontconfig/fc-lang/sv.orth /Users/<USER>/work/fontconfig/fc-lang/sw.orth /Users/<USER>/work/fontconfig/fc-lang/syr.orth /Users/<USER>/work/fontconfig/fc-lang/ta.orth /Users/<USER>/work/fontconfig/fc-lang/te.orth /Users/<USER>/work/fontconfig/fc-lang/tg.orth /Users/<USER>/work/fontconfig/fc-lang/th.orth /Users/<USER>/work/fontconfig/fc-lang/ti_er.orth /Users/<USER>/work/fontconfig/fc-lang/ti_et.orth /Users/<USER>/work/fontconfig/fc-lang/tig.orth /Users/<USER>/work/fontconfig/fc-lang/tk.orth /Users/<USER>/work/fontconfig/fc-lang/tl.orth /Users/<USER>/work/fontconfig/fc-lang/tn.orth /Users/<USER>/work/fontconfig/fc-lang/to.orth /Users/<USER>/work/fontconfig/fc-lang/tr.orth /Users/<USER>/work/fontconfig/fc-lang/ts.orth /Users/<USER>/work/fontconfig/fc-lang/tt.orth /Users/<USER>/work/fontconfig/fc-lang/tw.orth /Users/<USER>/work/fontconfig/fc-lang/tyv.orth /Users/<USER>/work/fontconfig/fc-lang/ug.orth /Users/<USER>/work/fontconfig/fc-lang/uk.orth /Users/<USER>/work/fontconfig/fc-lang/ur.orth /Users/<USER>/work/fontconfig/fc-lang/uz.orth /Users/<USER>/work/fontconfig/fc-lang/ve.orth /Users/<USER>/work/fontconfig/fc-lang/vi.orth /Users/<USER>/work/fontconfig/fc-lang/vo.orth /Users/<USER>/work/fontconfig/fc-lang/vot.orth /Users/<USER>/work/fontconfig/fc-lang/wa.orth /Users/<USER>/work/fontconfig/fc-lang/wen.orth /Users/<USER>/work/fontconfig/fc-lang/wo.orth /Users/<USER>/work/fontconfig/fc-lang/xh.orth /Users/<USER>/work/fontconfig/fc-lang/yap.orth /Users/<USER>/work/fontconfig/fc-lang/yi.orth /Users/<USER>/work/fontconfig/fc-lang/yo.orth /Users/<USER>/work/fontconfig/fc-lang/zh_cn.orth /Users/<USER>/work/fontconfig/fc-lang/zh_hk.orth /Users/<USER>/work/fontconfig/fc-lang/zh_mo.orth /Users/<USER>/work/fontconfig/fc-lang/zh_sg.orth /Users/<USER>/work/fontconfig/fc-lang/zh_tw.orth /Users/<USER>/work/fontconfig/fc-lang/zu.orth /Users/<USER>/work/fontconfig/fc-lang/ak.orth /Users/<USER>/work/fontconfig/fc-lang/an.orth /Users/<USER>/work/fontconfig/fc-lang/ber_dz.orth /Users/<USER>/work/fontconfig/fc-lang/ber_ma.orth /Users/<USER>/work/fontconfig/fc-lang/byn.orth /Users/<USER>/work/fontconfig/fc-lang/crh.orth /Users/<USER>/work/fontconfig/fc-lang/csb.orth /Users/<USER>/work/fontconfig/fc-lang/dv.orth /Users/<USER>/work/fontconfig/fc-lang/ee.orth /Users/<USER>/work/fontconfig/fc-lang/fat.orth /Users/<USER>/work/fontconfig/fc-lang/fil.orth /Users/<USER>/work/fontconfig/fc-lang/hne.orth /Users/<USER>/work/fontconfig/fc-lang/hsb.orth /Users/<USER>/work/fontconfig/fc-lang/ht.orth /Users/<USER>/work/fontconfig/fc-lang/hz.orth /Users/<USER>/work/fontconfig/fc-lang/ii.orth /Users/<USER>/work/fontconfig/fc-lang/jv.orth /Users/<USER>/work/fontconfig/fc-lang/kab.orth /Users/<USER>/work/fontconfig/fc-lang/kj.orth /Users/<USER>/work/fontconfig/fc-lang/kr.orth /Users/<USER>/work/fontconfig/fc-lang/ku_iq.orth /Users/<USER>/work/fontconfig/fc-lang/ku_tr.orth /Users/<USER>/work/fontconfig/fc-lang/kwm.orth /Users/<USER>/work/fontconfig/fc-lang/lg.orth /Users/<USER>/work/fontconfig/fc-lang/li.orth /Users/<USER>/work/fontconfig/fc-lang/mai.orth /Users/<USER>/work/fontconfig/fc-lang/mn_mn.orth /Users/<USER>/work/fontconfig/fc-lang/ms.orth /Users/<USER>/work/fontconfig/fc-lang/na.orth /Users/<USER>/work/fontconfig/fc-lang/ng.orth /Users/<USER>/work/fontconfig/fc-lang/nv.orth /Users/<USER>/work/fontconfig/fc-lang/ota.orth /Users/<USER>/work/fontconfig/fc-lang/pa_pk.orth /Users/<USER>/work/fontconfig/fc-lang/pap_an.orth /Users/<USER>/work/fontconfig/fc-lang/pap_aw.orth /Users/<USER>/work/fontconfig/fc-lang/qu.orth /Users/<USER>/work/fontconfig/fc-lang/quz.orth /Users/<USER>/work/fontconfig/fc-lang/rn.orth /Users/<USER>/work/fontconfig/fc-lang/rw.orth /Users/<USER>/work/fontconfig/fc-lang/sc.orth /Users/<USER>/work/fontconfig/fc-lang/sd.orth /Users/<USER>/work/fontconfig/fc-lang/sg.orth /Users/<USER>/work/fontconfig/fc-lang/sid.orth /Users/<USER>/work/fontconfig/fc-lang/sn.orth /Users/<USER>/work/fontconfig/fc-lang/su.orth /Users/<USER>/work/fontconfig/fc-lang/ty.orth /Users/<USER>/work/fontconfig/fc-lang/wal.orth /Users/<USER>/work/fontconfig/fc-lang/za.orth /Users/<USER>/work/fontconfig/fc-lang/lah.orth /Users/<USER>/work/fontconfig/fc-lang/nqo.orth /Users/<USER>/work/fontconfig/fc-lang/brx.orth /Users/<USER>/work/fontconfig/fc-lang/sat.orth /Users/<USER>/work/fontconfig/fc-lang/doi.orth /Users/<USER>/work/fontconfig/fc-lang/mni.orth /Users/<USER>/work/fontconfig/fc-lang/und_zsye.orth /Users/<USER>/work/fontconfig/fc-lang/und_zmth.orth /Users/<USER>/work/fontconfig/fc-lang/anp.orth /Users/<USER>/work/fontconfig/fc-lang/bhb.orth /Users/<USER>/work/fontconfig/fc-lang/hif.orth /Users/<USER>/work/fontconfig/fc-lang/mag.orth /Users/<USER>/work/fontconfig/fc-lang/raj.orth /Users/<USER>/work/fontconfig/fc-lang/the.orth /Users/<USER>/work/fontconfig/fc-lang/agr.orth /Users/<USER>/work/fontconfig/fc-lang/ayc.orth /Users/<USER>/work/fontconfig/fc-lang/bem.orth /Users/<USER>/work/fontconfig/fc-lang/ckb.orth /Users/<USER>/work/fontconfig/fc-lang/cmn.orth /Users/<USER>/work/fontconfig/fc-lang/dsb.orth /Users/<USER>/work/fontconfig/fc-lang/hak.orth /Users/<USER>/work/fontconfig/fc-lang/lij.orth /Users/<USER>/work/fontconfig/fc-lang/lzh.orth /Users/<USER>/work/fontconfig/fc-lang/mfe.orth /Users/<USER>/work/fontconfig/fc-lang/mhr.orth /Users/<USER>/work/fontconfig/fc-lang/miq.orth /Users/<USER>/work/fontconfig/fc-lang/mjw.orth /Users/<USER>/work/fontconfig/fc-lang/mnw.orth /Users/<USER>/work/fontconfig/fc-lang/nan.orth /Users/<USER>/work/fontconfig/fc-lang/nhn.orth /Users/<USER>/work/fontconfig/fc-lang/niu.orth /Users/<USER>/work/fontconfig/fc-lang/rif.orth /Users/<USER>/work/fontconfig/fc-lang/sgs.orth /Users/<USER>/work/fontconfig/fc-lang/shn.orth /Users/<USER>/work/fontconfig/fc-lang/szl.orth /Users/<USER>/work/fontconfig/fc-lang/tcy.orth /Users/<USER>/work/fontconfig/fc-lang/tpi.orth /Users/<USER>/work/fontconfig/fc-lang/unm.orth /Users/<USER>/work/fontconfig/fc-lang/wae.orth /Users/<USER>/work/fontconfig/fc-lang/yue.orth /Users/<USER>/work/fontconfig/fc-lang/yuw.orth /Users/<USER>/work/fontconfig/fc-lang/got.orth /Users/<USER>/work/fontconfig/fc-lang/cop.orth --template /Users/<USER>/work/fontconfig/fc-lang/fclang.tmpl.h --output /Users/<USER>/work/fontconfig/build/fc-lang/fclang.h --directory /Users/<USER>/work/fontconfig/fc-lang
  DESC = Generating fclang.h
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target patternlib_internal


#############################################
# Order-only phony target for patternlib_internal

build cmake_object_order_depends_target_patternlib_internal: phony || fc-case/fccase_h fc-lang/fclang_h src/generated_headers

build src/CMakeFiles/patternlib_internal.dir/fcpat.c.o: C_COMPILER__patternlib_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcpat.c || cmake_object_order_depends_target_patternlib_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/patternlib_internal.dir/fcpat.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src/.. -I/Users/<USER>/work/fontconfig/build/src -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16
  OBJECT_DIR = src/CMakeFiles/patternlib_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/patternlib_internal.dir



#############################################
# Object library patternlib_internal

build src/patternlib_internal: phony src/CMakeFiles/patternlib_internal.dir/fcpat.c.o


#############################################
# Utility command for generated_headers

build src/generated_headers: phony src/CMakeFiles/generated_headers src/fcalias.h src/fcaliastail.h src/fcftalias.h src/fcftaliastail.h src/fcobjshash.h src/fcobjshash.gperf

# =============================================================================
# Object build statements for SHARED_LIBRARY target fontconfig


#############################################
# Order-only phony target for fontconfig

build cmake_object_order_depends_target_fontconfig: phony || cmake_object_order_depends_target_patternlib_internal fc-case/fccase_h fc-lang/fclang_h src/generated_headers

build src/CMakeFiles/fontconfig.dir/fcatomic.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcatomic.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcatomic.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fccache.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fccache.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fccache.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fccfg.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fccfg.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fccfg.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fccharset.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fccharset.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fccharset.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fccompat.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fccompat.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fccompat.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcdbg.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcdbg.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcdbg.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcdefault.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcdefault.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcdefault.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcdir.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcdir.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcdir.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcformat.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcformat.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcformat.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcfreetype.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcfreetype.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcfreetype.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcfs.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcfs.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcfs.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcptrlist.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcptrlist.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcptrlist.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fchash.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fchash.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fchash.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcinit.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcinit.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcinit.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fclang.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fclang.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fclang.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fclist.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fclist.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fclist.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcmatch.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcmatch.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcmatch.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcmatrix.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcmatrix.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcmatrix.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcname.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcname.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcname.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcobjs.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcobjs.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcobjs.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcrange.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcrange.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcrange.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcserialize.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcserialize.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcserialize.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcstat.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcstat.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcstat.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcstr.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcstr.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcstr.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcweight.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcweight.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcweight.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/fcxml.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcxml.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/fcxml.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir

build src/CMakeFiles/fontconfig.dir/ftglue.c.o: C_COMPILER__fontconfig_unscanned_Debug /Users/<USER>/work/fontconfig/src/ftglue.c || cmake_object_order_depends_target_fontconfig
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -Dfontconfig_EXPORTS
  DEP_FILE = src/CMakeFiles/fontconfig.dir/ftglue.c.o.d
  FLAGS = -g -std=gnu11 -fPIC -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target fontconfig


#############################################
# Link the shared library src/libfontconfig.2.16.0.dylib

build src/libfontconfig.2.16.0.dylib: C_SHARED_LIBRARY_LINKER__fontconfig_Debug src/CMakeFiles/patternlib_internal.dir/fcpat.c.o src/CMakeFiles/fontconfig.dir/fcatomic.c.o src/CMakeFiles/fontconfig.dir/fccache.c.o src/CMakeFiles/fontconfig.dir/fccfg.c.o src/CMakeFiles/fontconfig.dir/fccharset.c.o src/CMakeFiles/fontconfig.dir/fccompat.c.o src/CMakeFiles/fontconfig.dir/fcdbg.c.o src/CMakeFiles/fontconfig.dir/fcdefault.c.o src/CMakeFiles/fontconfig.dir/fcdir.c.o src/CMakeFiles/fontconfig.dir/fcformat.c.o src/CMakeFiles/fontconfig.dir/fcfreetype.c.o src/CMakeFiles/fontconfig.dir/fcfs.c.o src/CMakeFiles/fontconfig.dir/fcptrlist.c.o src/CMakeFiles/fontconfig.dir/fchash.c.o src/CMakeFiles/fontconfig.dir/fcinit.c.o src/CMakeFiles/fontconfig.dir/fclang.c.o src/CMakeFiles/fontconfig.dir/fclist.c.o src/CMakeFiles/fontconfig.dir/fcmatch.c.o src/CMakeFiles/fontconfig.dir/fcmatrix.c.o src/CMakeFiles/fontconfig.dir/fcname.c.o src/CMakeFiles/fontconfig.dir/fcobjs.c.o src/CMakeFiles/fontconfig.dir/fcrange.c.o src/CMakeFiles/fontconfig.dir/fcserialize.c.o src/CMakeFiles/fontconfig.dir/fcstat.c.o src/CMakeFiles/fontconfig.dir/fcstr.c.o src/CMakeFiles/fontconfig.dir/fcweight.c.o src/CMakeFiles/fontconfig.dir/fcxml.c.o src/CMakeFiles/fontconfig.dir/ftglue.c.o | /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/generated_headers src/patternlib_internal
  CONFIG = Debug
  INSTALLNAME_DIR = @rpath/
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -compatibility_version 2.0.0 -current_version 2.16.0
  LINK_LIBRARIES = -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = src/CMakeFiles/fontconfig.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libfontconfig.2.dylib
  SONAME_FLAG = -install_name
  TARGET_FILE = src/libfontconfig.2.16.0.dylib
  TARGET_PDB = fontconfig.dylib.dbg


#############################################
# Create library symlink src/libfontconfig.dylib

build src/libfontconfig.2.dylib src/libfontconfig.dylib: CMAKE_SYMLINK_LIBRARY src/libfontconfig.2.16.0.dylib
  POST_BUILD = :

# =============================================================================
# Object build statements for STATIC_LIBRARY target fontconfig_internal


#############################################
# Order-only phony target for fontconfig_internal

build cmake_object_order_depends_target_fontconfig_internal: phony || cmake_object_order_depends_target_patternlib_internal fc-case/fccase_h fc-lang/fclang_h src/generated_headers

build src/CMakeFiles/fontconfig_internal.dir/fcatomic.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcatomic.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcatomic.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fccache.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fccache.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fccache.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fccfg.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fccfg.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fccfg.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fccharset.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fccharset.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fccharset.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fccompat.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fccompat.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fccompat.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcdbg.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcdbg.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcdbg.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcdefault.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcdefault.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcdefault.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcdir.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcdir.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcdir.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcformat.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcformat.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcformat.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcfreetype.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcfreetype.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcfreetype.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcfs.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcfs.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcfs.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcptrlist.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcptrlist.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcptrlist.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fchash.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fchash.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fchash.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcinit.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcinit.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcinit.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fclang.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fclang.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fclang.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fclist.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fclist.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fclist.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcmatch.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcmatch.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcmatch.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcmatrix.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcmatrix.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcmatrix.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcname.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcname.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcname.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcobjs.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcobjs.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcobjs.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcrange.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcrange.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcrange.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcserialize.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcserialize.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcserialize.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcstat.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcstat.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcstat.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcstr.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcstr.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcstr.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcweight.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcweight.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcweight.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/fcxml.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/fcxml.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/fcxml.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir

build src/CMakeFiles/fontconfig_internal.dir/ftglue.c.o: C_COMPILER__fontconfig_internal_unscanned_Debug /Users/<USER>/work/fontconfig/src/ftglue.c || cmake_object_order_depends_target_fontconfig_internal
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = src/CMakeFiles/fontconfig_internal.dir/ftglue.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/src -I/Users/<USER>/work/fontconfig/build/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src/../fc-lang -I/Users/<USER>/work/fontconfig/build/src/../fc-case
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  OBJECT_FILE_DIR = src/CMakeFiles/fontconfig_internal.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target fontconfig_internal


#############################################
# Link the static library src/libfontconfig_internal.a

build src/libfontconfig_internal.a: C_STATIC_LIBRARY_LINKER__fontconfig_internal_Debug src/CMakeFiles/patternlib_internal.dir/fcpat.c.o src/CMakeFiles/fontconfig_internal.dir/fcatomic.c.o src/CMakeFiles/fontconfig_internal.dir/fccache.c.o src/CMakeFiles/fontconfig_internal.dir/fccfg.c.o src/CMakeFiles/fontconfig_internal.dir/fccharset.c.o src/CMakeFiles/fontconfig_internal.dir/fccompat.c.o src/CMakeFiles/fontconfig_internal.dir/fcdbg.c.o src/CMakeFiles/fontconfig_internal.dir/fcdefault.c.o src/CMakeFiles/fontconfig_internal.dir/fcdir.c.o src/CMakeFiles/fontconfig_internal.dir/fcformat.c.o src/CMakeFiles/fontconfig_internal.dir/fcfreetype.c.o src/CMakeFiles/fontconfig_internal.dir/fcfs.c.o src/CMakeFiles/fontconfig_internal.dir/fcptrlist.c.o src/CMakeFiles/fontconfig_internal.dir/fchash.c.o src/CMakeFiles/fontconfig_internal.dir/fcinit.c.o src/CMakeFiles/fontconfig_internal.dir/fclang.c.o src/CMakeFiles/fontconfig_internal.dir/fclist.c.o src/CMakeFiles/fontconfig_internal.dir/fcmatch.c.o src/CMakeFiles/fontconfig_internal.dir/fcmatrix.c.o src/CMakeFiles/fontconfig_internal.dir/fcname.c.o src/CMakeFiles/fontconfig_internal.dir/fcobjs.c.o src/CMakeFiles/fontconfig_internal.dir/fcrange.c.o src/CMakeFiles/fontconfig_internal.dir/fcserialize.c.o src/CMakeFiles/fontconfig_internal.dir/fcstat.c.o src/CMakeFiles/fontconfig_internal.dir/fcstr.c.o src/CMakeFiles/fontconfig_internal.dir/fcweight.c.o src/CMakeFiles/fontconfig_internal.dir/fcxml.c.o src/CMakeFiles/fontconfig_internal.dir/ftglue.c.o || fc-case/fccase_h fc-lang/fclang_h src/generated_headers src/patternlib_internal
  CONFIG = Debug
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = src/CMakeFiles/fontconfig_internal.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = src/libfontconfig_internal.a
  TARGET_PDB = fontconfig_internal.a.dbg


#############################################
# Utility command for test

build src/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build src/test: phony src/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build src/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/edit_cache: phony src/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/rebuild_cache: phony src/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/list_install_components: phony


#############################################
# Utility command for install

build src/CMakeFiles/install.util: CUSTOM_COMMAND src/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/install: phony src/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/CMakeFiles/install/local.util: CUSTOM_COMMAND src/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/install/local: phony src/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/install/strip: phony src/CMakeFiles/install/strip.util


#############################################
# Phony custom command for src/CMakeFiles/generated_headers

build src/CMakeFiles/generated_headers | ${cmake_ninja_workdir}src/CMakeFiles/generated_headers: phony src/fcalias.h src/fcaliastail.h src/fcftalias.h src/fcftaliastail.h src/fcobjshash.h src/fcstdint.h


#############################################
# Custom command for src/fcalias.h

build src/fcalias.h src/fcaliastail.h | ${cmake_ninja_workdir}src/fcalias.h ${cmake_ninja_workdir}src/fcaliastail.h: CUSTOM_COMMAND /Users/<USER>/work/fontconfig/src/makealias.py fontconfig/fontconfig.h /Users/<USER>/work/fontconfig/src/fcdeprecate.h /Users/<USER>/work/fontconfig/fontconfig/fcprivate.h
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/src/makealias.py /Users/<USER>/work/fontconfig/src /Users/<USER>/work/fontconfig/build/src/fcalias.h /Users/<USER>/work/fontconfig/build/src/fcaliastail.h /Users/<USER>/work/fontconfig/build/src/../fontconfig/fontconfig.h /Users/<USER>/work/fontconfig/src/fcdeprecate.h /Users/<USER>/work/fontconfig/src/../fontconfig/fcprivate.h
  DESC = Generating alias headers
  restat = 1


#############################################
# Custom command for src/fcftalias.h

build src/fcftalias.h src/fcftaliastail.h | ${cmake_ninja_workdir}src/fcftalias.h ${cmake_ninja_workdir}src/fcftaliastail.h: CUSTOM_COMMAND /Users/<USER>/work/fontconfig/src/makealias.py /Users/<USER>/work/fontconfig/fontconfig/fcfreetype.h
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/src/makealias.py /Users/<USER>/work/fontconfig/src /Users/<USER>/work/fontconfig/build/src/fcftalias.h /Users/<USER>/work/fontconfig/build/src/fcftaliastail.h /Users/<USER>/work/fontconfig/src/../fontconfig/fcfreetype.h
  DESC = Generating FreeType alias headers
  restat = 1


#############################################
# Custom command for src/fcobjshash.h

build src/fcobjshash.h | ${cmake_ninja_workdir}src/fcobjshash.h: CUSTOM_COMMAND src/fcobjshash.gperf
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/bin/gperf --pic -m 100 /Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf --output-file /Users/<USER>/work/fontconfig/build/src/fcobjshash.h
  DESC = Generating fcobjshash.h with gperf
  restat = 1


#############################################
# Custom command for src/fcobjshash.gperf

build src/fcobjshash.gperf | ${cmake_ninja_workdir}src/fcobjshash.gperf: CUSTOM_COMMAND /Users/<USER>/work/fontconfig/src/fcobjshash.gperf.h /Users/<USER>/work/fontconfig/src/cutout.py /Users/<USER>/work/fontconfig/src/process-gperf.py /Users/<USER>/work/fontconfig/src/fcobjs.h
  COMMAND = cd /Users/<USER>/work/fontconfig/build/src && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/src/cutout.py /Users/<USER>/work/fontconfig/src/fcobjshash.gperf.h /Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf.tmp && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/src/process-gperf.py /usr/bin/clang /Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf.tmp /Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf
  DESC = Generating fcobjshash.gperf
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target fc-cache


#############################################
# Order-only phony target for fc-cache

build cmake_object_order_depends_target_fc-cache: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build fc-cache/CMakeFiles/fc-cache.dir/fc-cache.c.o: C_COMPILER__fc-cache_unscanned_Debug /Users/<USER>/work/fontconfig/fc-cache/fc-cache.c || cmake_object_order_depends_target_fc-cache
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = fc-cache/CMakeFiles/fc-cache.dir/fc-cache.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-cache/../src -I/Users/<USER>/work/fontconfig/build/fc-cache/.. -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = fc-cache/CMakeFiles/fc-cache.dir
  OBJECT_FILE_DIR = fc-cache/CMakeFiles/fc-cache.dir


# =============================================================================
# Link build statements for EXECUTABLE target fc-cache


#############################################
# Link the executable fc-cache/fc-cache

build fc-cache/fc-cache: C_EXECUTABLE_LINKER__fc-cache_Debug fc-cache/CMakeFiles/fc-cache.dir/fc-cache.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = fc-cache/CMakeFiles/fc-cache.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fc-cache/fc-cache
  TARGET_PDB = fc-cache.dbg


#############################################
# Utility command for test

build fc-cache/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cache && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-cache/test: phony fc-cache/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-cache/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cache && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-cache/edit_cache: phony fc-cache/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-cache/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cache && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-cache/rebuild_cache: phony fc-cache/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-cache/list_install_components: phony


#############################################
# Utility command for install

build fc-cache/CMakeFiles/install.util: CUSTOM_COMMAND fc-cache/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cache && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-cache/install: phony fc-cache/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-cache/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-cache/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cache && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-cache/install/local: phony fc-cache/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-cache/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-cache/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cache && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-cache/install/strip: phony fc-cache/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target fc-cat


#############################################
# Order-only phony target for fc-cat

build cmake_object_order_depends_target_fc-cat: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build fc-cat/CMakeFiles/fc-cat.dir/fc-cat.c.o: C_COMPILER__fc-cat_unscanned_Debug /Users/<USER>/work/fontconfig/fc-cat/fc-cat.c || cmake_object_order_depends_target_fc-cat
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = fc-cat/CMakeFiles/fc-cat.dir/fc-cat.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-cat/../src -I/Users/<USER>/work/fontconfig/build/fc-cat/.. -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = fc-cat/CMakeFiles/fc-cat.dir
  OBJECT_FILE_DIR = fc-cat/CMakeFiles/fc-cat.dir


# =============================================================================
# Link build statements for EXECUTABLE target fc-cat


#############################################
# Link the executable fc-cat/fc-cat

build fc-cat/fc-cat: C_EXECUTABLE_LINKER__fc-cat_Debug fc-cat/CMakeFiles/fc-cat.dir/fc-cat.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = fc-cat/CMakeFiles/fc-cat.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fc-cat/fc-cat
  TARGET_PDB = fc-cat.dbg


#############################################
# Utility command for test

build fc-cat/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cat && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-cat/test: phony fc-cat/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-cat/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cat && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-cat/edit_cache: phony fc-cat/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-cat/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cat && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-cat/rebuild_cache: phony fc-cat/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-cat/list_install_components: phony


#############################################
# Utility command for install

build fc-cat/CMakeFiles/install.util: CUSTOM_COMMAND fc-cat/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cat && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-cat/install: phony fc-cat/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-cat/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-cat/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cat && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-cat/install/local: phony fc-cat/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-cat/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-cat/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-cat && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-cat/install/strip: phony fc-cat/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target fc-conflist


#############################################
# Order-only phony target for fc-conflist

build cmake_object_order_depends_target_fc-conflist: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build fc-conflist/CMakeFiles/fc-conflist.dir/fc-conflist.c.o: C_COMPILER__fc-conflist_unscanned_Debug /Users/<USER>/work/fontconfig/fc-conflist/fc-conflist.c || cmake_object_order_depends_target_fc-conflist
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = fc-conflist/CMakeFiles/fc-conflist.dir/fc-conflist.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-conflist/../src -I/Users/<USER>/work/fontconfig/build/fc-conflist/.. -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = fc-conflist/CMakeFiles/fc-conflist.dir
  OBJECT_FILE_DIR = fc-conflist/CMakeFiles/fc-conflist.dir


# =============================================================================
# Link build statements for EXECUTABLE target fc-conflist


#############################################
# Link the executable fc-conflist/fc-conflist

build fc-conflist/fc-conflist: C_EXECUTABLE_LINKER__fc-conflist_Debug fc-conflist/CMakeFiles/fc-conflist.dir/fc-conflist.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = fc-conflist/CMakeFiles/fc-conflist.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fc-conflist/fc-conflist
  TARGET_PDB = fc-conflist.dbg


#############################################
# Utility command for test

build fc-conflist/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-conflist && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-conflist/test: phony fc-conflist/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-conflist/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-conflist && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-conflist/edit_cache: phony fc-conflist/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-conflist/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-conflist && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-conflist/rebuild_cache: phony fc-conflist/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-conflist/list_install_components: phony


#############################################
# Utility command for install

build fc-conflist/CMakeFiles/install.util: CUSTOM_COMMAND fc-conflist/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-conflist && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-conflist/install: phony fc-conflist/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-conflist/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-conflist/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-conflist && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-conflist/install/local: phony fc-conflist/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-conflist/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-conflist/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-conflist && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-conflist/install/strip: phony fc-conflist/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target fc-list


#############################################
# Order-only phony target for fc-list

build cmake_object_order_depends_target_fc-list: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build fc-list/CMakeFiles/fc-list.dir/fc-list.c.o: C_COMPILER__fc-list_unscanned_Debug /Users/<USER>/work/fontconfig/fc-list/fc-list.c || cmake_object_order_depends_target_fc-list
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = fc-list/CMakeFiles/fc-list.dir/fc-list.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-list/../src -I/Users/<USER>/work/fontconfig/build/fc-list/.. -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = fc-list/CMakeFiles/fc-list.dir
  OBJECT_FILE_DIR = fc-list/CMakeFiles/fc-list.dir


# =============================================================================
# Link build statements for EXECUTABLE target fc-list


#############################################
# Link the executable fc-list/fc-list

build fc-list/fc-list: C_EXECUTABLE_LINKER__fc-list_Debug fc-list/CMakeFiles/fc-list.dir/fc-list.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = fc-list/CMakeFiles/fc-list.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fc-list/fc-list
  TARGET_PDB = fc-list.dbg


#############################################
# Utility command for test

build fc-list/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-list && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-list/test: phony fc-list/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-list/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-list && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-list/edit_cache: phony fc-list/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-list/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-list && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-list/rebuild_cache: phony fc-list/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-list/list_install_components: phony


#############################################
# Utility command for install

build fc-list/CMakeFiles/install.util: CUSTOM_COMMAND fc-list/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-list && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-list/install: phony fc-list/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-list/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-list/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-list && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-list/install/local: phony fc-list/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-list/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-list/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-list && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-list/install/strip: phony fc-list/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target fc-match


#############################################
# Order-only phony target for fc-match

build cmake_object_order_depends_target_fc-match: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build fc-match/CMakeFiles/fc-match.dir/fc-match.c.o: C_COMPILER__fc-match_unscanned_Debug /Users/<USER>/work/fontconfig/fc-match/fc-match.c || cmake_object_order_depends_target_fc-match
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = fc-match/CMakeFiles/fc-match.dir/fc-match.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-match/../src -I/Users/<USER>/work/fontconfig/build/fc-match/.. -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = fc-match/CMakeFiles/fc-match.dir
  OBJECT_FILE_DIR = fc-match/CMakeFiles/fc-match.dir


# =============================================================================
# Link build statements for EXECUTABLE target fc-match


#############################################
# Link the executable fc-match/fc-match

build fc-match/fc-match: C_EXECUTABLE_LINKER__fc-match_Debug fc-match/CMakeFiles/fc-match.dir/fc-match.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = fc-match/CMakeFiles/fc-match.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fc-match/fc-match
  TARGET_PDB = fc-match.dbg


#############################################
# Utility command for test

build fc-match/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-match && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-match/test: phony fc-match/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-match/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-match && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-match/edit_cache: phony fc-match/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-match/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-match && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-match/rebuild_cache: phony fc-match/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-match/list_install_components: phony


#############################################
# Utility command for install

build fc-match/CMakeFiles/install.util: CUSTOM_COMMAND fc-match/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-match && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-match/install: phony fc-match/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-match/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-match/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-match && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-match/install/local: phony fc-match/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-match/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-match/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-match && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-match/install/strip: phony fc-match/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target fc-pattern


#############################################
# Order-only phony target for fc-pattern

build cmake_object_order_depends_target_fc-pattern: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build fc-pattern/CMakeFiles/fc-pattern.dir/fc-pattern.c.o: C_COMPILER__fc-pattern_unscanned_Debug /Users/<USER>/work/fontconfig/fc-pattern/fc-pattern.c || cmake_object_order_depends_target_fc-pattern
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = fc-pattern/CMakeFiles/fc-pattern.dir/fc-pattern.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-pattern/../src -I/Users/<USER>/work/fontconfig/build/fc-pattern/.. -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = fc-pattern/CMakeFiles/fc-pattern.dir
  OBJECT_FILE_DIR = fc-pattern/CMakeFiles/fc-pattern.dir


# =============================================================================
# Link build statements for EXECUTABLE target fc-pattern


#############################################
# Link the executable fc-pattern/fc-pattern

build fc-pattern/fc-pattern: C_EXECUTABLE_LINKER__fc-pattern_Debug fc-pattern/CMakeFiles/fc-pattern.dir/fc-pattern.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = fc-pattern/CMakeFiles/fc-pattern.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fc-pattern/fc-pattern
  TARGET_PDB = fc-pattern.dbg


#############################################
# Utility command for test

build fc-pattern/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-pattern && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-pattern/test: phony fc-pattern/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-pattern/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-pattern && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-pattern/edit_cache: phony fc-pattern/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-pattern/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-pattern && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-pattern/rebuild_cache: phony fc-pattern/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-pattern/list_install_components: phony


#############################################
# Utility command for install

build fc-pattern/CMakeFiles/install.util: CUSTOM_COMMAND fc-pattern/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-pattern && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-pattern/install: phony fc-pattern/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-pattern/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-pattern/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-pattern && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-pattern/install/local: phony fc-pattern/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-pattern/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-pattern/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-pattern && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-pattern/install/strip: phony fc-pattern/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target fc-query


#############################################
# Order-only phony target for fc-query

build cmake_object_order_depends_target_fc-query: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build fc-query/CMakeFiles/fc-query.dir/fc-query.c.o: C_COMPILER__fc-query_unscanned_Debug /Users/<USER>/work/fontconfig/fc-query/fc-query.c || cmake_object_order_depends_target_fc-query
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = fc-query/CMakeFiles/fc-query.dir/fc-query.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-query/../src -I/Users/<USER>/work/fontconfig/build/fc-query/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = fc-query/CMakeFiles/fc-query.dir
  OBJECT_FILE_DIR = fc-query/CMakeFiles/fc-query.dir


# =============================================================================
# Link build statements for EXECUTABLE target fc-query


#############################################
# Link the executable fc-query/fc-query

build fc-query/fc-query: C_EXECUTABLE_LINKER__fc-query_Debug fc-query/CMakeFiles/fc-query.dir/fc-query.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = fc-query/CMakeFiles/fc-query.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fc-query/fc-query
  TARGET_PDB = fc-query.dbg


#############################################
# Utility command for test

build fc-query/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-query && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-query/test: phony fc-query/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-query/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-query && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-query/edit_cache: phony fc-query/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-query/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-query && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-query/rebuild_cache: phony fc-query/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-query/list_install_components: phony


#############################################
# Utility command for install

build fc-query/CMakeFiles/install.util: CUSTOM_COMMAND fc-query/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-query && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-query/install: phony fc-query/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-query/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-query/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-query && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-query/install/local: phony fc-query/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-query/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-query/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-query && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-query/install/strip: phony fc-query/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target fc-scan


#############################################
# Order-only phony target for fc-scan

build cmake_object_order_depends_target_fc-scan: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build fc-scan/CMakeFiles/fc-scan.dir/fc-scan.c.o: C_COMPILER__fc-scan_unscanned_Debug /Users/<USER>/work/fontconfig/fc-scan/fc-scan.c || cmake_object_order_depends_target_fc-scan
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = fc-scan/CMakeFiles/fc-scan.dir/fc-scan.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-scan/../src -I/Users/<USER>/work/fontconfig/build/fc-scan/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = fc-scan/CMakeFiles/fc-scan.dir
  OBJECT_FILE_DIR = fc-scan/CMakeFiles/fc-scan.dir


# =============================================================================
# Link build statements for EXECUTABLE target fc-scan


#############################################
# Link the executable fc-scan/fc-scan

build fc-scan/fc-scan: C_EXECUTABLE_LINKER__fc-scan_Debug fc-scan/CMakeFiles/fc-scan.dir/fc-scan.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = fc-scan/CMakeFiles/fc-scan.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fc-scan/fc-scan
  TARGET_PDB = fc-scan.dbg


#############################################
# Utility command for test

build fc-scan/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-scan && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-scan/test: phony fc-scan/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-scan/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-scan && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-scan/edit_cache: phony fc-scan/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-scan/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-scan && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-scan/rebuild_cache: phony fc-scan/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-scan/list_install_components: phony


#############################################
# Utility command for install

build fc-scan/CMakeFiles/install.util: CUSTOM_COMMAND fc-scan/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-scan && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-scan/install: phony fc-scan/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-scan/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-scan/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-scan && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-scan/install/local: phony fc-scan/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-scan/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-scan/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-scan && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-scan/install/strip: phony fc-scan/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target fc-validate


#############################################
# Order-only phony target for fc-validate

build cmake_object_order_depends_target_fc-validate: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build fc-validate/CMakeFiles/fc-validate.dir/fc-validate.c.o: C_COMPILER__fc-validate_unscanned_Debug /Users/<USER>/work/fontconfig/fc-validate/fc-validate.c || cmake_object_order_depends_target_fc-validate
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = fc-validate/CMakeFiles/fc-validate.dir/fc-validate.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/fc-validate/../src -I/Users/<USER>/work/fontconfig/build/fc-validate/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = fc-validate/CMakeFiles/fc-validate.dir
  OBJECT_FILE_DIR = fc-validate/CMakeFiles/fc-validate.dir


# =============================================================================
# Link build statements for EXECUTABLE target fc-validate


#############################################
# Link the executable fc-validate/fc-validate

build fc-validate/fc-validate: C_EXECUTABLE_LINKER__fc-validate_Debug fc-validate/CMakeFiles/fc-validate.dir/fc-validate.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = fc-validate/CMakeFiles/fc-validate.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fc-validate/fc-validate
  TARGET_PDB = fc-validate.dbg


#############################################
# Utility command for test

build fc-validate/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-validate && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build fc-validate/test: phony fc-validate/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build fc-validate/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-validate && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build fc-validate/edit_cache: phony fc-validate/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build fc-validate/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-validate && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build fc-validate/rebuild_cache: phony fc-validate/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build fc-validate/list_install_components: phony


#############################################
# Utility command for install

build fc-validate/CMakeFiles/install.util: CUSTOM_COMMAND fc-validate/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-validate && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build fc-validate/install: phony fc-validate/CMakeFiles/install.util


#############################################
# Utility command for install/local

build fc-validate/CMakeFiles/install/local.util: CUSTOM_COMMAND fc-validate/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-validate && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build fc-validate/install/local: phony fc-validate/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build fc-validate/CMakeFiles/install/strip.util: CUSTOM_COMMAND fc-validate/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/fc-validate && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build fc-validate/install/strip: phony fc-validate/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for fetch_test_fonts

build test/fetch_test_fonts: phony test/CMakeFiles/fetch_test_fonts

# =============================================================================
# Object build statements for EXECUTABLE target test_bz89617


#############################################
# Order-only phony target for test_bz89617

build cmake_object_order_depends_target_test_bz89617: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_bz89617.dir/test-bz89617.c.o: C_COMPILER__test_bz89617_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-bz89617.c || cmake_object_order_depends_target_test_bz89617
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H -DSRCDIR=\"/Users/<USER>/work/fontconfig/test\"
  DEP_FILE = test/CMakeFiles/test_bz89617.dir/test-bz89617.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_bz89617.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_bz89617.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_bz89617


#############################################
# Link the executable test/test_bz89617

build test/test_bz89617: C_EXECUTABLE_LINKER__test_bz89617_Debug test/CMakeFiles/test_bz89617.dir/test-bz89617.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_bz89617.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_bz89617
  TARGET_PDB = test_bz89617.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_bz131804


#############################################
# Order-only phony target for test_bz131804

build cmake_object_order_depends_target_test_bz131804: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_bz131804.dir/test-bz131804.c.o: C_COMPILER__test_bz131804_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-bz131804.c || cmake_object_order_depends_target_test_bz131804
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_bz131804.dir/test-bz131804.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_bz131804.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_bz131804.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_bz131804


#############################################
# Link the executable test/test_bz131804

build test/test_bz131804: C_EXECUTABLE_LINKER__test_bz131804_Debug test/CMakeFiles/test_bz131804.dir/test-bz131804.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_bz131804.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_bz131804
  TARGET_PDB = test_bz131804.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_bz96676


#############################################
# Order-only phony target for test_bz96676

build cmake_object_order_depends_target_test_bz96676: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_bz96676.dir/test-bz96676.c.o: C_COMPILER__test_bz96676_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-bz96676.c || cmake_object_order_depends_target_test_bz96676
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_bz96676.dir/test-bz96676.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_bz96676.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_bz96676.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_bz96676


#############################################
# Link the executable test/test_bz96676

build test/test_bz96676: C_EXECUTABLE_LINKER__test_bz96676_Debug test/CMakeFiles/test_bz96676.dir/test-bz96676.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_bz96676.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_bz96676
  TARGET_PDB = test_bz96676.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_name_parse


#############################################
# Order-only phony target for test_name_parse

build cmake_object_order_depends_target_test_name_parse: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_name_parse.dir/test-name-parse.c.o: C_COMPILER__test_name_parse_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-name-parse.c || cmake_object_order_depends_target_test_name_parse
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_name_parse.dir/test-name-parse.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_name_parse.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_name_parse.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_name_parse


#############################################
# Link the executable test/test_name_parse

build test/test_name_parse: C_EXECUTABLE_LINKER__test_name_parse_Debug test/CMakeFiles/test_name_parse.dir/test-name-parse.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_name_parse.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_name_parse
  TARGET_PDB = test_name_parse.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_bz106618


#############################################
# Order-only phony target for test_bz106618

build cmake_object_order_depends_target_test_bz106618: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_bz106618.dir/test-bz106618.c.o: C_COMPILER__test_bz106618_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-bz106618.c || cmake_object_order_depends_target_test_bz106618
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_bz106618.dir/test-bz106618.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_bz106618.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_bz106618.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_bz106618


#############################################
# Link the executable test/test_bz106618

build test/test_bz106618: C_EXECUTABLE_LINKER__test_bz106618_Debug test/CMakeFiles/test_bz106618.dir/test-bz106618.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_bz106618.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_bz106618
  TARGET_PDB = test_bz106618.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_bz1744377


#############################################
# Order-only phony target for test_bz1744377

build cmake_object_order_depends_target_test_bz1744377: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_bz1744377.dir/test-bz1744377.c.o: C_COMPILER__test_bz1744377_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-bz1744377.c || cmake_object_order_depends_target_test_bz1744377
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_bz1744377.dir/test-bz1744377.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_bz1744377.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_bz1744377.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_bz1744377


#############################################
# Link the executable test/test_bz1744377

build test/test_bz1744377: C_EXECUTABLE_LINKER__test_bz1744377_Debug test/CMakeFiles/test_bz1744377.dir/test-bz1744377.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_bz1744377.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_bz1744377
  TARGET_PDB = test_bz1744377.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_issue180


#############################################
# Order-only phony target for test_issue180

build cmake_object_order_depends_target_test_issue180: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_issue180.dir/test-issue180.c.o: C_COMPILER__test_issue180_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-issue180.c || cmake_object_order_depends_target_test_issue180
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_issue180.dir/test-issue180.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_issue180.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_issue180.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_issue180


#############################################
# Link the executable test/test_issue180

build test/test_issue180: C_EXECUTABLE_LINKER__test_issue180_Debug test/CMakeFiles/test_issue180.dir/test-issue180.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_issue180.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_issue180
  TARGET_PDB = test_issue180.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_family_matching


#############################################
# Order-only phony target for test_family_matching

build cmake_object_order_depends_target_test_family_matching: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_family_matching.dir/test-family-matching.c.o: C_COMPILER__test_family_matching_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-family-matching.c || cmake_object_order_depends_target_test_family_matching
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_family_matching.dir/test-family-matching.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_family_matching.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_family_matching.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_family_matching


#############################################
# Link the executable test/test_family_matching

build test/test_family_matching: C_EXECUTABLE_LINKER__test_family_matching_Debug test/CMakeFiles/test_family_matching.dir/test-family-matching.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_family_matching.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_family_matching
  TARGET_PDB = test_family_matching.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_ptrlist


#############################################
# Order-only phony target for test_ptrlist

build cmake_object_order_depends_target_test_ptrlist: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_ptrlist.dir/test-ptrlist.c.o: C_COMPILER__test_ptrlist_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-ptrlist.c || cmake_object_order_depends_target_test_ptrlist
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_ptrlist.dir/test-ptrlist.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_ptrlist.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_ptrlist.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_ptrlist


#############################################
# Link the executable test/test_ptrlist

build test/test_ptrlist: C_EXECUTABLE_LINKER__test_ptrlist_Debug test/CMakeFiles/test_ptrlist.dir/test-ptrlist.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_ptrlist.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_ptrlist
  TARGET_PDB = test_ptrlist.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_bz106632


#############################################
# Order-only phony target for test_bz106632

build cmake_object_order_depends_target_test_bz106632: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_bz106632.dir/test-bz106632.c.o: C_COMPILER__test_bz106632_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-bz106632.c || cmake_object_order_depends_target_test_bz106632
  CONFIG = Debug
  DEFINES = -DFONTFILE=\"/Users/<USER>/work/fontconfig/test/4x6.pcf\" -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_bz106632.dir/test-bz106632.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_bz106632.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_bz106632.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_bz106632


#############################################
# Link the executable test/test_bz106632

build test/test_bz106632: C_EXECUTABLE_LINKER__test_bz106632_Debug test/CMakeFiles/test_bz106632.dir/test-bz106632.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_bz106632.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_bz106632
  TARGET_PDB = test_bz106632.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_issue107


#############################################
# Order-only phony target for test_issue107

build cmake_object_order_depends_target_test_issue107: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_issue107.dir/test-issue107.c.o: C_COMPILER__test_issue107_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-issue107.c || cmake_object_order_depends_target_test_issue107
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_issue107.dir/test-issue107.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_issue107.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_issue107.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_issue107


#############################################
# Link the executable test/test_issue107

build test/test_issue107: C_EXECUTABLE_LINKER__test_issue107_Debug test/CMakeFiles/test_issue107.dir/test-issue107.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_issue107.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_issue107
  TARGET_PDB = test_issue107.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_crbug1004254


#############################################
# Order-only phony target for test_crbug1004254

build cmake_object_order_depends_target_test_crbug1004254: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_crbug1004254.dir/test-crbug1004254.c.o: C_COMPILER__test_crbug1004254_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-crbug1004254.c || cmake_object_order_depends_target_test_crbug1004254
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_crbug1004254.dir/test-crbug1004254.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_crbug1004254.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_crbug1004254.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_crbug1004254


#############################################
# Link the executable test/test_crbug1004254

build test/test_crbug1004254: C_EXECUTABLE_LINKER__test_crbug1004254_Debug test/CMakeFiles/test_crbug1004254.dir/test-crbug1004254.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_crbug1004254.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_crbug1004254
  TARGET_PDB = test_crbug1004254.dbg

# =============================================================================
# Object build statements for EXECUTABLE target test_mt_fccfg


#############################################
# Order-only phony target for test_mt_fccfg

build cmake_object_order_depends_target_test_mt_fccfg: phony || cmake_object_order_depends_target_fontconfig_internal fc-case/fccase_h fc-lang/fclang_h

build test/CMakeFiles/test_mt_fccfg.dir/test-mt-fccfg.c.o: C_COMPILER__test_mt_fccfg_unscanned_Debug /Users/<USER>/work/fontconfig/test/test-mt-fccfg.c || cmake_object_order_depends_target_test_mt_fccfg
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/test_mt_fccfg.dir/test-mt-fccfg.c.o.d
  FLAGS = -g -std=gnu11 -Wno-excess-initializers
  INCLUDES = -I/Users/<USER>/work/fontconfig/build -I/Users/<USER>/work/fontconfig/build/fc-lang -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build/test/../src -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build/src
  OBJECT_DIR = test/CMakeFiles/test_mt_fccfg.dir
  OBJECT_FILE_DIR = test/CMakeFiles/test_mt_fccfg.dir


# =============================================================================
# Link build statements for EXECUTABLE target test_mt_fccfg


#############################################
# Link the executable test/test_mt_fccfg

build test/test_mt_fccfg: C_EXECUTABLE_LINKER__test_mt_fccfg_Debug test/CMakeFiles/test_mt_fccfg.dir/test-mt-fccfg.c.o | src/libfontconfig_internal.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || fc-case/fccase_h fc-lang/fclang_h src/libfontconfig_internal.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = src/libfontconfig_internal.a  -lfreetype  -lexpat  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/test_mt_fccfg.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/test_mt_fccfg
  TARGET_PDB = test_mt_fccfg.dbg


#############################################
# Utility command for test

build test/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/test && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build test/test: phony test/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build test/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/test && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build test/edit_cache: phony test/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build test/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/test && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build test/rebuild_cache: phony test/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build test/list_install_components: phony


#############################################
# Utility command for install

build test/CMakeFiles/install.util: CUSTOM_COMMAND test/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/test && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build test/install: phony test/CMakeFiles/install.util


#############################################
# Utility command for install/local

build test/CMakeFiles/install/local.util: CUSTOM_COMMAND test/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/test && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build test/install/local: phony test/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build test/CMakeFiles/install/strip.util: CUSTOM_COMMAND test/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/test && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build test/install/strip: phony test/CMakeFiles/install/strip.util


#############################################
# Custom command for test/CMakeFiles/fetch_test_fonts

build test/CMakeFiles/fetch_test_fonts | ${cmake_ninja_workdir}test/CMakeFiles/fetch_test_fonts: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/test && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/test/../build-aux/fetch-testfonts.py --target-dir /Users/<USER>/work/fontconfig/build/test/../testfonts --try-symlink
  DESC = Fetching test fonts

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for lang_normalize_conf

build conf.d/lang_normalize_conf: phony conf.d/CMakeFiles/lang_normalize_conf conf.d/35-lang-normalize.conf


#############################################
# Utility command for test

build conf.d/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/conf.d && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build conf.d/test: phony conf.d/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build conf.d/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/conf.d && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build conf.d/edit_cache: phony conf.d/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build conf.d/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/conf.d && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build conf.d/rebuild_cache: phony conf.d/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build conf.d/list_install_components: phony


#############################################
# Utility command for install

build conf.d/CMakeFiles/install.util: CUSTOM_COMMAND conf.d/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/conf.d && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build conf.d/install: phony conf.d/CMakeFiles/install.util


#############################################
# Utility command for install/local

build conf.d/CMakeFiles/install/local.util: CUSTOM_COMMAND conf.d/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/conf.d && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build conf.d/install/local: phony conf.d/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build conf.d/CMakeFiles/install/strip.util: CUSTOM_COMMAND conf.d/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/conf.d && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build conf.d/install/strip: phony conf.d/CMakeFiles/install/strip.util


#############################################
# Phony custom command for conf.d/CMakeFiles/lang_normalize_conf

build conf.d/CMakeFiles/lang_normalize_conf | ${cmake_ninja_workdir}conf.d/CMakeFiles/lang_normalize_conf: phony conf.d/35-lang-normalize.conf


#############################################
# Custom command for conf.d/35-lang-normalize.conf

build conf.d/35-lang-normalize.conf | ${cmake_ninja_workdir}conf.d/35-lang-normalize.conf: CUSTOM_COMMAND /Users/<USER>/work/fontconfig/conf.d/write-35-lang-normalize-conf.py
  COMMAND = cd /Users/<USER>/work/fontconfig/build/conf.d && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/conf.d/write-35-lang-normalize-conf.py aa\ ab\ af\ agr\ ak\ am\ an\ anp\ ar\ as\ ast\ av\ ay\ ayc\ ba\ be\ bem\ bg\ bh\ bhb\ bho\ bi\ bin\ bm\ bn\ bo\ br\ brx\ bs\ bua\ byn\ ca\ ce\ ch\ chm\ chr\ ckb\ cmn\ co\ cop\ crh\ cs\ csb\ cu\ cv\ cy\ da\ de\ doi\ dsb\ dv\ dz\ ee\ el\ en\ eo\ es\ et\ eu\ fa\ fat\ ff\ fi\ fil\ fj\ fo\ fr\ fur\ fy\ ga\ gd\ gez\ gl\ gn\ got\ gu\ gv\ ha\ hak\ haw\ he\ hi\ hif\ hne\ ho\ hr\ hsb\ ht\ hu\ hy\ hz\ ia\ id\ ie\ ig\ ii\ ik\ io\ is\ it\ iu\ ja\ jv\ ka\ kaa\ kab\ ki\ kj\ kk\ kl\ km\ kn\ ko\ kok\ kr\ ks\ kum\ kv\ kw\ kwm\ ky\ la\ lah\ lb\ lez\ lg\ li\ lij\ ln\ lo\ lt\ lv\ lzh\ mag\ mai\ mfe\ mg\ mh\ mhr\ mi\ miq\ mjw\ mk\ ml\ mni\ mnw\ mo\ mr\ ms\ mt\ my\ na\ nan\ nb\ nds\ ne\ ng\ nhn\ niu\ nl\ nn\ no\ nqo\ nr\ nso\ nv\ ny\ oc\ om\ or\ os\ ota\ pa\ pes\ pl\ prs\ pt\ qu\ quz\ raj\ rif\ rm\ rn\ ro\ ru\ rw\ sa\ sah\ sat\ sc\ sco\ sd\ se\ sel\ sg\ sgs\ sh\ shn\ shs\ si\ sid\ sk\ sl\ sm\ sma\ smj\ smn\ sms\ sn\ so\ sq\ sr\ ss\ st\ su\ sv\ sw\ syr\ szl\ ta\ tcy\ te\ tg\ th\ the\ tig\ tk\ tl\ tn\ to\ tpi\ tr\ ts\ tt\ tw\ ty\ tyv\ ug\ uk\ unm\ ur\ uz\ ve\ vi\ vo\ vot\ wa\ wae\ wal\ wen\ wo\ xh\ yap\ yi\ yo\ yue\ yuw\ za\ zu /Users/<USER>/work/fontconfig/build/conf.d/35-lang-normalize.conf
  DESC = Generating 35-lang-normalize.conf
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/fontconfig/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for test

build its/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/its && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build its/test: phony its/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build its/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/its && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build its/edit_cache: phony its/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build its/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build/its && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig -B/Users/<USER>/work/fontconfig/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build its/rebuild_cache: phony its/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build its/list_install_components: phony


#############################################
# Utility command for install

build its/CMakeFiles/install.util: CUSTOM_COMMAND its/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/its && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build its/install: phony its/CMakeFiles/install.util


#############################################
# Utility command for install/local

build its/CMakeFiles/install/local.util: CUSTOM_COMMAND its/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/its && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build its/install/local: phony its/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build its/CMakeFiles/install/strip.util: CUSTOM_COMMAND its/all
  COMMAND = cd /Users/<USER>/work/fontconfig/build/its && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build its/install/strip: phony its/CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build fc-cache: phony fc-cache/fc-cache

build fc-cat: phony fc-cat/fc-cat

build fc-conflist: phony fc-conflist/fc-conflist

build fc-list: phony fc-list/fc-list

build fc-match: phony fc-match/fc-match

build fc-pattern: phony fc-pattern/fc-pattern

build fc-query: phony fc-query/fc-query

build fc-scan: phony fc-scan/fc-scan

build fc-validate: phony fc-validate/fc-validate

build fccase_h: phony fc-case/fccase_h

build fclang_h: phony fc-lang/fclang_h

build fetch_test_fonts: phony test/fetch_test_fonts

build fontconfig: phony src/libfontconfig.dylib

build fontconfig_internal: phony src/libfontconfig_internal.a

build generated_headers: phony src/generated_headers

build lang_normalize_conf: phony conf.d/lang_normalize_conf

build libfontconfig.dylib: phony src/libfontconfig.dylib

build libfontconfig_internal.a: phony src/libfontconfig_internal.a

build patternlib_internal: phony src/patternlib_internal

build test_bz106618: phony test/test_bz106618

build test_bz106632: phony test/test_bz106632

build test_bz131804: phony test/test_bz131804

build test_bz1744377: phony test/test_bz1744377

build test_bz89617: phony test/test_bz89617

build test_bz96676: phony test/test_bz96676

build test_crbug1004254: phony test/test_crbug1004254

build test_family_matching: phony test/test_family_matching

build test_issue107: phony test/test_issue107

build test_issue180: phony test/test_issue180

build test_mt_fccfg: phony test/test_mt_fccfg

build test_name_parse: phony test/test_name_parse

build test_ptrlist: phony test/test_ptrlist

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build

build all: phony fontconfig/all fc-case/all fc-lang/all src/all fc-cache/all fc-cat/all fc-conflist/all fc-list/all fc-match/all fc-pattern/all fc-query/all fc-scan/all fc-validate/all test/all conf.d/all its/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/conf.d

build conf.d/all: phony conf.d/lang_normalize_conf

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-cache

build fc-cache/all: phony fc-cache/fc-cache

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-case

build fc-case/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-cat

build fc-cat/all: phony fc-cat/fc-cat

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-conflist

build fc-conflist/all: phony fc-conflist/fc-conflist

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-lang

build fc-lang/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-list

build fc-list/all: phony fc-list/fc-list

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-match

build fc-match/all: phony fc-match/fc-match

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-pattern

build fc-pattern/all: phony fc-pattern/fc-pattern

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-query

build fc-query/all: phony fc-query/fc-query

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-scan

build fc-scan/all: phony fc-scan/fc-scan

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fc-validate

build fc-validate/all: phony fc-validate/fc-validate

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/fontconfig

build fontconfig/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/its

build its/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/src

build src/all: phony src/patternlib_internal src/libfontconfig.dylib src/libfontconfig_internal.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build/test

build test/all: phony test/test_bz89617 test/test_bz131804 test/test_bz96676 test/test_name_parse test/test_bz106618 test/test_bz1744377 test/test_issue180 test/test_family_matching test/test_ptrlist test/test_bz106632 test/test_issue107 test/test_crbug1004254 test/test_mt_fccfg

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja /Users/<USER>/work/fontconfig/build/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fontconfig/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-case/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-lang/cmake_install.cmake /Users/<USER>/work/fontconfig/build/src/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-cache/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-cat/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-conflist/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-list/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-match/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-pattern/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-query/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-scan/cmake_install.cmake /Users/<USER>/work/fontconfig/build/fc-validate/cmake_install.cmake /Users/<USER>/work/fontconfig/build/test/cmake_install.cmake /Users/<USER>/work/fontconfig/build/conf.d/cmake_install.cmake /Users/<USER>/work/fontconfig/build/its/cmake_install.cmake /Users/<USER>/work/fontconfig/build/CTestTestfile.cmake /Users/<USER>/work/fontconfig/build/test/CTestTestfile.cmake /Users/<USER>/work/fontconfig/build/conf.d/CTestTestfile.cmake /Users/<USER>/work/fontconfig/build/its/CTestTestfile.cmake: RERUN_CMAKE | /Users/<USER>/work/fontconfig/CMakeLists.txt /Users/<USER>/work/fontconfig/cmake/ConfigureChecks.cmake /Users/<USER>/work/fontconfig/cmake/GenerateConfigFiles.cmake /Users/<USER>/work/fontconfig/cmake/config.h.in /Users/<USER>/work/fontconfig/cmake/fontconfigConfig.cmake.in /Users/<USER>/work/fontconfig/conf.d/CMakeLists.txt /Users/<USER>/work/fontconfig/conf.d/README.in /Users/<USER>/work/fontconfig/fc-cache/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-case/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-cat/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-conflist/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-lang/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-list/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-match/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-pattern/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-query/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-scan/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-validate/CMakeLists.txt /Users/<USER>/work/fontconfig/fontconfig.pc.in /Users/<USER>/work/fontconfig/fontconfig/CMakeLists.txt /Users/<USER>/work/fontconfig/fontconfig/fontconfig.h.in /Users/<USER>/work/fontconfig/fonts.conf.in /Users/<USER>/work/fontconfig/its/CMakeLists.txt /Users/<USER>/work/fontconfig/src/CMakeLists.txt /Users/<USER>/work/fontconfig/src/fcstdint.h.in /Users/<USER>/work/fontconfig/test/CMakeLists.txt /Users/<USER>/work/fontconfig/test/out.expected-long-family-names /usr/local/share/cmake/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /usr/local/share/cmake/Modules/CMakeCInformation.cmake /usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake/Modules/CMakePackageConfigHelpers.cmake /usr/local/share/cmake/Modules/CMakePushCheckState.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake /usr/local/share/cmake/Modules/CheckCSourceRuns.cmake /usr/local/share/cmake/Modules/CheckCXXSourceCompiles.cmake /usr/local/share/cmake/Modules/CheckFunctionExists.cmake /usr/local/share/cmake/Modules/CheckIncludeFile.cmake /usr/local/share/cmake/Modules/CheckIncludeFileCXX.cmake /usr/local/share/cmake/Modules/CheckLibraryExists.cmake /usr/local/share/cmake/Modules/CheckSourceCompiles.cmake /usr/local/share/cmake/Modules/CheckStructHasMember.cmake /usr/local/share/cmake/Modules/CheckSymbolExists.cmake /usr/local/share/cmake/Modules/CheckTypeSize.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake /usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake/Modules/Compiler/Clang.cmake /usr/local/share/cmake/Modules/Compiler/GNU.cmake /usr/local/share/cmake/Modules/FindIntl.cmake /usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /usr/local/share/cmake/Modules/FindPackageMessage.cmake /usr/local/share/cmake/Modules/FindPkgConfig.cmake /usr/local/share/cmake/Modules/FindPython/Support.cmake /usr/local/share/cmake/Modules/FindPython3.cmake /usr/local/share/cmake/Modules/FindThreads.cmake /usr/local/share/cmake/Modules/GNUInstallDirs.cmake /usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /usr/local/share/cmake/Modules/Internal/CheckSourceRuns.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake /usr/local/share/cmake/Modules/Linker/AppleClang.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake /usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake /usr/local/share/cmake/Modules/Platform/Darwin.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /usr/local/share/cmake/Modules/Platform/UnixPaths.cmake /usr/local/share/cmake/Modules/TestBigEndian.cmake /usr/local/share/cmake/Modules/WriteBasicConfigVersionFile.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/work/fontconfig/CMakeLists.txt /Users/<USER>/work/fontconfig/cmake/ConfigureChecks.cmake /Users/<USER>/work/fontconfig/cmake/GenerateConfigFiles.cmake /Users/<USER>/work/fontconfig/cmake/config.h.in /Users/<USER>/work/fontconfig/cmake/fontconfigConfig.cmake.in /Users/<USER>/work/fontconfig/conf.d/CMakeLists.txt /Users/<USER>/work/fontconfig/conf.d/README.in /Users/<USER>/work/fontconfig/fc-cache/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-case/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-cat/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-conflist/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-lang/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-list/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-match/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-pattern/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-query/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-scan/CMakeLists.txt /Users/<USER>/work/fontconfig/fc-validate/CMakeLists.txt /Users/<USER>/work/fontconfig/fontconfig.pc.in /Users/<USER>/work/fontconfig/fontconfig/CMakeLists.txt /Users/<USER>/work/fontconfig/fontconfig/fontconfig.h.in /Users/<USER>/work/fontconfig/fonts.conf.in /Users/<USER>/work/fontconfig/its/CMakeLists.txt /Users/<USER>/work/fontconfig/src/CMakeLists.txt /Users/<USER>/work/fontconfig/src/fcstdint.h.in /Users/<USER>/work/fontconfig/test/CMakeLists.txt /Users/<USER>/work/fontconfig/test/out.expected-long-family-names /usr/local/share/cmake/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /usr/local/share/cmake/Modules/CMakeCInformation.cmake /usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake/Modules/CMakePackageConfigHelpers.cmake /usr/local/share/cmake/Modules/CMakePushCheckState.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake /usr/local/share/cmake/Modules/CheckCSourceRuns.cmake /usr/local/share/cmake/Modules/CheckCXXSourceCompiles.cmake /usr/local/share/cmake/Modules/CheckFunctionExists.cmake /usr/local/share/cmake/Modules/CheckIncludeFile.cmake /usr/local/share/cmake/Modules/CheckIncludeFileCXX.cmake /usr/local/share/cmake/Modules/CheckLibraryExists.cmake /usr/local/share/cmake/Modules/CheckSourceCompiles.cmake /usr/local/share/cmake/Modules/CheckStructHasMember.cmake /usr/local/share/cmake/Modules/CheckSymbolExists.cmake /usr/local/share/cmake/Modules/CheckTypeSize.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake /usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake/Modules/Compiler/Clang.cmake /usr/local/share/cmake/Modules/Compiler/GNU.cmake /usr/local/share/cmake/Modules/FindIntl.cmake /usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /usr/local/share/cmake/Modules/FindPackageMessage.cmake /usr/local/share/cmake/Modules/FindPkgConfig.cmake /usr/local/share/cmake/Modules/FindPython/Support.cmake /usr/local/share/cmake/Modules/FindPython3.cmake /usr/local/share/cmake/Modules/FindThreads.cmake /usr/local/share/cmake/Modules/GNUInstallDirs.cmake /usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /usr/local/share/cmake/Modules/Internal/CheckSourceRuns.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake /usr/local/share/cmake/Modules/Linker/AppleClang.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake /usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake /usr/local/share/cmake/Modules/Platform/Darwin.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /usr/local/share/cmake/Modules/Platform/UnixPaths.cmake /usr/local/share/cmake/Modules/TestBigEndian.cmake /usr/local/share/cmake/Modules/WriteBasicConfigVersionFile.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
