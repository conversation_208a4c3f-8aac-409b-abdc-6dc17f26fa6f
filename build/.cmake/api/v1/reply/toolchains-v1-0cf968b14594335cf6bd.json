{"kind": "toolchains", "toolchains": [{"compiler": {"id": "AppleClang", "implicit": {"includeDirectories": ["/usr/local/include", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include"], "linkDirectories": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib"], "linkFrameworkDirectories": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"], "linkLibraries": []}, "path": "/usr/bin/clang", "version": "14.0.0.14000029"}, "language": "C", "sourceFileExtensions": ["c", "m"]}], "version": {"major": 1, "minor": 0}}