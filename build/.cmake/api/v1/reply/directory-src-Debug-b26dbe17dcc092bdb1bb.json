{"backtraceGraph": {"commands": ["install"], "files": ["src/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 205, "parent": 0}, {"command": 0, "file": 0, "line": 264, "parent": 0}, {"command": 0, "file": 0, "line": 285, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Runtime", "destination": "lib", "paths": ["src/libfontconfig.2.16.0.dylib", "src/libfontconfig.2.dylib"], "targetId": "fontconfig::@145eef247bfb46b6828c", "targetIndex": 12, "targetInstallNamelink": "skip", "type": "target"}, {"backtrace": 1, "component": "Development", "destination": "lib", "paths": ["src/libfontconfig.dylib"], "targetId": "fontconfig::@145eef247bfb46b6828c", "targetIndex": 12, "targetInstallNamelink": "only", "type": "target"}, {"backtrace": 2, "component": "Development", "destination": "lib/cmake/fontconfig", "exportName": "fontconfigTargets", "exportTargets": [{"id": "fontconfig::@145eef247bfb46b6828c", "index": 12}], "paths": ["src/CMakeFiles/Export/f949815ca86b7d55aaac5b532404bb82/fontconfigTargets.cmake"], "type": "export"}, {"backtrace": 3, "component": "Development", "destination": "lib/cmake/fontconfig", "paths": ["build/src/fontconfigConfig.cmake", "build/src/fontconfigConfigVersion.cmake"], "type": "file"}], "paths": {"build": "src", "source": "src"}}