{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["conf.d/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 139, "parent": 0}]}, "id": "lang_normalize_conf::@6f62672bd6593d5902fc", "name": "lang_normalize_conf", "paths": {"build": "conf.d", "source": "conf.d"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/conf.d/CMakeFiles/lang_normalize_conf", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/conf.d/CMakeFiles/lang_normalize_conf.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/conf.d/35-lang-normalize.conf.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}