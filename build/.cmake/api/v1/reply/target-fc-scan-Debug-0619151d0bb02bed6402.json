{"artifacts": [{"path": "fc-scan/fc-scan"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "add_dependencies", "add_compile_options", "add_compile_definitions", "include_directories", "target_include_directories"], "files": ["fc-scan/CMakeLists.txt", "src/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 1, "file": 0, "line": 23, "parent": 0}, {"command": 2, "file": 0, "line": 13, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 237, "parent": 4}, {"command": 2, "file": 1, "line": 247, "parent": 4}, {"command": 3, "file": 0, "line": 21, "parent": 0}, {"file": 2}, {"command": 4, "file": 2, "line": 74, "parent": 8}, {"command": 5, "file": 2, "line": 196, "parent": 8}, {"command": 6, "file": 2, "line": 206, "parent": 8}, {"command": 6, "file": 2, "line": 207, "parent": 8}, {"command": 7, "file": 0, "line": 5, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu11"}, {"backtrace": 9, "fragment": "-Wno-excess-initializers"}], "defines": [{"backtrace": 10, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 11, "path": "/Users/<USER>/work/fontconfig/build"}, {"backtrace": 12, "path": "/Users/<USER>/work/fontconfig/build/fc-lang"}, {"backtrace": 13, "path": "/Users/<USER>/work/fontconfig"}, {"backtrace": 13, "path": "/Users/<USER>/work/fontconfig/src"}, {"backtrace": 13, "path": "/Users/<USER>/work/fontconfig/build/fc-scan/../src"}, {"backtrace": 13, "path": "/Users/<USER>/work/fontconfig/build/fc-scan/.."}, {"backtrace": 13, "path": "/usr/local/opt/freetype/include/freetype2"}, {"backtrace": 13, "path": "/usr/local/opt/libpng/include/libpng16"}, {"backtrace": 3, "path": "/Users/<USER>/work/fontconfig/build/src"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 7, "id": "fccase_h::@6f692df06c1f38594375"}, {"backtrace": 7, "id": "fclang_h::@e795ec7c674a68a9d61d"}, {"backtrace": 3, "id": "fontconfig_internal::@145eef247bfb46b6828c"}], "id": "fc-scan::@d0b5815ee303cce3cd38", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 3, "fragment": "src/libfontconfig_internal.a", "role": "libraries"}, {"backtrace": 5, "fragment": "-lfreetype", "role": "libraries"}, {"backtrace": 5, "fragment": "-lexpat", "role": "libraries"}, {"backtrace": 6, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd", "role": "libraries"}], "language": "C"}, "name": "fc-scan", "nameOnDisk": "fc-scan", "paths": {"build": "fc-scan", "source": "fc-scan"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "fc-scan/fc-scan.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}