{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["src/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 123, "parent": 0}]}, "id": "generated_headers::@145eef247bfb46b6828c", "name": "generated_headers", "paths": {"build": "src", "source": "src"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2, 3, 4, 5]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/src/CMakeFiles/generated_headers", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/src/CMakeFiles/generated_headers.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/src/fcalias.h.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/src/fcftalias.h.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/src/fcobjshash.h.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/src/fcobjshash.gperf.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}