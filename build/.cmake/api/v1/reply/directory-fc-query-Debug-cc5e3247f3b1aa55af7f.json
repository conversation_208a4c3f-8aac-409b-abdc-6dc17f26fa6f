{"backtraceGraph": {"commands": ["install"], "files": ["fc-query/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Tools", "destination": "bin", "paths": ["fc-query/fc-query"], "targetId": "fc-query::@c70c80e4e8151512eecf", "targetIndex": 6, "type": "target"}], "paths": {"build": "fc-query", "source": "fc-query"}}