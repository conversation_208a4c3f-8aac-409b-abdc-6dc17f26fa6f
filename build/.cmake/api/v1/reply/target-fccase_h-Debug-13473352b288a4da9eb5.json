{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["fc-case/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 16, "parent": 0}]}, "id": "fccase_h::@6f692df06c1f38594375", "name": "fccase_h", "paths": {"build": "fc-case", "source": "fc-case"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/fc-case/CMakeFiles/fccase_h", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/fc-case/CMakeFiles/fccase_h.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/fc-case/fccase.h.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}