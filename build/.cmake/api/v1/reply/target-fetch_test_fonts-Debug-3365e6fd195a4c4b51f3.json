{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["test/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}]}, "id": "fetch_test_fonts::@36f028580bb02cc8272a", "name": "fetch_test_fonts", "paths": {"build": "test", "source": "test"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/test/CMakeFiles/fetch_test_fonts", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/test/CMakeFiles/fetch_test_fonts.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}