{"backtraceGraph": {"commands": ["install"], "files": ["conf.d/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 85, "parent": 0}, {"command": 0, "file": 0, "line": 91, "parent": 0}, {"command": 0, "file": 0, "line": 143, "parent": 0}, {"command": 0, "file": 0, "line": 156, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Runtime", "destination": "/usr/local/share/fontconfig/conf.avail", "paths": ["conf.d/05-reset-dirs-sample.conf", "conf.d/09-autohint-if-no-hinting.conf", "conf.d/10-autohint.conf", "conf.d/10-hinting-full.conf", "conf.d/10-hinting-medium.conf", "conf.d/10-hinting-none.conf", "conf.d/10-hinting-slight.conf", "conf.d/10-no-antialias.conf", "conf.d/10-scale-bitmap-fonts.conf", "conf.d/10-sub-pixel-bgr.conf", "conf.d/10-sub-pixel-none.conf", "conf.d/10-sub-pixel-rgb.conf", "conf.d/10-sub-pixel-vbgr.conf", "conf.d/10-sub-pixel-vrgb.conf", "conf.d/10-unhinted.conf", "conf.d/10-yes-antialias.conf", "conf.d/11-lcdfilter-default.conf", "conf.d/11-lcdfilter-legacy.conf", "conf.d/11-lcdfilter-light.conf", "conf.d/11-lcdfilter-none.conf", "conf.d/20-unhint-small-vera.conf", "conf.d/25-unhint-nonlatin.conf", "conf.d/30-metric-aliases.conf", "conf.d/40-nonlatin.conf", "conf.d/45-generic.conf", "conf.d/45-latin.conf", "conf.d/48-guessfamily.conf", "conf.d/48-spacing.conf", "conf.d/49-sansserif.conf", "conf.d/50-user.conf", "conf.d/51-local.conf", "conf.d/60-generic.conf", "conf.d/60-latin.conf", "conf.d/65-fonts-persian.conf", "conf.d/65-khmer.conf", "conf.d/65-nonlatin.conf", "conf.d/69-unifont.conf", "conf.d/70-no-bitmaps.conf", "conf.d/70-no-bitmaps-and-emoji.conf", "conf.d/70-no-bitmaps-except-emoji.conf", "conf.d/70-yes-bitmaps.conf", "conf.d/80-delicious.conf", "conf.d/90-synthetic.conf"], "type": "file"}, {"backtrace": 2, "component": "Runtime", "type": "code"}, {"backtrace": 3, "component": "Runtime", "destination": "/usr/local/share/fontconfig/conf.avail", "paths": ["build/conf.d/35-lang-normalize.conf"], "type": "file"}, {"backtrace": 4, "component": "Runtime", "destination": "/usr/local/etc/fonts/conf.d", "paths": ["build/conf.d/README"], "type": "file"}], "paths": {"build": "conf.d", "source": "conf.d"}}