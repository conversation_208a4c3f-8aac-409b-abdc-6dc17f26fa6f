{"backtraceGraph": {"commands": ["install", "include"], "files": ["cmake/GenerateConfigFiles.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 247, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 33, "parent": 2}, {"command": 0, "file": 0, "line": 54, "parent": 2}, {"command": 0, "file": 1, "line": 259, "parent": 0}]}, "installers": [{"backtrace": 3, "component": "Runtime", "destination": "/usr/local/etc/fonts", "paths": ["build/fonts.conf"], "type": "file"}, {"backtrace": 4, "component": "Development", "destination": "lib/pkgconfig", "paths": ["build/fontconfig.pc"], "type": "file"}, {"backtrace": 5, "component": "Runtime", "destination": "/usr/local/share/xml/fontconfig", "paths": ["fonts.dtd"], "type": "file"}], "paths": {"build": ".", "source": "."}}