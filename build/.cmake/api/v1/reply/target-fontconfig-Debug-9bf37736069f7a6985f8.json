{"artifacts": [{"path": "src/libfontconfig.dylib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "add_dependencies", "add_compile_options", "add_compile_definitions", "include_directories", "target_include_directories", "target_sources"], "files": ["src/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 144, "parent": 0}, {"command": 1, "file": 0, "line": 209, "parent": 0}, {"command": 2, "file": 0, "line": 177, "parent": 0}, {"command": 2, "file": 0, "line": 187, "parent": 0}, {"command": 3, "file": 0, "line": 147, "parent": 0}, {"file": 1}, {"command": 4, "file": 1, "line": 74, "parent": 6}, {"command": 5, "file": 1, "line": 196, "parent": 6}, {"command": 6, "file": 1, "line": 206, "parent": 6}, {"command": 6, "file": 1, "line": 207, "parent": 6}, {"command": 7, "file": 0, "line": 163, "parent": 0}, {"command": 8, "file": 0, "line": 184, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu11 -fPIC"}, {"backtrace": 7, "fragment": "-Wno-excess-initializers"}], "defines": [{"backtrace": 8, "define": "HAVE_CONFIG_H"}, {"define": "fontconfig_EXPORTS"}], "includes": [{"backtrace": 9, "path": "/Users/<USER>/work/fontconfig/build"}, {"backtrace": 10, "path": "/Users/<USER>/work/fontconfig/build/fc-lang"}, {"backtrace": 11, "path": "/Users/<USER>/work/fontconfig"}, {"backtrace": 11, "path": "/Users/<USER>/work/fontconfig/build/src"}, {"backtrace": 11, "path": "/Users/<USER>/work/fontconfig/build/src/.."}, {"backtrace": 11, "path": "/usr/local/opt/freetype/include/freetype2"}, {"backtrace": 11, "path": "/usr/local/opt/libpng/include/libpng16"}, {"backtrace": 11, "path": "/Users/<USER>/work/fontconfig/build/src/../fc-lang"}, {"backtrace": 11, "path": "/Users/<USER>/work/fontconfig/build/src/../fc-case"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}], "dependencies": [{"backtrace": 5, "id": "fccase_h::@6f692df06c1f38594375"}, {"backtrace": 5, "id": "fclang_h::@e795ec7c674a68a9d61d"}, {"id": "patternlib_internal::@145eef247bfb46b6828c"}, {"backtrace": 5, "id": "generated_headers::@145eef247bfb46b6828c"}], "id": "fontconfig::@145eef247bfb46b6828c", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "lib"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"backtrace": 3, "fragment": "-lfreetype", "role": "libraries"}, {"backtrace": 3, "fragment": "-lexpat", "role": "libraries"}, {"backtrace": 4, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd", "role": "libraries"}], "language": "C"}, "name": "fontconfig", "nameOnDisk": "libfontconfig.dylib", "paths": {"build": "src", "source": "src"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [27]}, {"name": "Object Libraries", "sourceIndexes": [28]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcatomic.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fccache.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fccfg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fccharset.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fccompat.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcdbg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcdefault.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcdir.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcformat.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcfreetype.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcfs.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcptrlist.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fchash.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcinit.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fclang.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fclist.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcmatch.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcmatrix.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcname.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcobjs.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcrange.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcserialize.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcstat.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcstr.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcweight.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcxml.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ftglue.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "build/src/fcstdint.h", "sourceGroupIndex": 1}, {"backtrace": 12, "isGenerated": true, "path": "build/src/CMakeFiles/patternlib_internal.dir/fcpat.c.o", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}