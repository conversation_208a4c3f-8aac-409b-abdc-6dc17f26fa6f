{"archive": {}, "artifacts": [{"path": "src/libfontconfig_internal.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_dependencies", "add_compile_options", "add_compile_definitions", "include_directories", "target_include_directories", "target_sources"], "files": ["src/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 221, "parent": 0}, {"command": 1, "file": 0, "line": 224, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 74, "parent": 3}, {"command": 3, "file": 1, "line": 196, "parent": 3}, {"command": 4, "file": 1, "line": 206, "parent": 3}, {"command": 4, "file": 1, "line": 207, "parent": 3}, {"command": 5, "file": 0, "line": 225, "parent": 0}, {"command": 6, "file": 0, "line": 244, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu11"}, {"backtrace": 4, "fragment": "-Wno-excess-initializers"}], "defines": [{"backtrace": 5, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 6, "path": "/Users/<USER>/work/fontconfig/build"}, {"backtrace": 7, "path": "/Users/<USER>/work/fontconfig/build/fc-lang"}, {"backtrace": 8, "path": "/Users/<USER>/work/fontconfig"}, {"backtrace": 8, "path": "/Users/<USER>/work/fontconfig/build/src"}, {"backtrace": 8, "path": "/Users/<USER>/work/fontconfig/build/src/.."}, {"backtrace": 8, "path": "/usr/local/opt/freetype/include/freetype2"}, {"backtrace": 8, "path": "/usr/local/opt/libpng/include/libpng16"}, {"backtrace": 8, "path": "/Users/<USER>/work/fontconfig/build/src/../fc-lang"}, {"backtrace": 8, "path": "/Users/<USER>/work/fontconfig/build/src/../fc-case"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}], "dependencies": [{"backtrace": 2, "id": "fccase_h::@6f692df06c1f38594375"}, {"backtrace": 2, "id": "fclang_h::@e795ec7c674a68a9d61d"}, {"id": "patternlib_internal::@145eef247bfb46b6828c"}, {"backtrace": 2, "id": "generated_headers::@145eef247bfb46b6828c"}], "id": "fontconfig_internal::@145eef247bfb46b6828c", "name": "fontconfig_internal", "nameOnDisk": "libfontconfig_internal.a", "paths": {"build": "src", "source": "src"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [27]}, {"name": "Object Libraries", "sourceIndexes": [28]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcatomic.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fccache.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fccfg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fccharset.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fccompat.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcdbg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcdefault.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcdir.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcformat.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcfreetype.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcfs.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcptrlist.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fchash.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcinit.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fclang.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fclist.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcmatch.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcmatrix.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcname.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcobjs.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcrange.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcserialize.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcstat.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcstr.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcweight.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcxml.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ftglue.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "build/src/fcstdint.h", "sourceGroupIndex": 1}, {"backtrace": 9, "isGenerated": true, "path": "build/src/CMakeFiles/patternlib_internal.dir/fcpat.c.o", "sourceGroupIndex": 2}], "type": "STATIC_LIBRARY"}