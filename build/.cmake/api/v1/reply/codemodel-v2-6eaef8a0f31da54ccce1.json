{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], "hasInstallRule": true, "jsonFile": "directory-.-Debug-025dcdcc4755f3e6d01b.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": "."}, {"build": "fontconfig", "hasInstallRule": true, "jsonFile": "directory-fontconfig-Debug-6ed3e6d00b96619055ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fontconfig"}, {"build": "fc-case", "jsonFile": "directory-fc-case-Debug-a1d67888e9149623854c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-case", "targetIndexes": [9]}, {"build": "fc-lang", "jsonFile": "directory-fc-lang-Debug-7ca81de762210544a4d9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-lang", "targetIndexes": [10]}, {"build": "src", "hasInstallRule": true, "jsonFile": "directory-src-Debug-0282f8c09924054c79a2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "src", "targetIndexes": [12, 13, 14, 16]}, {"build": "fc-cache", "hasInstallRule": true, "jsonFile": "directory-fc-cache-Debug-f47a22be86494057f400.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-cache", "targetIndexes": [0]}, {"build": "fc-cat", "hasInstallRule": true, "jsonFile": "directory-fc-cat-Debug-9b3b88f643c2147acebc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-cat", "targetIndexes": [1]}, {"build": "fc-conflist", "hasInstallRule": true, "jsonFile": "directory-fc-conflist-Debug-412a89999d88f2e4032b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-conflist", "targetIndexes": [2]}, {"build": "fc-list", "hasInstallRule": true, "jsonFile": "directory-fc-list-Debug-21b930f3efd5a985f8c2.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-list", "targetIndexes": [3]}, {"build": "fc-match", "hasInstallRule": true, "jsonFile": "directory-fc-match-Debug-d2569305cd5840adba5c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-match", "targetIndexes": [4]}, {"build": "fc-pattern", "hasInstallRule": true, "jsonFile": "directory-fc-pattern-Debug-872204d5d9b45e205e99.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-pattern", "targetIndexes": [5]}, {"build": "fc-query", "hasInstallRule": true, "jsonFile": "directory-fc-query-Debug-cc5e3247f3b1aa55af7f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-query", "targetIndexes": [6]}, {"build": "fc-scan", "hasInstallRule": true, "jsonFile": "directory-fc-scan-Debug-bbc770ee3844d2750990.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-scan", "targetIndexes": [7]}, {"build": "fc-validate", "hasInstallRule": true, "jsonFile": "directory-fc-validate-Debug-8b3e3e4da1384accf03b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "fc-validate", "targetIndexes": [8]}, {"build": "test", "jsonFile": "directory-test-Debug-451c0598f41488bb20b9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "test", "targetIndexes": [11, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]}, {"build": "conf.d", "hasInstallRule": true, "jsonFile": "directory-conf.d-Debug-56eda61985874c94d47d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "conf.d", "targetIndexes": [15]}, {"build": "its", "hasInstallRule": true, "jsonFile": "directory-its-Debug-00013d54682e1a0c61bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "its"}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], "name": "fontconfig", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]}], "targets": [{"directoryIndex": 5, "id": "fc-cache::@8cda09f4e9841d05ca91", "jsonFile": "target-fc-cache-Debug-9f26aa710ae85559912e.json", "name": "fc-cache", "projectIndex": 0}, {"directoryIndex": 6, "id": "fc-cat::@a51083f9fab36e69faea", "jsonFile": "target-fc-cat-Debug-a660592c3aefbf147b22.json", "name": "fc-cat", "projectIndex": 0}, {"directoryIndex": 7, "id": "fc-conflist::@456f1a01ad28e124a593", "jsonFile": "target-fc-conflist-Debug-332111d8d32f0ea2d0c0.json", "name": "fc-conflist", "projectIndex": 0}, {"directoryIndex": 8, "id": "fc-list::@8aab16a8a69038dd929d", "jsonFile": "target-fc-list-Debug-4ac6cf1c659028a34a34.json", "name": "fc-list", "projectIndex": 0}, {"directoryIndex": 9, "id": "fc-match::@37ce643736b414800495", "jsonFile": "target-fc-match-Debug-bdde3a8b8924fc9bfe69.json", "name": "fc-match", "projectIndex": 0}, {"directoryIndex": 10, "id": "fc-pattern::@d53174bc8bd1aaf776ad", "jsonFile": "target-fc-pattern-Debug-d0ab06c2f26a2416f902.json", "name": "fc-pattern", "projectIndex": 0}, {"directoryIndex": 11, "id": "fc-query::@c70c80e4e8151512eecf", "jsonFile": "target-fc-query-Debug-69a4255833132d22b237.json", "name": "fc-query", "projectIndex": 0}, {"directoryIndex": 12, "id": "fc-scan::@d0b5815ee303cce3cd38", "jsonFile": "target-fc-scan-Debug-0619151d0bb02bed6402.json", "name": "fc-scan", "projectIndex": 0}, {"directoryIndex": 13, "id": "fc-validate::@3330a24540bbf4b808b5", "jsonFile": "target-fc-validate-Debug-a37af0d7bb6cf6e62096.json", "name": "fc-validate", "projectIndex": 0}, {"directoryIndex": 2, "id": "fccase_h::@6f692df06c1f38594375", "jsonFile": "target-fccase_h-Debug-13473352b288a4da9eb5.json", "name": "fccase_h", "projectIndex": 0}, {"directoryIndex": 3, "id": "fclang_h::@e795ec7c674a68a9d61d", "jsonFile": "target-fclang_h-Debug-67e0291364d3c4fbc395.json", "name": "fclang_h", "projectIndex": 0}, {"directoryIndex": 14, "id": "fetch_test_fonts::@36f028580bb02cc8272a", "jsonFile": "target-fetch_test_fonts-Debug-3365e6fd195a4c4b51f3.json", "name": "fetch_test_fonts", "projectIndex": 0}, {"directoryIndex": 4, "id": "fontconfig::@145eef247bfb46b6828c", "jsonFile": "target-fontconfig-Debug-9bf37736069f7a6985f8.json", "name": "fontconfig", "projectIndex": 0}, {"directoryIndex": 4, "id": "fontconfig_internal::@145eef247bfb46b6828c", "jsonFile": "target-fontconfig_internal-Debug-f24e6e353a6910b03980.json", "name": "fontconfig_internal", "projectIndex": 0}, {"directoryIndex": 4, "id": "generated_headers::@145eef247bfb46b6828c", "jsonFile": "target-generated_headers-Debug-133d447c1a5c69f08e7d.json", "name": "generated_headers", "projectIndex": 0}, {"directoryIndex": 15, "id": "lang_normalize_conf::@6f62672bd6593d5902fc", "jsonFile": "target-lang_normalize_conf-Debug-aa5809c44e56bfb19ae8.json", "name": "lang_normalize_conf", "projectIndex": 0}, {"directoryIndex": 4, "id": "patternlib_internal::@145eef247bfb46b6828c", "jsonFile": "target-patternlib_internal-Debug-2bf3b19ec30f5500ef70.json", "name": "patternlib_internal", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_bz106618::@36f028580bb02cc8272a", "jsonFile": "target-test_bz106618-Debug-f5ce550822cadbf3f33f.json", "name": "test_bz106618", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_bz106632::@36f028580bb02cc8272a", "jsonFile": "target-test_bz106632-Debug-c6ca2eab35fa915e2f3e.json", "name": "test_bz106632", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_bz131804::@36f028580bb02cc8272a", "jsonFile": "target-test_bz131804-Debug-ea5d37728470c94d9a37.json", "name": "test_bz131804", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_bz1744377::@36f028580bb02cc8272a", "jsonFile": "target-test_bz1744377-Debug-f4ed9cc6a5316cf6fdde.json", "name": "test_bz1744377", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_bz89617::@36f028580bb02cc8272a", "jsonFile": "target-test_bz89617-Debug-1c893e5dd730d1f3ab11.json", "name": "test_bz89617", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_bz96676::@36f028580bb02cc8272a", "jsonFile": "target-test_bz96676-Debug-7568e162fe67e256f9db.json", "name": "test_bz96676", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_crbug1004254::@36f028580bb02cc8272a", "jsonFile": "target-test_crbug1004254-Debug-f8ff567c200582b66ace.json", "name": "test_crbug1004254", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_family_matching::@36f028580bb02cc8272a", "jsonFile": "target-test_family_matching-Debug-116c9b87d58eb10bbcf8.json", "name": "test_family_matching", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_issue107::@36f028580bb02cc8272a", "jsonFile": "target-test_issue107-Debug-b12514dd66000884e571.json", "name": "test_issue107", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_issue180::@36f028580bb02cc8272a", "jsonFile": "target-test_issue180-Debug-79581bd17f940b57bbb2.json", "name": "test_issue180", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_mt_fccfg::@36f028580bb02cc8272a", "jsonFile": "target-test_mt_fccfg-Debug-d615dc08fa8903dc6c69.json", "name": "test_mt_fccfg", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_name_parse::@36f028580bb02cc8272a", "jsonFile": "target-test_name_parse-Debug-e725eac409b6e95c231c.json", "name": "test_name_parse", "projectIndex": 0}, {"directoryIndex": 14, "id": "test_ptrlist::@36f028580bb02cc8272a", "jsonFile": "target-test_ptrlist-Debug-e37d0c5be178a81f253e.json", "name": "test_ptrlist", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/work/fontconfig/build", "source": "/Users/<USER>/work/fontconfig"}, "version": {"major": 2, "minor": 8}}