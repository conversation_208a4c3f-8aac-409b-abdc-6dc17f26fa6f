{"artifacts": [{"path": "src/CMakeFiles/patternlib_internal.dir/./fcpat.c.o"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_dependencies", "add_compile_options", "add_compile_definitions", "include_directories", "target_include_directories"], "files": ["src/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}, {"command": 1, "file": 0, "line": 36, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 72, "parent": 3}, {"command": 3, "file": 1, "line": 194, "parent": 3}, {"command": 4, "file": 1, "line": 204, "parent": 3}, {"command": 4, "file": 1, "line": 205, "parent": 3}, {"command": 5, "file": 0, "line": 24, "parent": 0}, {"command": 5, "file": 0, "line": 30, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu11"}, {"backtrace": 4, "fragment": "-Wno-excess-initializers"}], "defines": [{"backtrace": 5, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 6, "path": "/Users/<USER>/work/fontconfig/build"}, {"backtrace": 7, "path": "/Users/<USER>/work/fontconfig/build/fc-lang"}, {"backtrace": 8, "path": "/Users/<USER>/work/fontconfig"}, {"backtrace": 8, "path": "/Users/<USER>/work/fontconfig/build/src/.."}, {"backtrace": 8, "path": "/Users/<USER>/work/fontconfig/build/src"}, {"backtrace": 9, "path": "/usr/local/opt/freetype/include/freetype2"}, {"backtrace": 9, "path": "/usr/local/opt/libpng/include/libpng16"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "fccase_h::@6f692df06c1f38594375"}, {"backtrace": 2, "id": "fclang_h::@e795ec7c674a68a9d61d"}, {"backtrace": 2, "id": "generated_headers::@145eef247bfb46b6828c"}], "id": "patternlib_internal::@145eef247bfb46b6828c", "name": "patternlib_internal", "paths": {"build": "src", "source": "src"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/fcpat.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "build/src/fcstdint.h", "sourceGroupIndex": 1}], "type": "OBJECT_LIBRARY"}