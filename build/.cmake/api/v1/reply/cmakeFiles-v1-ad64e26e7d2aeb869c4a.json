{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Darwin.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/GNUInstallDirs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckTypeSize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCSourceRuns.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CheckSourceRuns.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/WriteBasicConfigVersionFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindIntl.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakePushCheckState.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPython3.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPython/Support.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/TestBigEndian.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckTypeSize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"path": "cmake/ConfigureChecks.cmake"}, {"path": "cmake/GenerateConfigFiles.cmake"}, {"path": "cmake/config.h.in"}, {"path": "fonts.conf.in"}, {"path": "fontconfig.pc.in"}, {"path": "fontconfig/CMakeLists.txt"}, {"path": "fontconfig/fontconfig.h.in"}, {"path": "fc-case/CMakeLists.txt"}, {"path": "fc-lang/CMakeLists.txt"}, {"path": "src/CMakeLists.txt"}, {"path": "src/fcstdint.h.in"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/WriteBasicConfigVersionFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/BasicConfigVersion-SameMajorVersion.cmake.in"}, {"path": "cmake/fontconfigConfig.cmake.in"}, {"path": "fc-cache/CMakeLists.txt"}, {"path": "fc-cat/CMakeLists.txt"}, {"path": "fc-conflist/CMakeLists.txt"}, {"path": "fc-list/CMakeLists.txt"}, {"path": "fc-match/CMakeLists.txt"}, {"path": "fc-pattern/CMakeLists.txt"}, {"path": "fc-query/CMakeLists.txt"}, {"path": "fc-scan/CMakeLists.txt"}, {"path": "fc-validate/CMakeLists.txt"}, {"path": "test/CMakeLists.txt"}, {"path": "test/out.expected-long-family-names"}, {"path": "conf.d/CMakeLists.txt"}, {"path": "conf.d/README.in"}, {"path": "its/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/work/fontconfig/build", "source": "/Users/<USER>/work/fontconfig"}, "version": {"major": 1, "minor": 1}}