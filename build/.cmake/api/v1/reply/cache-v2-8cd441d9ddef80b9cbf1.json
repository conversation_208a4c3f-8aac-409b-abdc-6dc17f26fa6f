{"entries": [{"name": "ADDITIONAL_FONTS_DIRS", "properties": [{"name": "HELPSTRING", "value": "Additional font directories (semicolon separated)"}], "type": "STRING", "value": ""}, {"name": "BITMAP_CONF", "properties": [{"name": "HELPSTRING", "value": "Default bitmap font configuration"}, {"name": "STRINGS", "value": "yes;no;no-except-emoji"}], "type": "STRING", "value": "no-except-emoji"}, {"name": "CMAKE_ADDR2LINE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_ADDR2LINE-NOTFOUND"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ar"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "/Users/<USER>/work/fontconfig/build"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "4"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "0"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "2"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "/usr/local/bin/cmake"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "/usr/local/bin/cpack"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "/usr/local/bin/ctest"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/usr/bin/clang++"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/usr/bin/clang"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_DLLTOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_DLLTOOL-NOTFOUND"}, {"name": "CMAKE_EDIT_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cache edit program executable."}], "type": "INTERNAL", "value": "/usr/local/bin/ccmake"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "MACHO"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXPORT_BUILD_DATABASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of build database during the build."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "BOOL", "value": "TRUE"}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "/Users/<USER>/work/fontconfig/build/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Ninja"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "/Users/<USER>/work/fontconfig"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_NAME_TOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/install_name_tool"}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "/usr/local"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ld"}, {"name": "CMAKE_LIST_FILE_NAME", "properties": [{"name": "HELPSTRING", "value": "Name of CMakeLists files to read"}], "type": "INTERNAL", "value": "CMakeLists.txt"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program used to build from build.ninja files."}], "type": "FILEPATH", "value": "/usr/local/bin/ninja"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_NM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/nm"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "17"}, {"name": "CMAKE_OBJCOPY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_OBJCOPY-NOTFOUND"}, {"name": "CMAKE_OBJDUMP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/objdump"}, {"name": "CMAKE_OSX_ARCHITECTURES", "properties": [{"name": "HELPSTRING", "value": "Build architectures for OSX"}], "type": "STRING", "value": ""}, {"name": "CMAKE_OSX_DEPLOYMENT_TARGET", "properties": [{"name": "HELPSTRING", "value": "Minimum OS X version to target for deployment (at runtime); newer APIs weak linked. Set to empty string for default value."}], "type": "STRING", "value": ""}, {"name": "CMAKE_OSX_SYSROOT", "properties": [{"name": "HELPSTRING", "value": "The product will be built against the headers and libraries located inside the indicated SDK."}], "type": "STRING", "value": ""}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "Font configuration and customization library"}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "fontconfig"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "2.17.1"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "2"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "17"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1"}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ranlib"}, {"name": "CMAKE_READELF", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_READELF-NOTFOUND"}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "/usr/local/share/cmake"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STRIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/strip"}, {"name": "CMAKE_TAPI", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi"}, {"name": "CMAKE_UNAME", "properties": [{"name": "HELPSTRING", "value": "uname command"}], "type": "INTERNAL", "value": "/usr/bin/uname"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "DEFAULT_FONTS_DIRS", "properties": [{"name": "HELPSTRING", "value": "Default font directories (semicolon separated)"}], "type": "STRING", "value": ""}, {"name": "DEFAULT_HINTING", "properties": [{"name": "HELPSTRING", "value": "Default hinting configuration"}, {"name": "STRINGS", "value": "none;slight;medium;full"}], "type": "STRING", "value": "slight"}, {"name": "DEFAULT_SUB_PIXEL_RENDERING", "properties": [{"name": "HELPSTRING", "value": "Default sub-pixel rendering"}, {"name": "STRINGS", "value": "none;bgr;rgb;vbgr;vrgb"}], "type": "STRING", "value": "none"}, {"name": "ENABLE_CACHE_BUILD", "properties": [{"name": "HELPSTRING", "value": "Run fc-cache on install"}], "type": "BOOL", "value": "ON"}, {"name": "ENABLE_DOCS", "properties": [{"name": "HELPSTRING", "value": "Build documentation"}], "type": "BOOL", "value": "OFF"}, {"name": "ENABLE_FONTATIONS", "properties": [{"name": "HELPSTRING", "value": "Use Fontations for indexing"}], "type": "BOOL", "value": "OFF"}, {"name": "ENABLE_ICONV", "properties": [{"name": "HELPSTRING", "value": "Enable iconv support"}], "type": "BOOL", "value": "OFF"}, {"name": "ENABLE_NLS", "properties": [{"name": "HELPSTRING", "value": "Enable native language support"}], "type": "BOOL", "value": "ON"}, {"name": "ENABLE_TESTS", "properties": [{"name": "HELPSTRING", "value": "Enable unit tests"}], "type": "BOOL", "value": "ON"}, {"name": "ENABLE_TOOLS", "properties": [{"name": "HELPSTRING", "value": "Build command-line tools"}], "type": "BOOL", "value": "ON"}, {"name": "EXPAT_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "EXPAT_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include"}, {"name": "EXPAT_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib;-lexpat"}, {"name": "EXPAT_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "EXPAT_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "expat"}, {"name": "EXPAT_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "EXPAT_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "expat"}, {"name": "EXPAT_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr"}, {"name": "EXPAT_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib;-lexpat"}, {"name": "EXPAT_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "expat"}, {"name": "EXPAT_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "EXPAT_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "2.4.1"}, {"name": "EXPAT_expat_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_expat_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_expat_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EXPAT_expat_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FC_BASECONFIGDIR", "properties": [{"name": "HELPSTRING", "value": "Base config directory"}], "type": "PATH", "value": "/usr/local/etc/fonts"}, {"name": "FC_CACHEDIR", "properties": [{"name": "HELPSTRING", "value": "Cache directory"}], "type": "PATH", "value": "/usr/local/var/cache/fontconfig"}, {"name": "FC_CONFIGDIR", "properties": [{"name": "HELPSTRING", "value": "Config directory"}], "type": "PATH", "value": "/usr/local/etc/fonts/conf.d"}, {"name": "FC_TEMPLATEDIR", "properties": [{"name": "HELPSTRING", "value": "Template directory"}], "type": "PATH", "value": "/usr/local/share/fontconfig/conf.avail"}, {"name": "FC_XMLDIR", "properties": [{"name": "HELPSTRING", "value": "XML directory"}], "type": "PATH", "value": "/usr/local/share/xml/fontconfig"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig", "properties": [{"name": "HELPSTRING", "value": "Details about finding PkgConfig"}], "type": "INTERNAL", "value": "[/usr/local/bin/pkg-config][v2.4.3()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Python3", "properties": [{"name": "HELPSTRING", "value": "Details about finding Python3"}], "type": "INTERNAL", "value": "[/usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13][found components: Interpreter ][v3.13.3()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FREETYPE2_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/local/opt/freetype/include/freetype2;-I/usr/local/opt/libpng/include/libpng16"}, {"name": "FREETYPE2_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "FREETYPE2_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/opt/freetype/include"}, {"name": "FREETYPE2_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/opt/freetype/include/freetype2;/usr/local/opt/libpng/include/libpng16"}, {"name": "FREETYPE2_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/local/opt/freetype/lib;-lfreetype"}, {"name": "FREETYPE2_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/opt/freetype/lib"}, {"name": "FREETYPE2_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "freetype"}, {"name": "FREETYPE2_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/opt/freetype/lib"}, {"name": "FREETYPE2_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "freetype2"}, {"name": "FREETYPE2_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/opt/freetype"}, {"name": "FREETYPE2_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/local/opt/freetype/include/freetype2;-I/usr/local/opt/libpng/include/libpng16"}, {"name": "FREETYPE2_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/opt/freetype/include/freetype2;/usr/local/opt/libpng/include/libpng16"}, {"name": "FREETYPE2_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/local/opt/freetype/lib;-lfreetype;-lbz2;-L/usr/local/opt/libpng/lib;-lpng16;-L/usr/lib;-lz"}, {"name": "FREETYPE2_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "freetype;bz2;png16;z"}, {"name": "FREETYPE2_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/opt/freetype/lib;/usr/local/opt/libpng/lib;/usr/lib"}, {"name": "FREETYPE2_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "26.2.20"}, {"name": "FREETYPE2_freetype2_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_freetype2_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_freetype2_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE2_freetype2_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "FREETYPE_PCF_LONG_FAMILY_NAMES", "properties": [{"name": "HELPSTRING", "value": "Test FREETYPE_PCF_LONG_FAMILY_NAMES"}], "type": "INTERNAL", "value": "1"}, {"name": "GPERF_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/gperf"}, {"name": "HAVE_DCGETTEXT", "properties": [{"name": "HELPSTRING", "value": "Have function dcgettext"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_DIRENT_H", "properties": [{"name": "HELPSTRING", "value": "Have include dirent.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_DLFCN_H", "properties": [{"name": "HELPSTRING", "value": "Have include dlfcn.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FCNTL_H", "properties": [{"name": "HELPSTRING", "value": "Have include fcntl.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FLEXIBLE_ARRAY_MEMBER", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_FLEXIBLE_ARRAY_MEMBER"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FSTATFS", "properties": [{"name": "HELPSTRING", "value": "Have function fstatfs"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FSTATVFS", "properties": [{"name": "HELPSTRING", "value": "Have function fstatvfs"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FT_DONE_MM_VAR", "properties": [{"name": "HELPSTRING", "value": "Have function FT_Done_MM_Var"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FT_GET_BDF_PROPERTY", "properties": [{"name": "HELPSTRING", "value": "Have function FT_Get_BDF_Property"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FT_GET_PS_FONT_INFO", "properties": [{"name": "HELPSTRING", "value": "Have function FT_Get_PS_Font_Info"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FT_GET_X11_FONT_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Have function FT_Get_X11_Font_Format"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FT_HAS_PS_GLYPH_NAMES", "properties": [{"name": "HELPSTRING", "value": "Have function FT_Has_PS_Glyph_Names"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_GETEXECNAME", "properties": [{"name": "HELPSTRING", "value": "Have function getexecname"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_GETOPT", "properties": [{"name": "HELPSTRING", "value": "Have function getopt"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_GETOPT_LONG", "properties": [{"name": "HELPSTRING", "value": "Have function getopt_long"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_GETPAGESIZE", "properties": [{"name": "HELPSTRING", "value": "Have function getpagesize"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_GETPID", "properties": [{"name": "HELPSTRING", "value": "Have function getpid"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_GETPROGNAME", "properties": [{"name": "HELPSTRING", "value": "Have function getprogname"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_GETTEXT", "properties": [{"name": "HELPSTRING", "value": "Have function gettext"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_INT32_T", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "HAVE_INTEL_ATOMIC_PRIMITIVES", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_INTEL_ATOMIC_PRIMITIVES"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_INTPTR_T", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "HAVE_INTTYPES_H", "properties": [{"name": "HELPSTRING", "value": "Have include inttypes.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_LINK", "properties": [{"name": "HELPSTRING", "value": "Have function link"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_LOCALTIME_R", "properties": [{"name": "HELPSTRING", "value": "Have function localtime_r"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_LRAND48", "properties": [{"name": "HELPSTRING", "value": "Have function lrand48"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_LSTAT", "properties": [{"name": "HELPSTRING", "value": "Have function lstat"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_MKDTEMP", "properties": [{"name": "HELPSTRING", "value": "Have function mkdtemp"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_MKOSTEMP", "properties": [{"name": "HELPSTRING", "value": "Have function mkostemp"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_MKSTEMP", "properties": [{"name": "HELPSTRING", "value": "Have function mkstemp"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_MMAP", "properties": [{"name": "HELPSTRING", "value": "Have function mmap"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_POSIX_FADVISE", "properties": [{"name": "HELPSTRING", "value": "Have symbol posix_fadvise"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_PTHREAD_PRIO_INHERIT", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_PTHREAD_PRIO_INHERIT"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_RAND", "properties": [{"name": "HELPSTRING", "value": "Have function rand"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_RANDOM", "properties": [{"name": "HELPSTRING", "value": "Have function random"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_RANDOM_R", "properties": [{"name": "HELPSTRING", "value": "Have function random_r"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_RAND_R", "properties": [{"name": "HELPSTRING", "value": "Have function rand_r"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_READLINK", "properties": [{"name": "HELPSTRING", "value": "Have function readlink"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SIZEOF_VOID_P", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "HAVE_SOLARIS_ATOMIC_OPS", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_SOLARIS_ATOMIC_OPS"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_STDATOMIC_PRIMITIVES", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STDATOMIC_PRIMITIVES"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STDDEF_H", "properties": [{"name": "HELPSTRING", "value": "Have include stddef.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STDINT_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdint.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STDIO_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdio.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STDLIB_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdlib.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STRERROR", "properties": [{"name": "HELPSTRING", "value": "Have function strerror"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STRERROR_R", "properties": [{"name": "HELPSTRING", "value": "Have function strerror_r"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STRINGS_H", "properties": [{"name": "HELPSTRING", "value": "Have include strings.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STRING_H", "properties": [{"name": "HELPSTRING", "value": "Have include string.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STRUCT_DIRENT_D_TYPE", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STRUCT_DIRENT_D_TYPE"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STRUCT_STATFS_F_FLAGS", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STRUCT_STATFS_F_FLAGS"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_STRUCT_STATFS_F_FSTYPENAME", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STRUCT_STATFS_F_FSTYPENAME"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_STRUCT_STATVFS_F_BASETYPE", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STRUCT_STATVFS_F_BASETYPE"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_STRUCT_STATVFS_F_FSTYPENAME", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STRUCT_STATVFS_F_FSTYPENAME"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_STRUCT_STAT_ST_MTIM", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STRUCT_STAT_ST_MTIM"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_SYS_MOUNT_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/mount.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SYS_PARAM_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/param.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SYS_STATFS_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/statfs.h"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_SYS_STATVFS_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/statvfs.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SYS_STAT_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/stat.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SYS_TYPES_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/types.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SYS_VFS_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/vfs.h"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_TIME_H", "properties": [{"name": "HELPSTRING", "value": "Have include time.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_UINT64_T", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "HAVE_UINTPTR_T", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "HAVE_UNISTD_H", "properties": [{"name": "HELPSTRING", "value": "Have include unistd.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_VPRINTF", "properties": [{"name": "HELPSTRING", "value": "Have function vprintf"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_VSNPRINTF", "properties": [{"name": "HELPSTRING", "value": "Have function vsnprintf"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_VSPRINTF", "properties": [{"name": "HELPSTRING", "value": "Have function vsprintf"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_WCHAR_H", "properties": [{"name": "HELPSTRING", "value": "Have include wchar.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE__MKTEMP_S", "properties": [{"name": "HELPSTRING", "value": "Have function _mktemp_s"}], "type": "INTERNAL", "value": ""}, {"name": "INT32_T", "properties": [{"name": "HELPSTRING", "value": "CHECK_TYPE_SIZE: sizeof(int32_t)"}], "type": "INTERNAL", "value": "4"}, {"name": "INTPTR_T", "properties": [{"name": "HELPSTRING", "value": "CHECK_TYPE_SIZE: sizeof(intptr_t)"}], "type": "INTERNAL", "value": "8"}, {"name": "Intl_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "libintl include directory"}], "type": "PATH", "value": "Intl_INCLUDE_DIR-NOTFOUND"}, {"name": "Intl_IS_BUILT_IN", "properties": [{"name": "HELPSTRING", "value": "Test Intl_IS_BUILT_IN"}], "type": "INTERNAL", "value": ""}, {"name": "Intl_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "libintl libraries (if not in the C library)"}], "type": "FILEPATH", "value": "/usr/local/lib/libintl.dylib"}, {"name": "JSONC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_json-c_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_json-c_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_json-c_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONC_json-c_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "MATH_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd"}, {"name": "PKG_CONFIG_ARGN", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Arguments to supply to pkg-config"}], "type": "STRING", "value": ""}, {"name": "PKG_CONFIG_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "pkg-config executable"}], "type": "FILEPATH", "value": "/usr/local/bin/pkg-config"}, {"name": "PYTEST_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "PYTEST_EXECUTABLE-NOTFOUND"}, {"name": "SIZEOF_VOID_P", "properties": [{"name": "HELPSTRING", "value": "CHECK_TYPE_SIZE: sizeof(void *)"}], "type": "INTERNAL", "value": "8"}, {"name": "UINT64_T", "properties": [{"name": "HELPSTRING", "value": "CHECK_TYPE_SIZE: sizeof(uint64_t)"}], "type": "INTERNAL", "value": "8"}, {"name": "UINTPTR_T", "properties": [{"name": "HELPSTRING", "value": "CHECK_TYPE_SIZE: sizeof(uintptr_t)"}], "type": "INTERNAL", "value": "8"}, {"name": "XML_BACKEND", "properties": [{"name": "HELPSTRING", "value": "XML backend to use (auto, expat, libxml2)"}, {"name": "STRINGS", "value": "auto;expat;libxml2"}], "type": "STRING", "value": "auto"}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "/usr/local"}, {"name": "_Python3_Compiler_REASON_FAILURE", "properties": [{"name": "HELPSTRING", "value": "Compiler reason failure"}], "type": "INTERNAL", "value": ""}, {"name": "_Python3_Development_REASON_FAILURE", "properties": [{"name": "HELPSTRING", "value": "Development reason failure"}], "type": "INTERNAL", "value": ""}, {"name": "_Python3_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "INTERNAL", "value": "/usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13"}, {"name": "_Python3_INTERPRETER_PROPERTIES", "properties": [{"name": "HELPSTRING", "value": "Python3 Properties"}], "type": "INTERNAL", "value": "Python;3;13;3;64;32;<none>;cpython-313-dar<PERSON>;abi3;/usr/local/opt/python@3.13/Frameworks/Python.framework/Versions/3.13/lib/python3.13;/usr/local/opt/python@3.13/Frameworks/Python.framework/Versions/3.13/lib/python3.13;/usr/local/lib/python3.13/site-packages;/usr/local/lib/python3.13/site-packages"}, {"name": "_Python3_INTERPRETER_SIGNATURE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "133e2ff1251621b076d962675dda2845"}, {"name": "_Python3_NumPy_REASON_FAILURE", "properties": [{"name": "HELPSTRING", "value": "NumPy reason failure"}], "type": "INTERNAL", "value": ""}, {"name": "__pkg_config_arguments_EXPAT", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "expat"}, {"name": "__pkg_config_arguments_FREETYPE2", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;freetype2>=21.0.15"}, {"name": "__pkg_config_checked_EXPAT", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_FREETYPE2", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_JSONC", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "fontconfig_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/work/fontconfig/build"}, {"name": "fontconfig_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "fontconfig_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/work/fontconfig"}, {"name": "pkgcfg_lib_EXPAT_expat", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libexpat.tbd"}, {"name": "pkgcfg_lib_FREETYPE2_freetype", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/opt/freetype/lib/libfreetype.dylib"}, {"name": "prefix_result", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}], "kind": "cache", "version": {"major": 2, "minor": 0}}