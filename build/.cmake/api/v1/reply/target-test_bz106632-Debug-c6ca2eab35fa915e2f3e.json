{"artifacts": [{"path": "test/test_bz106632"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "add_dependencies", "add_compile_options", "target_compile_definitions", "add_compile_definitions", "include_directories", "target_include_directories"], "files": ["test/CMakeLists.txt", "src/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 52, "parent": 0}, {"command": 1, "file": 0, "line": 62, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 237, "parent": 3}, {"command": 1, "file": 1, "line": 247, "parent": 3}, {"command": 2, "file": 0, "line": 82, "parent": 0}, {"file": 2}, {"command": 3, "file": 2, "line": 74, "parent": 7}, {"command": 4, "file": 0, "line": 70, "parent": 0}, {"command": 5, "file": 2, "line": 196, "parent": 7}, {"command": 6, "file": 2, "line": 206, "parent": 7}, {"command": 6, "file": 2, "line": 207, "parent": 7}, {"command": 7, "file": 0, "line": 57, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu11"}, {"backtrace": 8, "fragment": "-Wno-excess-initializers"}], "defines": [{"backtrace": 9, "define": "FONTFILE=\"/Users/<USER>/work/fontconfig/test/4x6.pcf\""}, {"backtrace": 10, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 11, "path": "/Users/<USER>/work/fontconfig/build"}, {"backtrace": 12, "path": "/Users/<USER>/work/fontconfig/build/fc-lang"}, {"backtrace": 13, "path": "/Users/<USER>/work/fontconfig"}, {"backtrace": 13, "path": "/Users/<USER>/work/fontconfig/build/test/../src"}, {"backtrace": 2, "path": "/Users/<USER>/work/fontconfig/build/src"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 6, "id": "fccase_h::@6f692df06c1f38594375"}, {"backtrace": 6, "id": "fclang_h::@e795ec7c674a68a9d61d"}, {"backtrace": 2, "id": "fontconfig_internal::@145eef247bfb46b6828c"}], "id": "test_bz106632::@36f028580bb02cc8272a", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 2, "fragment": "src/libfontconfig_internal.a", "role": "libraries"}, {"backtrace": 4, "fragment": "-lfreetype", "role": "libraries"}, {"backtrace": 4, "fragment": "-lexpat", "role": "libraries"}, {"backtrace": 5, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd", "role": "libraries"}], "language": "C"}, "name": "test_bz106632", "nameOnDisk": "test_bz106632", "paths": {"build": "test", "source": "test"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "test/test-bz106632.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "build/src/fcstdint.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}