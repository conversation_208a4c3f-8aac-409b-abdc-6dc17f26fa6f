{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["fc-lang/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 61, "parent": 0}]}, "id": "fclang_h::@e795ec7c674a68a9d61d", "name": "fclang_h", "paths": {"build": "fc-lang", "source": "fc-lang"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/fc-lang/CMakeFiles/fclang_h", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/fc-lang/CMakeFiles/fclang_h.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/fc-lang/fclang.h.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}