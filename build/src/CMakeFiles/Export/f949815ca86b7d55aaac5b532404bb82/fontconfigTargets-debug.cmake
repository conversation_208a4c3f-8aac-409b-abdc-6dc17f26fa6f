#----------------------------------------------------------------
# Generated CMake target import file for configuration "Debug".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "fontconfig::fontconfig" for configuration "Debug"
set_property(TARGET fontconfig::fontconfig APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG)
set_target_properties(fontconfig::fontconfig PROPERTIES
  IMPORTED_LOCATION_DEBUG "${_IMPORT_PREFIX}/lib/libfontconfig.2.16.0.dylib"
  IMPORTED_SONAME_DEBUG "@rpath/libfontconfig.2.dylib"
  )

list(APPEND _cmake_import_check_targets fontconfig::fontconfig )
list(APPEND _cmake_import_check_files_for_fontconfig::fontconfig "${_IMPORT_PREFIX}/lib/libfontconfig.2.16.0.dylib" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
