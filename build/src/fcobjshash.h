/* ANSI-C code produced by gperf version 3.0.3 */
/* Command-line: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/gperf --pic -m 100 --output-file /Users/<USER>/work/fontconfig/build/src/fcobjshash.h /Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf  */
/* Computed positions: -k'4-5,9' */

#if !((' ' == 32) && ('!' == 33) && ('"' == 34) && ('#' == 35) \
      && ('%' == 37) && ('&' == 38) && ('\'' == 39) && ('(' == 40) \
      && (')' == 41) && ('*' == 42) && ('+' == 43) && (',' == 44) \
      && ('-' == 45) && ('.' == 46) && ('/' == 47) && ('0' == 48) \
      && ('1' == 49) && ('2' == 50) && ('3' == 51) && ('4' == 52) \
      && ('5' == 53) && ('6' == 54) && ('7' == 55) && ('8' == 56) \
      && ('9' == 57) && (':' == 58) && (';' == 59) && ('<' == 60) \
      && ('=' == 61) && ('>' == 62) && ('?' == 63) && ('A' == 65) \
      && ('B' == 66) && ('C' == 67) && ('D' == 68) && ('E' == 69) \
      && ('F' == 70) && ('G' == 71) && ('H' == 72) && ('I' == 73) \
      && ('J' == 74) && ('K' == 75) && ('L' == 76) && ('M' == 77) \
      && ('N' == 78) && ('O' == 79) && ('P' == 80) && ('Q' == 81) \
      && ('R' == 82) && ('S' == 83) && ('T' == 84) && ('U' == 85) \
      && ('V' == 86) && ('W' == 87) && ('X' == 88) && ('Y' == 89) \
      && ('Z' == 90) && ('[' == 91) && ('\\' == 92) && (']' == 93) \
      && ('^' == 94) && ('_' == 95) && ('a' == 97) && ('b' == 98) \
      && ('c' == 99) && ('d' == 100) && ('e' == 101) && ('f' == 102) \
      && ('g' == 103) && ('h' == 104) && ('i' == 105) && ('j' == 106) \
      && ('k' == 107) && ('l' == 108) && ('m' == 109) && ('n' == 110) \
      && ('o' == 111) && ('p' == 112) && ('q' == 113) && ('r' == 114) \
      && ('s' == 115) && ('t' == 116) && ('u' == 117) && ('v' == 118) \
      && ('w' == 119) && ('x' == 120) && ('y' == 121) && ('z' == 122) \
      && ('{' == 123) && ('|' == 124) && ('}' == 125) && ('~' == 126))
/* The character set is not based on ISO-646.  */
#error "gperf generated tables don't work with this execution character set. Please report a bug to <<EMAIL>>."
#endif

#line 1 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"

#line 13 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
struct FcObjectTypeInfo {
int name;
int id;
};
#include <string.h>
#include <stddef.h>
/* maximum key range = 63, duplicates = 0 */

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
static unsigned int
FcObjectTypeHash (register const char *str, register unsigned int len)
{
  static const unsigned char asso_values[] =
    {
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79,  9, 30,  5,  5, 30,
       5, 34,  4,  4, 79, 79, 12, 17,  6,  4,
      10, 79, 21, 25, 17, 23, 15, 20, 32, 32,
      79, 79, 79, 79, 79, 29, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79, 79, 79, 79, 79,
      79, 79, 79, 79, 79, 79
    };
  register unsigned int hval = len;

  switch (hval)
    {
      default:
        hval += asso_values[(unsigned char)str[8]];
      /*FALLTHROUGH*/
      case 8:
      case 7:
      case 6:
      case 5:
        hval += asso_values[(unsigned char)str[4]];
      /*FALLTHROUGH*/
      case 4:
        hval += asso_values[(unsigned char)str[3]];
        break;
    }
  return hval;
}

struct FcObjectTypeNamePool_t
  {
    char FcObjectTypeNamePool_str16[sizeof("FC_FILE")];
    char FcObjectTypeNamePool_str17[sizeof("FC_COLOR")];
    char FcObjectTypeNamePool_str18[sizeof("FC_INDEX")];
    char FcObjectTypeNamePool_str20[sizeof("FC_HASH")];
    char FcObjectTypeNamePool_str21[sizeof("FC_DPI")];
    char FcObjectTypeNamePool_str24[sizeof("FC_HINTING")];
    char FcObjectTypeNamePool_str25[sizeof("FC_CHARWIDTH")];
    char FcObjectTypeNamePool_str26[sizeof("FC_FONTFORMAT")];
    char FcObjectTypeNamePool_str27[sizeof("FC_CHAR_HEIGHT")];
    char FcObjectTypeNamePool_str28[sizeof("FC_LANG")];
    char FcObjectTypeNamePool_str29[sizeof("FC_FONT_HAS_HINT")];
    char FcObjectTypeNamePool_str30[sizeof("FC_FONT_FEATURES")];
    char FcObjectTypeNamePool_str31[sizeof("FC_CAPABILITY")];
    char FcObjectTypeNamePool_str32[sizeof("FC_WIDTH")];
    char FcObjectTypeNamePool_str33[sizeof("FC_ORDER")];
    char FcObjectTypeNamePool_str34[sizeof("FC_LCD_FILTER")];
    char FcObjectTypeNamePool_str35[sizeof("FC_NAMELANG")];
    char FcObjectTypeNamePool_str36[sizeof("FC_SIZE")];
    char FcObjectTypeNamePool_str37[sizeof("FC_POSTSCRIPT_NAME")];
    char FcObjectTypeNamePool_str38[sizeof("FC_SCALE")];
    char FcObjectTypeNamePool_str39[sizeof("FC_ANTIALIAS")];
    char FcObjectTypeNamePool_str40[sizeof("FC_FOUNDRY")];
    char FcObjectTypeNamePool_str41[sizeof("FC_MINSPACE")];
    char FcObjectTypeNamePool_str42[sizeof("FC_FONT_VARIATIONS")];
    char FcObjectTypeNamePool_str43[sizeof("FC_OUTLINE")];
    char FcObjectTypeNamePool_str44[sizeof("FC_FONT_WRAPPER")];
    char FcObjectTypeNamePool_str45[sizeof("FC_SLANT")];
    char FcObjectTypeNamePool_str46[sizeof("FC_HINT_STYLE")];
    char FcObjectTypeNamePool_str47[sizeof("FC_AUTOHINT")];
    char FcObjectTypeNamePool_str48[sizeof("FC_FULLNAME")];
    char FcObjectTypeNamePool_str49[sizeof("FC_CHARSET")];
    char FcObjectTypeNamePool_str50[sizeof("FC_STYLE")];
    char FcObjectTypeNamePool_str51[sizeof("FC_SPACING")];
    char FcObjectTypeNamePool_str52[sizeof("FC_FULLNAMELANG")];
    char FcObjectTypeNamePool_str53[sizeof("FC_FONTVERSION")];
    char FcObjectTypeNamePool_str54[sizeof("FC_DESKTOP_NAME")];
    char FcObjectTypeNamePool_str55[sizeof("FC_FAMILY")];
    char FcObjectTypeNamePool_str56[sizeof("FC_PIXEL_SIZE")];
    char FcObjectTypeNamePool_str57[sizeof("FC_DECORATIVE")];
    char FcObjectTypeNamePool_str58[sizeof("FC_PRGNAME")];
    char FcObjectTypeNamePool_str59[sizeof("FC_FAMILYLANG")];
    char FcObjectTypeNamePool_str60[sizeof("FC_ASPECT")];
    char FcObjectTypeNamePool_str61[sizeof("FC_NAMED_INSTANCE")];
    char FcObjectTypeNamePool_str62[sizeof("FC_RGBA")];
    char FcObjectTypeNamePool_str63[sizeof("FC_EMBOLDEN")];
    char FcObjectTypeNamePool_str64[sizeof("FC_RASTERIZER")];
    char FcObjectTypeNamePool_str65[sizeof("FC_VARIABLE")];
    char FcObjectTypeNamePool_str66[sizeof("FC_STYLELANG")];
    char FcObjectTypeNamePool_str67[sizeof("FC_MATRIX")];
    char FcObjectTypeNamePool_str68[sizeof("FC_VERTICAL_LAYOUT")];
    char FcObjectTypeNamePool_str70[sizeof("FC_EMBEDDED_BITMAP")];
    char FcObjectTypeNamePool_str71[sizeof("FC_SCALABLE")];
    char FcObjectTypeNamePool_str75[sizeof("FC_GLOBAL_ADVANCE")];
    char FcObjectTypeNamePool_str76[sizeof("FC_WEIGHT")];
    char FcObjectTypeNamePool_str78[sizeof("FC_SYMBOL")];
  };
static const struct FcObjectTypeNamePool_t FcObjectTypeNamePool_contents =
  {
    "FC_FILE",
    "FC_COLOR",
    "FC_INDEX",
    "FC_HASH",
    "FC_DPI",
    "FC_HINTING",
    "FC_CHARWIDTH",
    "FC_FONTFORMAT",
    "FC_CHAR_HEIGHT",
    "FC_LANG",
    "FC_FONT_HAS_HINT",
    "FC_FONT_FEATURES",
    "FC_CAPABILITY",
    "FC_WIDTH",
    "FC_ORDER",
    "FC_LCD_FILTER",
    "FC_NAMELANG",
    "FC_SIZE",
    "FC_POSTSCRIPT_NAME",
    "FC_SCALE",
    "FC_ANTIALIAS",
    "FC_FOUNDRY",
    "FC_MINSPACE",
    "FC_FONT_VARIATIONS",
    "FC_OUTLINE",
    "FC_FONT_WRAPPER",
    "FC_SLANT",
    "FC_HINT_STYLE",
    "FC_AUTOHINT",
    "FC_FULLNAME",
    "FC_CHARSET",
    "FC_STYLE",
    "FC_SPACING",
    "FC_FULLNAMELANG",
    "FC_FONTVERSION",
    "FC_DESKTOP_NAME",
    "FC_FAMILY",
    "FC_PIXEL_SIZE",
    "FC_DECORATIVE",
    "FC_PRGNAME",
    "FC_FAMILYLANG",
    "FC_ASPECT",
    "FC_NAMED_INSTANCE",
    "FC_RGBA",
    "FC_EMBOLDEN",
    "FC_RASTERIZER",
    "FC_VARIABLE",
    "FC_STYLELANG",
    "FC_MATRIX",
    "FC_VERTICAL_LAYOUT",
    "FC_EMBEDDED_BITMAP",
    "FC_SCALABLE",
    "FC_GLOBAL_ADVANCE",
    "FC_WEIGHT",
    "FC_SYMBOL"
  };
#define FcObjectTypeNamePool ((const char *) &FcObjectTypeNamePool_contents)
const struct FcObjectTypeInfo *
FcObjectTypeLookup (register const char *str, register unsigned int len)
{
  enum
    {
      TOTAL_KEYWORDS = 55,
      MIN_WORD_LENGTH = 6,
      MAX_WORD_LENGTH = 18,
      MIN_HASH_VALUE = 16,
      MAX_HASH_VALUE = 78
    };

  static const struct FcObjectTypeInfo wordlist[] =
    {
      {-1}, {-1}, {-1}, {-1}, {-1}, {-1}, {-1}, {-1}, {-1},
      {-1}, {-1}, {-1}, {-1}, {-1}, {-1}, {-1},
#line 38 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str16),FC_FILE_OBJECT},
#line 64 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str17),FC_COLOR_OBJECT},
#line 39 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str18),FC_INDEX_OBJECT},
      {-1},
#line 62 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str20),FC_HASH_OBJECT},
#line 43 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str21),FC_DPI_OBJECT},
      {-1}, {-1},
#line 34 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str24),FC_HINTING_OBJECT},
#line 47 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str25),FC_CHARWIDTH_OBJECT},
#line 54 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str26),FC_FONTFORMAT_OBJECT},
#line 48 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str27),FC_CHAR_HEIGHT_OBJECT},
#line 51 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str28),FC_LANG_OBJECT},
#line 68 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str29),FC_FONT_HAS_HINT_OBJECT},
#line 60 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str30),FC_FONT_FEATURES_OBJECT},
#line 53 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str31),FC_CAPABILITY_OBJECT},
#line 26 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str32),FC_WIDTH_OBJECT},
#line 69 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str33),FC_ORDER_OBJECT},
#line 58 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str34),FC_LCD_FILTER_OBJECT},
#line 59 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str35),FC_NAMELANG_OBJECT},
#line 27 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str36),FC_SIZE_OBJECT},
#line 63 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str37),FC_POSTSCRIPT_NAME_OBJECT},
#line 45 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str38),FC_SCALE_OBJECT},
#line 32 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str39),FC_ANTIALIAS_OBJECT},
#line 31 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str40),FC_FOUNDRY_OBJECT},
#line 46 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str41),FC_MINSPACE_OBJECT},
#line 66 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str42),FC_FONT_VARIATIONS_OBJECT},
#line 41 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str43),FC_OUTLINE_OBJECT},
#line 72 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str44),FC_FONT_WRAPPER_OBJECT},
#line 24 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str45),FC_SLANT_OBJECT},
#line 33 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str46),FC_HINT_STYLE_OBJECT},
#line 36 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str47),FC_AUTOHINT_OBJECT},
#line 22 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str48),FC_FULLNAME_OBJECT},
#line 50 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str49),FC_CHARSET_OBJECT},
#line 20 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str50),FC_STYLE_OBJECT},
#line 30 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str51),FC_SPACING_OBJECT},
#line 23 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str52),FC_FULLNAMELANG_OBJECT},
#line 52 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str53),FC_FONTVERSION_OBJECT},
#line 70 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str54),FC_DESKTOP_NAME_OBJECT},
#line 18 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str55),FC_FAMILY_OBJECT},
#line 29 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str56),FC_PIXEL_SIZE_OBJECT},
#line 57 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str57),FC_DECORATIVE_OBJECT},
#line 61 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str58),FC_PRGNAME_OBJECT},
#line 19 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str59),FC_FAMILYLANG_OBJECT},
#line 28 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str60),FC_ASPECT_OBJECT},
#line 71 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str61),FC_NAMED_INSTANCE_OBJECT},
#line 44 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str62),FC_RGBA_OBJECT},
#line 55 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str63),FC_EMBOLDEN_OBJECT},
#line 40 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str64),FC_RASTERIZER_OBJECT},
#line 67 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str65),FC_VARIABLE_OBJECT},
#line 21 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str66),FC_STYLELANG_OBJECT},
#line 49 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str67),FC_MATRIX_OBJECT},
#line 35 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str68),FC_VERTICAL_LAYOUT_OBJECT},
      {-1},
#line 56 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str70),FC_EMBEDDED_BITMAP_OBJECT},
#line 42 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str71),FC_SCALABLE_OBJECT},
      {-1}, {-1}, {-1},
#line 37 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str75),FC_GLOBAL_ADVANCE_OBJECT},
#line 25 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str76),FC_WEIGHT_OBJECT},
      {-1},
#line 65 "/Users/<USER>/work/fontconfig/build/src/fcobjshash.gperf"
      {offsetof(struct FcObjectTypeNamePool_t, FcObjectTypeNamePool_str78),FC_SYMBOL_OBJECT}
    };

  if (len <= MAX_WORD_LENGTH && len >= MIN_WORD_LENGTH)
    {
      unsigned int key = FcObjectTypeHash (str, len);

      if (key <= MAX_HASH_VALUE)
        {
          register int o = wordlist[key].name;
          if (o >= 0)
            {
              register const char *s = o + FcObjectTypeNamePool;

              if (*str == *s && !strcmp (str + 1, s + 1))
                return &wordlist[key];
            }
        }
    }
  return 0;
}
