/*
 * fontconfig/src/fcobjshash.gperf.h
 *
 * Copyright © 2000 <PERSON> Packard
 *
 * Permission to use, copy, modify, distribute, and sell this software and its
 * documentation for any purpose is hereby granted without fee, provided that
 * the above copyright notice appear in all copies and that both that
 * copyright notice and this permission notice appear in supporting
 * documentation, and that the name of the author(s) not be used in
 * advertising or publicity pertaining to distribution of the software without
 * specific, written prior permission.  The authors make no
 * representations about the suitability of this software for any purpose.  It
 * is provided "as is" without express or implied warranty.
 *
 * THE AUTHOR(S) DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
 * INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
 * EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
 * CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
 * <PERSON>ATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
 * TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */
%{
#include <fontconfig/fontconfig.h>
%}
%struct-type
%language=ANSI-C
%includes
%enum
%readonly-tables
%define slot-name name
%define hash-function-name FcObjectTypeHash
%define lookup-function-name FcObjectTypeLookup

%pic
%define string-pool-name FcObjectTypeNamePool

struct FcObjectTypeInfo {
	int name;
	int id;
};

%%
"family",FC_FAMILY_OBJECT
"familylang",FC_FAMILYLANG_OBJECT
"style",FC_STYLE_OBJECT
"stylelang",FC_STYLELANG_OBJECT
"fullname",FC_FULLNAME_OBJECT
"fullnamelang",FC_FULLNAMELANG_OBJECT
"slant",FC_SLANT_OBJECT
"weight",FC_WEIGHT_OBJECT
"width",FC_WIDTH_OBJECT
"size",FC_SIZE_OBJECT
"aspect",FC_ASPECT_OBJECT
"pixel_size",FC_PIXEL_SIZE_OBJECT
"spacing",FC_SPACING_OBJECT
"foundry",FC_FOUNDRY_OBJECT
"antialias",FC_ANTIALIAS_OBJECT
"hint_style",FC_HINT_STYLE_OBJECT
"hinting",FC_HINTING_OBJECT
"vertical_layout",FC_VERTICAL_LAYOUT_OBJECT
"autohint",FC_AUTOHINT_OBJECT
"global_advance",FC_GLOBAL_ADVANCE_OBJECT
"file",FC_FILE_OBJECT
"index",FC_INDEX_OBJECT
"rasterizer",FC_RASTERIZER_OBJECT
"outline",FC_OUTLINE_OBJECT
"scalable",FC_SCALABLE_OBJECT
"dpi",FC_DPI_OBJECT
"rgba",FC_RGBA_OBJECT
"scale",FC_SCALE_OBJECT
"minspace",FC_MINSPACE_OBJECT
"charwidth",FC_CHARWIDTH_OBJECT
"char_height",FC_CHAR_HEIGHT_OBJECT
"matrix",FC_MATRIX_OBJECT
"charset",FC_CHARSET_OBJECT
"lang",FC_LANG_OBJECT
"fontversion",FC_FONTVERSION_OBJECT
"capability",FC_CAPABILITY_OBJECT
"fontformat",FC_FONTFORMAT_OBJECT
"embolden",FC_EMBOLDEN_OBJECT
"embedded_bitmap",FC_EMBEDDED_BITMAP_OBJECT
"decorative",FC_DECORATIVE_OBJECT
"lcd_filter",FC_LCD_FILTER_OBJECT
"namelang",FC_NAMELANG_OBJECT
"font_features",FC_FONT_FEATURES_OBJECT
"prgname",FC_PRGNAME_OBJECT
"hash",FC_HASH_OBJECT
"postscript_name",FC_POSTSCRIPT_NAME_OBJECT
"color",FC_COLOR_OBJECT
"symbol",FC_SYMBOL_OBJECT
"font_variations",FC_FONT_VARIATIONS_OBJECT
"variable",FC_VARIABLE_OBJECT
"font_has_hint",FC_FONT_HAS_HINT_OBJECT
"order",FC_ORDER_OBJECT
"desktop_name",FC_DESKTOP_NAME_OBJECT
"named_instance",FC_NAMED_INSTANCE_OBJECT
"font_wrapper",FC_FONT_WRAPPER_OBJECT
