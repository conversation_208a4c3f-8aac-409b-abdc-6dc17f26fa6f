# CMake generated Testfile for 
# Source directory: /Users/<USER>/work/fontconfig
# Build directory: /Users/<USER>/work/fontconfig/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
subdirs("fontconfig")
subdirs("fc-case")
subdirs("fc-lang")
subdirs("src")
subdirs("fc-cache")
subdirs("fc-cat")
subdirs("fc-conflist")
subdirs("fc-list")
subdirs("fc-match")
subdirs("fc-pattern")
subdirs("fc-query")
subdirs("fc-scan")
subdirs("fc-validate")
subdirs("test")
subdirs("conf.d")
subdirs("its")
