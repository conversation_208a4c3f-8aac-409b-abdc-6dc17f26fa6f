# fontconfig-conf translation to Georgian.
# Copyright (C) 2022 Fontconfig Author(s)
# This file is distributed under the same license as the fontconfig package.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: fontconfig 2.14.0\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/fontconfig/fontconfig/"
"issues/new\n"
"POT-Creation-Date: 2022-10-01 10:16+0200\n"
"PO-Revision-Date: 2022-10-01 11:01+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Georgian <(nothing)>\n"
"Language: ka\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.1.1\n"

#: conf.d/10-autohint.conf:4
msgid "Enable autohinter"
msgstr "autohinter-ის ჩართვა"

#: conf.d/10-hinting-full.conf:4
msgid "Set hintfull to hintstyle"
msgstr "hintstyle-ის hintfull -ზე დაყენება"

#: conf.d/10-hinting-medium.conf:4
msgid "Set hintmedium to hintstyle"
msgstr "hintstyle-ის hintmedium-ზე დაყენება"

#: conf.d/10-hinting-none.conf:4
msgid "Set hintnone to hintstyle"
msgstr "hintstyle-ის hintnone-ზე დაყენება"

#: conf.d/10-hinting-slight.conf:4
msgid "Set hintslight to hintstyle"
msgstr "hintstyle-ის hintslight-ზე დაყენება"

#: conf.d/10-no-sub-pixel.conf:4
msgid "Disable sub-pixel rendering"
msgstr "ქვეპიქსელის რენდერის გამორთვა"

#: conf.d/10-scale-bitmap-fonts.conf:4
msgid "Bitmap scaling"
msgstr "ბიტური რუკის მასშტაბირება"

#: conf.d/10-sub-pixel-bgr.conf:4
msgid "Enable sub-pixel rendering with the BGR stripes layout"
msgstr "ქვეპიქსელების რენდერის ჩართვა BGR ზოლების განლაგებით"

#: conf.d/10-sub-pixel-rgb.conf:4
msgid "Enable sub-pixel rendering with the RGB stripes layout"
msgstr "ქვეპიქსელების რენდერის ჩართვა RGB ზოლების განლაგებით"

#: conf.d/10-sub-pixel-vbgr.conf:4
msgid "Enable sub-pixel rendering with the vertical BGR stripes layout"
msgstr "ქვეპიქსელების რენდერის ჩართვა ვერტიკალური BGR ზოლების განლაგებით"

#: conf.d/10-sub-pixel-vrgb.conf:4
msgid "Enable sub-pixel rendering with the vertical RGB stripes layout"
msgstr "ქვეპიქსელების რენდერის ჩართვა ვერტიკალური RGB ზოლების განლაგებით"

#: conf.d/10-unhinted.conf:4
msgid "Disable hinting"
msgstr "მომრგვალების გამორთვა"

#: conf.d/11-lcdfilter-default.conf:4
msgid "Use lcddefault as default for LCD filter"
msgstr "ნაგულისხმებ LCD ფილტრად lcddefault -ის გამოყენება"

#: conf.d/11-lcdfilter-legacy.conf:4
msgid "Use lcdlegacy as default for LCD filter"
msgstr "ნაგულისხმებ LCD ფილტრად lcdlegacy -ის გამოყენება"

#: conf.d/11-lcdfilter-light.conf:4
msgid "Use lcdlight as default for LCD filter"
msgstr "ნაგულისხმებ LCD ფილტრად lcdlight -ის გამოყენება"

#: conf.d/20-unhint-small-vera.conf:4
msgid ""
"Disable hinting for Bitstream Vera fonts when the size is less than 8ppem"
msgstr ""
"მომრგვალების გამორთვა Bitsream Vera-ის ფონტებისთვის, როცა ზომა 8ppem-ზე "
"მცირეა"

#: conf.d/25-unhint-nonlatin.conf:4
msgid "Disable hinting for CJK fonts"
msgstr "CJK ფონტების მომრგვალების გამორთვა"

#: conf.d/30-metric-aliases.conf:4
msgid "Set substitutions for similar/metric-compatible families"
msgstr "მსგავსი/მეტრულთან-თავსებადი ოჯახების ჩანაცვლებების დაყენება"

#: conf.d/40-nonlatin.conf:4
msgid "Set substitutions for non-Latin fonts"
msgstr "არა-Latn ფონტების ჩანაცვლებების დაყენება"

#: conf.d/45-generic.conf:4
msgid "Set substitutions for emoji/math fonts"
msgstr "ემოჯი/მათემატიკური ფონტების ჩანაცვლებების დაყენება"

#: conf.d/45-latin.conf:4
msgid "Set substitutions for Latin fonts"
msgstr "Latin ფონტების ჩამნაცვლებლების დაყენება"

#: conf.d/49-sansserif.conf:4
msgid "Add sans-serif to the family when no generic name"
msgstr "როცა ზოგადი სახელი არ არსებობს, ოჯახისთვის sans-serif-ის დამატება"

#: conf.d/50-user.conf:4
msgid "Load per-user customization files"
msgstr "მომხმარებლების საკუთარი კონფიგურაციის ფაილების ჩატვირთვა"

#: conf.d/51-local.conf:4
msgid "Load local customization file"
msgstr "ლოკალური კონფიგურაციის ფაილის ჩაატვირთვა"

#: conf.d/60-generic.conf:4
msgid "Set preferable fonts for emoji/math fonts"
msgstr "ემოჯი/მათემატიკური ფონტებისთვის უმჯობესი ფონტების დაყენება"

#: conf.d/60-latin.conf:4
msgid "Set preferable fonts for Latin"
msgstr "Latin-სთვის უმჯობესი ფონტების დაყენება"

#: conf.d/65-nonlatin.conf:4
msgid "Set preferable fonts for non-Latin"
msgstr "არა-Latin-სთვის უმჯობესი ფონტების დაყენება"

#: conf.d/70-no-bitmaps.conf:4
msgid "Reject bitmap fonts"
msgstr "ბიტური რუკების ფონტების უარყოფა"

#: conf.d/70-yes-bitmaps.conf:4
msgid "Accept bitmap fonts"
msgstr "ბიტური რუკების ფონტების მიღება"
