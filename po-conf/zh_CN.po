# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR Fontconfig Author(s)
# This file is distributed under the same license as the fontconfig package.
#
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018.
# <AUTHOR> <EMAIL>, 2018.
# <PERSON><PERSON> <<EMAIL>>, 2018.
#
msgid ""
msgstr ""
"Project-Id-Version: fontconfig 2.12.92\n"
"Report-Msgid-Bugs-To: https://gitlab.freedesktop.org/fontconfig/"
"fontconfig/issues/new\n"
"POT-Creation-Date: 2018-02-14 21:06-0600\n"
"PO-Revision-Date: 2018-02-16 01:19-0600\n"
"Language-Team: AOSC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.5\n"
"Last-Translator: <PERSON>con<PERSON> <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Language: zh_CN\n"

#: conf.d/10-autohint.conf:8
msgid "Enable autohinter"
msgstr "启用自动微调"

#: conf.d/10-hinting-full.conf:8
msgid "Set hintfull to hintstyle"
msgstr "设置微调风格为“完全 (hintfull)”"

#: conf.d/10-hinting-medium.conf:8
msgid "Set hintmedium to hintstyle"
msgstr "设置微调风格为“中等 (hintmedium)”"

#: conf.d/10-hinting-none.conf:8
msgid "Set hintnone to hintstyle"
msgstr "设置微调风格为“无 (hintnone)”"

#: conf.d/10-hinting-slight.conf:8
msgid "Set hintslight to hintstyle"
msgstr "设置微调风格为“轻微 (hintslight)”"

#: conf.d/10-no-sub-pixel.conf:8
msgid "Disable sub-pixel rendering"
msgstr "禁用次像素渲染"

#: conf.d/10-scale-bitmap-fonts.conf:8
msgid "Bitmap scaling"
msgstr "位图缩放"

#: conf.d/10-sub-pixel-bgr.conf:8
msgid "Enable sub-pixel rendering with the BGR stripes layout"
msgstr "启用蓝绿红 (BGR) 像素布局的次像素渲染"

#: conf.d/10-sub-pixel-rgb.conf:8
msgid "Enable sub-pixel rendering with the RGB stripes layout"
msgstr "启用红绿蓝 (RGB) 像素布局的次像素渲染"

#: conf.d/10-sub-pixel-vbgr.conf:8
msgid "Enable sub-pixel rendering with the vertical BGR stripes layout"
msgstr "启用垂直蓝绿红 (BGR) 像素布局的次像素渲染"

#: conf.d/10-sub-pixel-vrgb.conf:8
msgid "Enable sub-pixel rendering with the vertical RGB stripes layout"
msgstr "启用垂直红绿蓝 (RGB) 像素布局的次像素渲染"

#: conf.d/10-unhinted.conf:8
msgid "Disable hinting"
msgstr "禁用微调"

#: conf.d/11-lcdfilter-default.conf:8
msgid "Use lcddefault as default for LCD filter"
msgstr "将 lcddefault 设为默认 LCD 滤镜"

#: conf.d/11-lcdfilter-legacy.conf:8
msgid "Use lcdlegacy as default for LCD filter"
msgstr "将 lcdlegacy 设为默认 LCD 滤镜"

#: conf.d/11-lcdfilter-light.conf:8
msgid "Use lcdlight as default for LCD filter"
msgstr "将 lcdlight 设为默认 LCD 滤镜"

#: conf.d/20-unhint-small-vera.conf:8
msgid ""
"Disable hinting for Bitstream Vera fonts when the size is less than 8ppem"
msgstr "为大小小于 8ppem 的 Bitstream Vera 字体禁用微调"

#: conf.d/25-unhint-nonlatin.conf:8
msgid "Disable hinting for CJK fonts"
msgstr "为中日韩 (CJK) 字体禁用微调"

#: conf.d/30-metric-aliases.conf:8
msgid "Set substitutions for similar/metric-compatible families"
msgstr "为相似或规格兼容的字体家族设置替换"

#: conf.d/40-nonlatin.conf:8
msgid "Set substitutions for non-Latin fonts"
msgstr "为非拉丁语言字体设置替换"

#: conf.d/45-generic.conf:8
msgid "Set substitutions for emoji/math fonts"
msgstr "为绘文字 (Emoji) 或数学字体设置替换"

#: conf.d/45-latin.conf:8
msgid "Set substitutions for Latin fonts"
msgstr "为拉丁语言字体设置替换"

#: conf.d/49-sansserif.conf:8
msgid "Add sans-serif to the family when no generic name"
msgstr "在没有通用名称时添加 sans-serif 到字体家族"

#: conf.d/50-user.conf:8
msgid "Load per-user customization files"
msgstr "载入用户自定义文件"

#: conf.d/51-local.conf:8
msgid "Load local customization file"
msgstr "载入本地自定义文件"

#: conf.d/60-generic.conf:8
msgid "Set preferable fonts for emoji/math fonts"
msgstr "设置首选绘文字 (Emoji) 或数学字体"

#: conf.d/60-latin.conf:8
msgid "Set preferable fonts for Latin"
msgstr "设置首选拉丁语言字体"

#: conf.d/65-nonlatin.conf:8
msgid "Set preferable fonts for non-Latin"
msgstr "设置首选非拉丁语言字体"

#: conf.d/70-no-bitmaps.conf:8
msgid "Reject bitmap fonts"
msgstr "排除点阵字体"

#: conf.d/70-yes-bitmaps.conf:8
msgid "Accept bitmap fonts"
msgstr "接受点阵字体"
