<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<!-- /etc/fonts/local.conf file for local customizations -->
<fontconfig>
  <its:rules xmlns:its="http://www.w3.org/2005/11/its" version="1.0">
    <its:translateRule translate="no" selector="/fontconfig/*[not(self::description)]"/>
  </its:rules>

  <description>local customizations</description>
<!--
  Enable sub-pixel rendering
	<match target="font">
		<test qual="all" name="rgba">
			<const>unknown</const>
		</test>
		<edit name="rgba" mode="assign"><const>rgb</const></edit>
	</match>
-->
<!-- 
  Reject bitmap fonts
 -->

 <selectfont>
  <rejectfont>
   <pattern>
     <patelt name="scalable"><bool>false</bool></patelt>
   </pattern>
  </rejectfont>
 </selectfont>
 
<!--
  Accept bitmap fonts.  This overrides any rejectfont which matches 
  
 <selectfont>
  <acceptfont>
   <pattern>
     <patelt name="scalable"><bool>false</bool></patelt>
   </pattern>
  </acceptfont>
 </selectfont>
 -->
</fontconfig>
