<!doctype refentry PUBLIC "-//OASIS//DTD DocBook V4.1//EN" [

<!-- Process this file with docbook-to-man to generate an nroff manual
     page: `docbook-to-man manpage.sgml > manpage.1'.  You may view
     the manual page with: `docbook-to-man manpage.sgml | nroff -man |
     less'.  A typical entry in a Makefile or Makefile.am is:

manpage.1: manpage.sgml
        docbook-to-man $< > $@


        The docbook-to-man binary is found in the docbook-to-man package.
        Please remember that if you create the nroff version in one of the
        debian/rules file targets (such as build), you will need to include
        docbook-to-man in your Build-Depends control field.

  -->

  <!-- Fill in your name for FIRSTNAME and SURNAME. -->
  <!ENTITY dhfirstname "<firstname>Josselin</firstname>">
  <!ENTITY dhsurname   "<surname><PERSON><PERSON>tte</surname>">
  <!-- Please adjust the date whenever revising the manpage. -->
  <!ENTITY dhdate      "<date>Aug 13, 2008</date>">
  <!-- SECTION should be 1-8, maybe w/ subsection other parameters are
       allowed: see man(7), man(1). -->
  <!ENTITY dhsection   "<manvolnum>1</manvolnum>">
  <!ENTITY dhemail     "<email><EMAIL></email>">
  <!ENTITY dhusername  "Josselin Mouette">
  <!ENTITY dhucpackage "<refentrytitle>fc-cache</refentrytitle>">
  <!ENTITY dhpackage   "fc-cache">

  <!ENTITY debian      "<productname>Debian</productname>">
  <!ENTITY gnu         "<acronym>GNU</acronym>">
  <!ENTITY gpl         "&gnu; <acronym>GPL</acronym>">
]>

<refentry>
  <refentryinfo>
    <address>
      &dhemail;
    </address>
    <author>
      &dhfirstname;
      &dhsurname;
    </author>
    <copyright>
      <year>2003</year>
      <holder>&dhusername;</holder>
    </copyright>
    &dhdate;
  </refentryinfo>
  <refmeta>
    &dhucpackage;

    &dhsection;
  </refmeta>
  <refnamediv>
    <refname>&dhpackage;</refname>

    <refpurpose>build font information cache files</refpurpose>
  </refnamediv>
  <refsynopsisdiv>
    <cmdsynopsis>
      <command>&dhpackage;</command>

      <arg><option>-EfrsvVh</option></arg>
      <arg><option>--error-on-no-fonts</option></arg>
      <arg><option>--force</option></arg>
      <arg><option>--really-force</option></arg>
      <group>
        <arg><option>-y</option> <option><replaceable>dir</replaceable></option></arg>
        <arg><option>--sysroot</option> <option><replaceable>dir</replaceable></option></arg>
      </group>
      <arg><option>--system-only</option></arg>
      <arg><option>--verbose</option></arg>
      <arg><option>--version</option></arg>
      <arg><option>--help</option></arg>
      <arg rep="repeat"><option><replaceable>dir</replaceable></option></arg>

     </cmdsynopsis>
  </refsynopsisdiv>
  <refsect1>
    <title>DESCRIPTION</title>

      <para><command>&dhpackage;</command> scans the font directories on
        the system and builds font information cache files for
        applications using fontconfig for their font handling.</para>

      <para>If directory arguments are not given,
        <command>&dhpackage;</command> uses each directory in the
        current font configuration. Each directory is scanned for
        font files readable by FreeType.  A cache is created which
        contains properties of each font and the associated filename.
        This cache is used to speed up application startup when using
        the fontconfig library.</para>

      <para>Note that <command>&dhpackage;</command> must be executed
        once per architecture to generate font information customized
        for that architecture.</para>

  </refsect1>
  <refsect1>
    <title>OPTIONS</title>

    <para>This program follows the usual &gnu; command line syntax,
      with long options starting with two dashes (`-').  A summary of
      options is included below.</para>

    <variablelist>
      <varlistentry>
        <term><option>-E</option>
          <option>--error-on-no-fonts</option>
        </term>
        <listitem>
          <para>Raise an error if there are no fonts in
            <option><replaceable>dir</replaceable></option> or directories
            in the configuration if not given.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-f</option>
          <option>--force</option>
        </term>
        <listitem>
          <para>Force re-generation of apparently up-to-date cache files,
            overriding the timestamp checking.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-r</option>
          <option>--really-force</option>
        </term>
        <listitem>
          <para>Erase all existing cache files and rescan.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-s</option>
          <option>--system-only</option>
        </term>
        <listitem>
          <para>Only scan system-wide directories, omitting the places
            located in the user's home directory.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-v</option>
          <option>--verbose</option>
        </term>
        <listitem>
          <para>Display status information while busy.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-y</option>
          <option>-sysroot</option>
          <option><replaceable>dir</replaceable></option>
        </term>
        <listitem>
          <para>Prepend <option><replaceable>dir</replaceable></option> to all paths for scanning.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-h</option>
          <option>--help</option>
        </term>
        <listitem>
          <para>Show summary of options.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-V</option>
          <option>--version</option>
        </term>
        <listitem>
          <para>Show version of the program and exit.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option><replaceable>dir</replaceable></option>
        </term>
        <listitem>
          <para>Directory to scan for fonts.</para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  <refsect1>
    <title>RETURN CODES</title>
    <para><command>fc-cache</command> returns zero if the caches successfully generated. otherwise non-zero.</para>
  </refsect1>

  <refsect1>
    <title>FILES</title>
    <variablelist>
      <varlistentry>
        <term><filename><replaceable>%cachedir%</replaceable>/*-<replaceable>%arch%</replaceable>.cache-%version%</filename></term>
        <listitem>
          <para>These files are generated by <command>&dhpackage;</command>
            and contain maps from file names to font properties. They are
            read by the fontconfig library at application startup to locate
            appropriate fonts.</para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  <refsect1>
    <title>SEE ALSO</title>

    <para>
      <command>fc-cat</command>(1)
      <command>fc-list</command>(1)
      <command>fc-match</command>(1)
      <command>fc-pattern</command>(1)
      <command>fc-query</command>(1)
      <command>fc-scan</command>(1)
    </para>

    <para><ulink url="https://fontconfig.pages.freedesktop.org/fontconfig/fontconfig-user.html">The fontconfig user's guide</ulink></para>

 </refsect1>
  <refsect1>
    <title>AUTHOR</title>

    <para>This manual page was written by Keith Packard
      <email><EMAIL></email> and &dhusername; &dhemail;.</para>

  </refsect1>
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
