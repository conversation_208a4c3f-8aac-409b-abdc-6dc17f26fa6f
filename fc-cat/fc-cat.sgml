<!doctype refentry PUBLIC "-//OASIS//DTD DocBook V4.1//EN" [

<!--
Copyright © 2008 Patrick Lam

Permission to use, copy, modify, distribute, and sell this software and its
documentation for any purpose is hereby granted without fee, provided that
the above copyright notice appear in all copies and that both that
copyright notice and this permission notice appear in supporting
documentation, and that the name of the author(s) not be used in
advertising or publicity pertaining to distribution of the software without
specific, written prior permission.  The authors make no
representations about the suitability of this software for any purpose.  It
is provided "as is" without express or implied warranty.

THE AUTHOR(S) DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
-->

<!-- Process this file with docbook-to-man to generate an nroff manual
     page: `docbook-to-man manpage.sgml > manpage.1'.  You may view
     the manual page with: `docbook-to-man manpage.sgml | nroff -man |
     less'.  A typical entry in a Makefile or Makefile.am is:

manpage.1: manpage.sgml
        docbook-to-man $< > $@


        The docbook-to-man binary is found in the docbook-to-man package.
        Please remember that if you create the nroff version in one of the
        debian/rules file targets (such as build), you will need to include
        docbook-to-man in your Build-Depends control field.

  -->

  <!-- Fill in your name for FIRSTNAME and SURNAME. -->
  <!ENTITY dhfirstname "<firstname>Patrick</firstname>">
  <!ENTITY dhsurname   "<surname>Lam</surname>">
  <!-- Please adjust the date whenever revising the manpage. -->
  <!ENTITY dhdate      "<date>Aug 13, 2008</date>">
  <!-- SECTION should be 1-8, maybe w/ subsection other parameters are
       allowed: see man(7), man(1). -->
  <!ENTITY dhsection   "<manvolnum>1</manvolnum>">
  <!ENTITY dhemail     "<email><EMAIL></email>">
  <!ENTITY dhusername  "Patrick Lam">
  <!ENTITY dhucpackage "<refentrytitle>fc-cat</refentrytitle>">
  <!ENTITY dhpackage   "fc-cat">

  <!ENTITY debian      "<productname>Debian</productname>">
  <!ENTITY gnu         "<acronym>GNU</acronym>">
  <!ENTITY gpl         "&gnu; <acronym>GPL</acronym>">
]>

<refentry>
  <refentryinfo>
    <address>
      &dhemail;
    </address>
    <author>
      &dhfirstname;
      &dhsurname;
    </author>
    <copyright>
      <year>2005</year>
      <holder>&dhusername;</holder>
    </copyright>
    &dhdate;
  </refentryinfo>
  <refmeta>
    &dhucpackage;

    &dhsection;
  </refmeta>
  <refnamediv>
    <refname>&dhpackage;</refname>

    <refpurpose>read font information cache files</refpurpose>
  </refnamediv>
  <refsynopsisdiv>
    <cmdsynopsis>
      <command>&dhpackage;</command>

      <arg><option>-rvVh</option></arg>
      <arg><option>--recurse</option></arg>
      <arg><option>--verbose</option></arg>
      <arg><option>--version</option></arg>
      <arg><option>--help</option></arg>
      <sbr>
      <group rep="repeat">
          <arg><option><replaceable>fonts-cache-%version%-files</replaceable></option></arg>
          <arg><option><replaceable>dirs</replaceable></option></arg>
      </group>

     </cmdsynopsis>
  </refsynopsisdiv>
  <refsect1>
    <title>DESCRIPTION</title>

      <para><command>&dhpackage;</command> reads the font information from
        cache files or related to font directories
        and emits it in ASCII form.</para>

  </refsect1>
  <refsect1>
    <title>OPTIONS</title>

    <para>This program follows the usual &gnu; command line syntax,
      with long options starting with two dashes (`-').  A summary of
      options is included below.</para>

    <variablelist>
      <varlistentry>
        <term><option>-r</option>
          <option>--recurse</option>
        </term>
        <listitem>
          <para>Recurse into subdirectories.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-v</option>
          <option>--verbose</option>
        </term>
        <listitem>
          <para>Be verbose.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-h</option>
          <option>--help</option>
        </term>
        <listitem>
          <para>Show summary of options.</para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><option>-V</option>
          <option>--version</option>
        </term>
        <listitem>
          <para>Show version of the program and exit.</para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  <refsect1>
    <title>SEE ALSO</title>

    <para>
      <command>fc-cache</command>(1)
      <command>fc-list</command>(1)
      <command>fc-match</command>(1)
      <command>fc-pattern</command>(1)
      <command>fc-query</command>(1)
      <command>fc-scan</command>(1)
    </para>

    <para><ulink url="https://fontconfig.pages.freedesktop.org/fontconfig/fontconfig-user.html">The fontconfig user's guide</ulink></para>

 </refsect1>
  <refsect1>
    <title>AUTHOR</title>

    <para>This manual page was written by &dhusername; &dhemail;.</para>

  </refsect1>
</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
