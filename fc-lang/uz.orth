#
# fontconfig/fc-lang/uz.orth
#
# Copyright © 2009 Roozbeh Pournader
#
# Permission to use, copy, modify, distribute, and sell this software and its
# documentation for any purpose is hereby granted without fee, provided that
# the above copyright notice appear in all copies and that both that
# copyright notice and this permission notice appear in supporting
# documentation, and that the name of the author(s) not be used in
# advertising or publicity pertaining to distribution of the software without
# specific, written prior permission.  The author(s) make(s) no
# representations about the suitability of this software for any purpose.  It
# is provided "as is" without express or implied warranty.
#
# THE AUTHOR(S) DISCLAIM(S) ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
# INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
# EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
# CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
# DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
# TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.
#
# Uzbek (uz)
#
# Sources:
# * http://ru.wikipedia.org/wiki/Узбекская_письменность
# * http://unicode.org/cldr/data/common/main/uz_Latn.xml
# * http://www.oxuscom.com/New_Uzbek_Latin_Alphabet.pdf
#
0041-005A
0061-007A
# There are one to three modifier letters too, that are important for the
# orthography. But it's impossible to locate them in Unicode with the
# information available online. Possible candidates:
# U+02BB, U+02BC, U+02BF, U+02C8.
