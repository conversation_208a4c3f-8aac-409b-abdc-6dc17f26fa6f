#
# fontconfig/fc-lang/pa.orth
#
# Copyright © 2004 Red Hat, Inc.
# Copyright © 2009 Roozbeh Pournader
#
# Permission to use, copy, modify, distribute, and sell this software and its
# documentation for any purpose is hereby granted without fee, provided that
# the above copyright notice appear in all copies and that both that
# copyright notice and this permission notice appear in supporting
# documentation, and that the name of the author(s) not be used in
# advertising or publicity pertaining to distribution of the software without
# specific, written prior permission.  The author(s) make(s) no
# representations about the suitability of this software for any purpose.  It
# is provided "as is" without express or implied warranty.
#
# THE AUTHOR(S) DISCLAIM(S) ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
# INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
# EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
# CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
# DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
# TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.
#
# Panjabi/Punjabi (pa)
#
# According to ISO 639-3, 'pa/pan' only applies to Panjabi as used in India:
#   http://www.sil.org/iso639-3/documentation.asp?id=pan
#
# For Panjabi as used in Pakistan, use 'lah' or 'pa-PK':
#   http://www.sil.org/iso639-3/documentation.asp?id=lah
#
# From Unicode coverage for Gurmukhi, with modifications based on
# the 'Lohit Punjabi' font
#
# 0A01-0A03	# Various signs
0A05-0A0A	# Independent vowels
0A0F-0A10
0A13-0A14
0A15-0A28	# Consonants
0A2A-0A30
0A32-0A33
0A35-0A36
0A38-0A39
0A3C		# Nukta
0A3E-0A42	# Dependent vowel signs
0A47-0A48
0A4B-0A4C
0A4D		# Virama
0A59-0A5C	# Additional consonants
# 0A5E		# GURMUKHI LETTER FA
# 0A66-0A6F	# Digits
0A70-0A74	# Gurmukhi-specific additions
