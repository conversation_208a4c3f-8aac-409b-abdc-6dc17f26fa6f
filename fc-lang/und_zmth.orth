#
# fontconfig/fc-lang/emoji.orth
#
# Copyright © 2002 <PERSON>
# Copyright © 2017 Red Hat, Inc.
#
# Permission to use, copy, modify, distribute, and sell this software and its
# documentation for any purpose is hereby granted without fee, provided that
# the above copyright notice appear in all copies and that both that
# copyright notice and this permission notice appear in supporting
# documentation, and that the name of the author(s) not be used in
# advertising or publicity pertaining to distribution of the software without
# specific, written prior permission.  The authors make no
# representations about the suitability of this software for any purpose.  It
# is provided "as is" without express or implied warranty.
#
# THE AUTHOR(S) DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
# INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
# EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
# CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
# DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
# TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.
#
# Math
#
# based on
# http://www.unicode.org/Public/math/revision-9/MathClass-9.txt
#
0020	#	S 	#  	SPACE
0021	#	P 	# !	EXCLAMATION MARK
0021	#	N 	# !	EXCLAMATION MARK
0023..0026	#	N 	# #..&	NUMBER SIGN..AMPERSAND
0028	#	O 	# (	LEFT PARENTHESIS
0029	#	C 	# )	RIGHT PARENTHESIS
002A	#	N 	# *	ASTERISK
002B	#	V 	# +	PLUS SIGN
002C	#	P 	# ,	COMMA
002D	#	R 	# -	HYPHEN-MINUS
002E	#	P 	# .	FULL STOP
002F	#	R 	# /	SOLIDUS
0030..0039	#	N 	# 0..9	DIGIT ZERO..NINE
003A..003B	#	P 	# :..	#	COLON..SEMICOLON
003C..003E	#	R 	# <..>	LESS-THAN SIGN..GREATER-THAN SIGN
003F	#	P 	# ?	QUESTION MARK
0040	#	N 	# @	COMMERCIAL AT
0041..005A	#	A 	# A..Z	LATIN CAPITAL LETTER A..Z
005B	#	O 	# [	LEFT SQUARE BRACKET
005C	#	N 	# \	REVERSE SOLIDUS
005D	#	C 	# ]	RIGHT SQUARE BRACKET
005E	#	X 	# ^	CIRCUMFLEX ACCENT
0061..007A	#	A 	# a..z	LATIN SMALL LETTER A..Z
007B	#	O 	# {	LEFT CURLY BRACKET
007C	#	F 	# |	VERTICAL LINE
007D	#	C 	# }	RIGHT CURLY BRACKET
007E	#	X 	# ~	TILDE
00A1	#	P 	# ¡	INVERTED EXCLAMATION MARK
00A2..00A7	#	N 	# ¢..§	CENT SIGN..SECTION SIGN
00AC	#	N 	# ¬	NOT SIGN
00B0	#	N 	# °	DEGREE SIGN
00B1	#	V 	# ±	PLUS-MINUS SIGN
00B5..00B6	#	N 	# µ..¶	MICRO SIGN..PILCROW SIGN
00B7	#	B 	# ·	MIDDLE DOT
00BF	#	P 	# ¿	INVERTED QUESTION MARK
00D7	#	B 	# ×	MULTIPLICATION SIGN
00F7	#	B 	# ÷	DIVISION SIGN
0131	#	A 	# ı	LATIN SMALL LETTER DOTLESS I
0308	#	D 	# ̈	COMBINING DIAERESIS
030A	#	D 	# ̊	COMBINING RING ABOVE
030C	#	D 	# ̌	COMBINING CARON
0338	#	D 	# ̸	COMBINING LONG SOLIDUS OVERLAY
0391..03A1	#	A 	# Α..Ρ	GREEK CAPITAL LETTER ALPHA..RHO
03A3..03A4	#	A 	# Σ..Τ	GREEK CAPITAL LETTER SIGMA..TAU
03A6..03A9	#	A 	# Φ..Ω	GREEK CAPITAL LETTER PHI..OMEGA
03B1..03C1	#	A 	# α..ρ	GREEK SMALL LETTER ALPHA..RHO
03C3..03C9	#	A 	# σ..ω	GREEK SMALL LETTER SIGMA..OMEGA
03D5..03D6	#	A 	# ϕ..ϖ	GREEK PHI SYMBOL..PI SYMBOL
03F0..03F1	#	A 	# ϰ..ϱ	GREEK KAPPA SYMBOL..RHO SYMBOL
2016	#	F 	# ‖	DOUBLE VERTICAL LINE
2020	#	R 	# †	DAGGER
2020	#	N 	# †	DAGGER
2021	#	R 	# ‡	DOUBLE DAGGER
2021	#	N 	# ‡	DOUBLE DAGGER
2022	#	B 	# •	BULLET
2026	#	N 	# …	HORIZONTAL ELLIPSIS
2044	#	X 	# ⁄	FRACTION SLASH
2057	#	N 	# ⁗	QUADRUPLE PRIME
20E1	#	D 	# ⃡	COMBINING LEFT RIGHT ARROW ABOVE
2102	#	A 	# ℂ	DOUBLE-STRUCK CAPITAL C
210E..210F	#	N 	# ℎ..ℏ	PLANCK CONSTANT.. OVER TWO PI
2110..2113	#	A 	# ℐ..ℓ	SCRIPT CAPITAL I..SMALL L
2115	#	A 	# ℕ	DOUBLE-STRUCK CAPITAL N
2118..211D	#	A 	# ℘..ℝ	SCRIPT CAPITAL P..DOUBLE-STRUCK CAPITAL R
2124	#	A 	# ℤ	DOUBLE-STRUCK CAPITAL Z
2200..2201	#	U 	# ∀..∁	FOR ALL..COMPLEMENT
2202	#	N 	# ∂	PARTIAL DIFFERENTIAL
2203..2204	#	U 	# ∃..∄	THERE EXISTS..DOES NOT EXIST
2205	#	N 	# ∅	EMPTY SET
2206..2207	#	U 	# ∆..∇	INCREMENT..NABLA
2208..220D	#	R 	# ∈..∍	ELEMENT OF..SMALL CONTAINS AS MEMBER
220F..2211	#	L 	# ∏..∑	N-ARY PRODUCT..SUMMATION
2212..2213	#	V 	# −..∓	MINUS SIGN..MINUS-OR-PLUS SIGN
2214..2219	#	B 	# ∔..∙	DOT PLUS..BULLET OPERATOR
221D	#	R 	# ∝	PROPORTIONAL TO
221E..2222	#	N 	# ∞..∢	INFINITY..SPHERICAL ANGLE
2223..2226	#	R 	# ∣..∦	DIVIDES..NOT PARALLEL TO
2227..222A	#	B 	# ∧..∪	LOGICAL AND..UNION
2234..2235	#	N 	# ∴..∵	THEREFORE..BECAUSE
2236..2237	#	R 	# ∶..∷	RATIO..PROPORTION
2238	#	B 	# ∸	DOT MINUS
2239..223D	#	R 	# ∹..∽	EXCESS..REVERSED TILDE
223E	#	B 	# ∾	INVERTED LAZY S
223F	#	N 	# ∿	SINE WAVE
2240	#	B 	# ≀	WREATH PRODUCT
228C..228E	#	B 	# ⊌..⊎	MULTISET.. UNION
228F..2292	#	R 	# ⊏..⊒	SQUARE IMAGE OF..ORIGINAL OF OR EQUAL TO
2293..22A1	#	B 	# ⊓..⊡	SQUARE CAP..SQUARED DOT OPERATOR
22A2..22A3	#	R 	# ⊢..⊣	RIGHT TACK..LEFT TACK
22A4..22A5	#	N 	# ⊤..⊥	DOWN TACK..UP TACK
22C0..22C3	#	L 	# ⋀..⋃	N-ARY LOGICAL AND..UNION
22C8	#	R 	# ⋈	BOWTIE
22CD	#	R 	# ⋍	REVERSED TILDE EQUALS
22CE..22CF	#	B 	# ⋎..⋏	CURLY LOGICAL OR..AND
2308	#	O 	# ⌈	LEFT CEILING
2309	#	C 	# ⌉	RIGHT CEILING
230A	#	O 	# ⌊	LEFT FLOOR
230B	#	C 	# ⌋	RIGHT FLOOR
2322..2323	#	R 	# ⌢..⌣	FROWN..SMILE
25A0..25A1	#	N 	# ■..□	BLACK SQUARE..WHITE SQUARE
27E6	#	O 	# ⟦	MATHEMATICAL LEFT WHITE SQUARE BRACKET
27E7	#	C 	# ⟧	MATHEMATICAL RIGHT WHITE SQUARE BRACKET
27E8	#	O 	# ⟨	MATHEMATICAL LEFT ANGLE BRACKET
27E9	#	C 	# ⟩	MATHEMATICAL RIGHT ANGLE BRACKET
1D400..1D454	#	A 	# 𝐀..𝑔	MATHEMATICAL BOLD CAPITAL A..ITALIC SMALL G
1D456..1D49C	#	A 	# 𝑖..𝒜	MATHEMATICAL ITALIC SMALL I..SCRIPT CAPITAL A
1D49E..1D49F	#	A 	# 𝒞..𝒟	MATHEMATICAL SCRIPT CAPITAL C..D
1D4A2	#	A 	# 𝒢	MATHEMATICAL SCRIPT CAPITAL G
1D4A5..1D4A6	#	A 	# 𝒥..𝒦	MATHEMATICAL SCRIPT CAPITAL J..K
1D4A9..1D4AC	#	A 	# 𝒩..𝒬	MATHEMATICAL SCRIPT CAPITAL N..Q
1D53B..1D53E	#	A 	# 𝔻..𝔾	MATHEMATICAL DOUBLE-STRUCK CAPITAL D..G
1D540..1D544	#	A 	# 𝕀..𝕄	MATHEMATICAL DOUBLE-STRUCK CAPITAL I..M
1D546	#	A 	# 𝕆	MATHEMATICAL DOUBLE-STRUCK CAPITAL O
1D54A..1D550	#	A 	# 𝕊..𝕐	MATHEMATICAL DOUBLE-STRUCK CAPITAL S..Y
1D6A4..1D6A5	#	A 	# 𝚤..𝚥	MATHEMATICAL ITALIC SMALL DOTLESS I..J
