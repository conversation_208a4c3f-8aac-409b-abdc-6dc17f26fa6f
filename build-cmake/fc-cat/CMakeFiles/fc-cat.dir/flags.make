# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile C with /usr/bin/cc
C_DEFINES = -DHAVE_CONFIG_H

C_INCLUDES = -I/Users/<USER>/work/fontconfig/build-cmake -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/src -I/Users/<USER>/work/fontconfig/build-cmake/fc-cat/../src -I/Users/<USER>/work/fontconfig/build-cmake/fc-cat/.. -I/Users/<USER>/work/fontconfig/build-cmake/src

C_FLAGS = -std=gnu11

