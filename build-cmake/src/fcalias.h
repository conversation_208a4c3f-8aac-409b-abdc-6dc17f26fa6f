extern __typeof (FcBlanksCreate) IA__FcBlanksCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcBlanksCreate IA__FcBlanksCreate
extern __typeof (FcBlanksDestroy) IA__FcBlanksDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcBlanksDestroy IA__FcBlanksDestroy
extern __typeof (FcBlanksAdd) IA__FcBlanksAdd FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcBlanksAdd IA__FcBlanksAdd
extern __typeof (FcBlanksIsMember) IA__FcBlanksIsMember FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcBlanksIsMember IA__FcBlanksIsMember
extern __typeof (FcCacheCopySet) IA__FcCacheCopySet FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCacheCopySet IA__FcCacheCopySet
extern __typeof (FcCacheNumSubdir) IA__FcCacheNumSubdir FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCacheNumSubdir IA__FcCacheNumSubdir
extern __typeof (FcCacheNumFont) IA__FcCacheNumFont FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCacheNumFont IA__FcCacheNumFont
extern __typeof (FcDirCacheUnlink) IA__FcDirCacheUnlink FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheUnlink IA__FcDirCacheUnlink
extern __typeof (FcDirCacheValid) IA__FcDirCacheValid FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheValid IA__FcDirCacheValid
extern __typeof (FcDirCacheClean) IA__FcDirCacheClean FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheClean IA__FcDirCacheClean
extern __typeof (FcCacheCreateTagFile) IA__FcCacheCreateTagFile FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCacheCreateTagFile IA__FcCacheCreateTagFile
extern __typeof (FcDirCacheCreateUUID) IA__FcDirCacheCreateUUID FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheCreateUUID IA__FcDirCacheCreateUUID
extern __typeof (FcDirCacheDeleteUUID) IA__FcDirCacheDeleteUUID FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheDeleteUUID IA__FcDirCacheDeleteUUID
extern __typeof (FcConfigHome) IA__FcConfigHome FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigHome IA__FcConfigHome
extern __typeof (FcConfigEnableHome) IA__FcConfigEnableHome FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigEnableHome IA__FcConfigEnableHome
extern __typeof (FcConfigGetFilename) IA__FcConfigGetFilename FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetFilename IA__FcConfigGetFilename
extern __typeof (FcConfigFilename) IA__FcConfigFilename FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigFilename IA__FcConfigFilename
extern __typeof (FcConfigCreate) IA__FcConfigCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigCreate IA__FcConfigCreate
extern __typeof (FcConfigReference) IA__FcConfigReference FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigReference IA__FcConfigReference
extern __typeof (FcConfigDestroy) IA__FcConfigDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigDestroy IA__FcConfigDestroy
extern __typeof (FcConfigSetCurrent) IA__FcConfigSetCurrent FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigSetCurrent IA__FcConfigSetCurrent
extern __typeof (FcConfigGetCurrent) IA__FcConfigGetCurrent FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetCurrent IA__FcConfigGetCurrent
extern __typeof (FcConfigUptoDate) IA__FcConfigUptoDate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigUptoDate IA__FcConfigUptoDate
extern __typeof (FcConfigBuildFonts) IA__FcConfigBuildFonts FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigBuildFonts IA__FcConfigBuildFonts
extern __typeof (FcConfigGetFontDirs) IA__FcConfigGetFontDirs FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetFontDirs IA__FcConfigGetFontDirs
extern __typeof (FcConfigGetConfigDirs) IA__FcConfigGetConfigDirs FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetConfigDirs IA__FcConfigGetConfigDirs
extern __typeof (FcConfigGetConfigFiles) IA__FcConfigGetConfigFiles FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetConfigFiles IA__FcConfigGetConfigFiles
extern __typeof (FcConfigGetCache) IA__FcConfigGetCache FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetCache IA__FcConfigGetCache
extern __typeof (FcConfigGetBlanks) IA__FcConfigGetBlanks FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetBlanks IA__FcConfigGetBlanks
extern __typeof (FcConfigGetCacheDirs) IA__FcConfigGetCacheDirs FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetCacheDirs IA__FcConfigGetCacheDirs
extern __typeof (FcConfigGetRescanInterval) IA__FcConfigGetRescanInterval FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetRescanInterval IA__FcConfigGetRescanInterval
extern __typeof (FcConfigSetRescanInterval) IA__FcConfigSetRescanInterval FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigSetRescanInterval IA__FcConfigSetRescanInterval
extern __typeof (FcConfigGetFonts) IA__FcConfigGetFonts FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetFonts IA__FcConfigGetFonts
extern __typeof (FcConfigAcceptFont) IA__FcConfigAcceptFont FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigAcceptFont IA__FcConfigAcceptFont
extern __typeof (FcConfigAcceptFilter) IA__FcConfigAcceptFilter FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigAcceptFilter IA__FcConfigAcceptFilter
extern __typeof (FcConfigAppFontAddFile) IA__FcConfigAppFontAddFile FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigAppFontAddFile IA__FcConfigAppFontAddFile
extern __typeof (FcConfigAppFontAddDir) IA__FcConfigAppFontAddDir FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigAppFontAddDir IA__FcConfigAppFontAddDir
extern __typeof (FcConfigAppFontClear) IA__FcConfigAppFontClear FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigAppFontClear IA__FcConfigAppFontClear
extern __typeof (FcConfigPreferAppFont) IA__FcConfigPreferAppFont FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigPreferAppFont IA__FcConfigPreferAppFont
extern __typeof (FcConfigSubstituteWithPat) IA__FcConfigSubstituteWithPat FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigSubstituteWithPat IA__FcConfigSubstituteWithPat
extern __typeof (FcConfigSubstitute) IA__FcConfigSubstitute FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigSubstitute IA__FcConfigSubstitute
extern __typeof (FcConfigGetSysRoot) IA__FcConfigGetSysRoot FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetSysRoot IA__FcConfigGetSysRoot
extern __typeof (FcConfigSetSysRoot) IA__FcConfigSetSysRoot FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigSetSysRoot IA__FcConfigSetSysRoot
extern __typeof (FcConfigSetFontSetFilter) IA__FcConfigSetFontSetFilter FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigSetFontSetFilter IA__FcConfigSetFontSetFilter
extern __typeof (FcConfigFileInfoIterInit) IA__FcConfigFileInfoIterInit FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigFileInfoIterInit IA__FcConfigFileInfoIterInit
extern __typeof (FcConfigFileInfoIterNext) IA__FcConfigFileInfoIterNext FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigFileInfoIterNext IA__FcConfigFileInfoIterNext
extern __typeof (FcConfigFileInfoIterGet) IA__FcConfigFileInfoIterGet FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigFileInfoIterGet IA__FcConfigFileInfoIterGet
extern __typeof (FcCharSetCreate) IA__FcCharSetCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetCreate IA__FcCharSetCreate
extern __typeof (FcCharSetNew) IA__FcCharSetNew FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetNew IA__FcCharSetNew
extern __typeof (FcCharSetDestroy) IA__FcCharSetDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetDestroy IA__FcCharSetDestroy
extern __typeof (FcCharSetAddChar) IA__FcCharSetAddChar FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetAddChar IA__FcCharSetAddChar
extern __typeof (FcCharSetDelChar) IA__FcCharSetDelChar FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetDelChar IA__FcCharSetDelChar
extern __typeof (FcCharSetCopy) IA__FcCharSetCopy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetCopy IA__FcCharSetCopy
extern __typeof (FcCharSetEqual) IA__FcCharSetEqual FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetEqual IA__FcCharSetEqual
extern __typeof (FcCharSetIntersect) IA__FcCharSetIntersect FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetIntersect IA__FcCharSetIntersect
extern __typeof (FcCharSetUnion) IA__FcCharSetUnion FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetUnion IA__FcCharSetUnion
extern __typeof (FcCharSetSubtract) IA__FcCharSetSubtract FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetSubtract IA__FcCharSetSubtract
extern __typeof (FcCharSetMerge) IA__FcCharSetMerge FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetMerge IA__FcCharSetMerge
extern __typeof (FcCharSetHasChar) IA__FcCharSetHasChar FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetHasChar IA__FcCharSetHasChar
extern __typeof (FcCharSetCount) IA__FcCharSetCount FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetCount IA__FcCharSetCount
extern __typeof (FcCharSetIntersectCount) IA__FcCharSetIntersectCount FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetIntersectCount IA__FcCharSetIntersectCount
extern __typeof (FcCharSetSubtractCount) IA__FcCharSetSubtractCount FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetSubtractCount IA__FcCharSetSubtractCount
extern __typeof (FcCharSetIsSubset) IA__FcCharSetIsSubset FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetIsSubset IA__FcCharSetIsSubset
extern __typeof (FcCharSetFirstPage) IA__FcCharSetFirstPage FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetFirstPage IA__FcCharSetFirstPage
extern __typeof (FcCharSetNextPage) IA__FcCharSetNextPage FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetNextPage IA__FcCharSetNextPage
extern __typeof (FcCharSetCoverage) IA__FcCharSetCoverage FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcCharSetCoverage IA__FcCharSetCoverage
extern __typeof (FcValuePrint) IA__FcValuePrint FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcValuePrint IA__FcValuePrint
extern __typeof (FcPatternPrint) IA__FcPatternPrint FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternPrint IA__FcPatternPrint
extern __typeof (FcFontSetPrint) IA__FcFontSetPrint FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontSetPrint IA__FcFontSetPrint
extern __typeof (FcConfigGetDefaultLangs) IA__FcConfigGetDefaultLangs FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetDefaultLangs IA__FcConfigGetDefaultLangs
extern __typeof (FcGetDefaultLangs) IA__FcGetDefaultLangs FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcGetDefaultLangs IA__FcGetDefaultLangs
extern __typeof (FcConfigSetDefaultSubstitute) IA__FcConfigSetDefaultSubstitute FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigSetDefaultSubstitute IA__FcConfigSetDefaultSubstitute
extern __typeof (FcDefaultSubstitute) IA__FcDefaultSubstitute FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDefaultSubstitute IA__FcDefaultSubstitute
extern __typeof (FcFileIsDir) IA__FcFileIsDir FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFileIsDir IA__FcFileIsDir
extern __typeof (FcFileScan) IA__FcFileScan FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFileScan IA__FcFileScan
extern __typeof (FcDirScan) IA__FcDirScan FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirScan IA__FcDirScan
extern __typeof (FcDirSave) IA__FcDirSave FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirSave IA__FcDirSave
extern __typeof (FcDirCacheLoad) IA__FcDirCacheLoad FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheLoad IA__FcDirCacheLoad
extern __typeof (FcDirCacheRescan) IA__FcDirCacheRescan FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheRescan IA__FcDirCacheRescan
extern __typeof (FcDirCacheRead) IA__FcDirCacheRead FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheRead IA__FcDirCacheRead
extern __typeof (FcDirCacheLoadFile) IA__FcDirCacheLoadFile FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheLoadFile IA__FcDirCacheLoadFile
extern __typeof (FcDirCacheUnload) IA__FcDirCacheUnload FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcDirCacheUnload IA__FcDirCacheUnload
extern __typeof (FcFreeTypeQuery) IA__FcFreeTypeQuery FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFreeTypeQuery IA__FcFreeTypeQuery
extern __typeof (FcFreeTypeQueryAll) IA__FcFreeTypeQueryAll FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFreeTypeQueryAll IA__FcFreeTypeQueryAll
extern __typeof (FcFontSetCreate) IA__FcFontSetCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontSetCreate IA__FcFontSetCreate
extern __typeof (FcFontSetDestroy) IA__FcFontSetDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontSetDestroy IA__FcFontSetDestroy
extern __typeof (FcFontSetAdd) IA__FcFontSetAdd FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontSetAdd IA__FcFontSetAdd
extern __typeof (FcInitLoadConfig) IA__FcInitLoadConfig FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcInitLoadConfig IA__FcInitLoadConfig
extern __typeof (FcInitLoadConfigAndFonts) IA__FcInitLoadConfigAndFonts FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcInitLoadConfigAndFonts IA__FcInitLoadConfigAndFonts
extern __typeof (FcInit) IA__FcInit FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcInit IA__FcInit
extern __typeof (FcFini) IA__FcFini FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFini IA__FcFini
extern __typeof (FcGetVersion) IA__FcGetVersion FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcGetVersion IA__FcGetVersion
extern __typeof (FcInitReinitialize) IA__FcInitReinitialize FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcInitReinitialize IA__FcInitReinitialize
extern __typeof (FcInitBringUptoDate) IA__FcInitBringUptoDate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcInitBringUptoDate IA__FcInitBringUptoDate
extern __typeof (FcGetLangs) IA__FcGetLangs FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcGetLangs IA__FcGetLangs
extern __typeof (FcLangNormalize) IA__FcLangNormalize FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangNormalize IA__FcLangNormalize
extern __typeof (FcLangGetCharSet) IA__FcLangGetCharSet FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangGetCharSet IA__FcLangGetCharSet
extern __typeof (FcLangSetCreate) IA__FcLangSetCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetCreate IA__FcLangSetCreate
extern __typeof (FcLangSetDestroy) IA__FcLangSetDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetDestroy IA__FcLangSetDestroy
extern __typeof (FcLangSetCopy) IA__FcLangSetCopy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetCopy IA__FcLangSetCopy
extern __typeof (FcLangSetAdd) IA__FcLangSetAdd FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetAdd IA__FcLangSetAdd
extern __typeof (FcLangSetDel) IA__FcLangSetDel FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetDel IA__FcLangSetDel
extern __typeof (FcLangSetHasLang) IA__FcLangSetHasLang FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetHasLang IA__FcLangSetHasLang
extern __typeof (FcLangSetCompare) IA__FcLangSetCompare FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetCompare IA__FcLangSetCompare
extern __typeof (FcLangSetContains) IA__FcLangSetContains FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetContains IA__FcLangSetContains
extern __typeof (FcLangSetEqual) IA__FcLangSetEqual FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetEqual IA__FcLangSetEqual
extern __typeof (FcLangSetHash) IA__FcLangSetHash FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetHash IA__FcLangSetHash
extern __typeof (FcLangSetGetLangs) IA__FcLangSetGetLangs FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetGetLangs IA__FcLangSetGetLangs
extern __typeof (FcLangSetUnion) IA__FcLangSetUnion FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetUnion IA__FcLangSetUnion
extern __typeof (FcLangSetSubtract) IA__FcLangSetSubtract FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcLangSetSubtract IA__FcLangSetSubtract
extern __typeof (FcObjectSetCreate) IA__FcObjectSetCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcObjectSetCreate IA__FcObjectSetCreate
extern __typeof (FcObjectSetAdd) IA__FcObjectSetAdd FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcObjectSetAdd IA__FcObjectSetAdd
extern __typeof (FcObjectSetDestroy) IA__FcObjectSetDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcObjectSetDestroy IA__FcObjectSetDestroy
extern __typeof (FcObjectSetVaBuild) IA__FcObjectSetVaBuild FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcObjectSetVaBuild IA__FcObjectSetVaBuild
extern __typeof (FcObjectSetBuild) IA__FcObjectSetBuild FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcObjectSetBuild IA__FcObjectSetBuild
extern __typeof (FcFontSetList) IA__FcFontSetList FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontSetList IA__FcFontSetList
extern __typeof (FcFontList) IA__FcFontList FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontList IA__FcFontList
extern __typeof (FcAtomicCreate) IA__FcAtomicCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcAtomicCreate IA__FcAtomicCreate
extern __typeof (FcAtomicLock) IA__FcAtomicLock FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcAtomicLock IA__FcAtomicLock
extern __typeof (FcAtomicNewFile) IA__FcAtomicNewFile FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcAtomicNewFile IA__FcAtomicNewFile
extern __typeof (FcAtomicOrigFile) IA__FcAtomicOrigFile FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcAtomicOrigFile IA__FcAtomicOrigFile
extern __typeof (FcAtomicReplaceOrig) IA__FcAtomicReplaceOrig FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcAtomicReplaceOrig IA__FcAtomicReplaceOrig
extern __typeof (FcAtomicDeleteNew) IA__FcAtomicDeleteNew FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcAtomicDeleteNew IA__FcAtomicDeleteNew
extern __typeof (FcAtomicUnlock) IA__FcAtomicUnlock FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcAtomicUnlock IA__FcAtomicUnlock
extern __typeof (FcAtomicDestroy) IA__FcAtomicDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcAtomicDestroy IA__FcAtomicDestroy
extern __typeof (FcFontSetMatch) IA__FcFontSetMatch FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontSetMatch IA__FcFontSetMatch
extern __typeof (FcFontMatch) IA__FcFontMatch FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontMatch IA__FcFontMatch
extern __typeof (FcFontRenderPrepare) IA__FcFontRenderPrepare FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontRenderPrepare IA__FcFontRenderPrepare
extern __typeof (FcFontSetSort) IA__FcFontSetSort FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontSetSort IA__FcFontSetSort
extern __typeof (FcFontSort) IA__FcFontSort FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontSort IA__FcFontSort
extern __typeof (FcFontSetSortDestroy) IA__FcFontSetSortDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcFontSetSortDestroy IA__FcFontSetSortDestroy
extern __typeof (FcMatrixCopy) IA__FcMatrixCopy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcMatrixCopy IA__FcMatrixCopy
extern __typeof (FcMatrixEqual) IA__FcMatrixEqual FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcMatrixEqual IA__FcMatrixEqual
extern __typeof (FcMatrixMultiply) IA__FcMatrixMultiply FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcMatrixMultiply IA__FcMatrixMultiply
extern __typeof (FcMatrixRotate) IA__FcMatrixRotate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcMatrixRotate IA__FcMatrixRotate
extern __typeof (FcMatrixScale) IA__FcMatrixScale FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcMatrixScale IA__FcMatrixScale
extern __typeof (FcMatrixShear) IA__FcMatrixShear FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcMatrixShear IA__FcMatrixShear
extern __typeof (FcNameRegisterObjectTypes) IA__FcNameRegisterObjectTypes FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameRegisterObjectTypes IA__FcNameRegisterObjectTypes
extern __typeof (FcNameUnregisterObjectTypes) IA__FcNameUnregisterObjectTypes FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameUnregisterObjectTypes IA__FcNameUnregisterObjectTypes
extern __typeof (FcNameGetObjectType) IA__FcNameGetObjectType FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameGetObjectType IA__FcNameGetObjectType
extern __typeof (FcNameRegisterConstants) IA__FcNameRegisterConstants FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameRegisterConstants IA__FcNameRegisterConstants
extern __typeof (FcNameUnregisterConstants) IA__FcNameUnregisterConstants FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameUnregisterConstants IA__FcNameUnregisterConstants
extern __typeof (FcNameGetConstant) IA__FcNameGetConstant FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameGetConstant IA__FcNameGetConstant
extern __typeof (FcNameGetConstantFor) IA__FcNameGetConstantFor FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameGetConstantFor IA__FcNameGetConstantFor
extern __typeof (FcNameConstant) IA__FcNameConstant FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameConstant IA__FcNameConstant
extern __typeof (FcNameParse) IA__FcNameParse FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameParse IA__FcNameParse
extern __typeof (FcNameUnparse) IA__FcNameUnparse FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcNameUnparse IA__FcNameUnparse
extern __typeof (FcPatternCreate) IA__FcPatternCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternCreate IA__FcPatternCreate
extern __typeof (FcPatternDuplicate) IA__FcPatternDuplicate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternDuplicate IA__FcPatternDuplicate
extern __typeof (FcPatternReference) IA__FcPatternReference FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternReference IA__FcPatternReference
extern __typeof (FcPatternFilter) IA__FcPatternFilter FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternFilter IA__FcPatternFilter
extern __typeof (FcValueDestroy) IA__FcValueDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcValueDestroy IA__FcValueDestroy
extern __typeof (FcValueEqual) IA__FcValueEqual FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcValueEqual IA__FcValueEqual
extern __typeof (FcValueSave) IA__FcValueSave FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcValueSave IA__FcValueSave
extern __typeof (FcPatternDestroy) IA__FcPatternDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternDestroy IA__FcPatternDestroy
extern __typeof (FcPatternObjectCount) IA__FcPatternObjectCount FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternObjectCount IA__FcPatternObjectCount
extern __typeof (FcPatternEqual) IA__FcPatternEqual FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternEqual IA__FcPatternEqual
extern __typeof (FcPatternEqualSubset) IA__FcPatternEqualSubset FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternEqualSubset IA__FcPatternEqualSubset
extern __typeof (FcPatternHash) IA__FcPatternHash FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternHash IA__FcPatternHash
extern __typeof (FcPatternAdd) IA__FcPatternAdd FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAdd IA__FcPatternAdd
extern __typeof (FcPatternAddWeak) IA__FcPatternAddWeak FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAddWeak IA__FcPatternAddWeak
extern __typeof (FcPatternGet) IA__FcPatternGet FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGet IA__FcPatternGet
extern __typeof (FcPatternGetWithBinding) IA__FcPatternGetWithBinding FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGetWithBinding IA__FcPatternGetWithBinding
extern __typeof (FcPatternDel) IA__FcPatternDel FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternDel IA__FcPatternDel
extern __typeof (FcPatternRemove) IA__FcPatternRemove FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternRemove IA__FcPatternRemove
extern __typeof (FcPatternAddInteger) IA__FcPatternAddInteger FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAddInteger IA__FcPatternAddInteger
extern __typeof (FcPatternAddDouble) IA__FcPatternAddDouble FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAddDouble IA__FcPatternAddDouble
extern __typeof (FcPatternAddString) IA__FcPatternAddString FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAddString IA__FcPatternAddString
extern __typeof (FcPatternAddMatrix) IA__FcPatternAddMatrix FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAddMatrix IA__FcPatternAddMatrix
extern __typeof (FcPatternAddCharSet) IA__FcPatternAddCharSet FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAddCharSet IA__FcPatternAddCharSet
extern __typeof (FcPatternAddBool) IA__FcPatternAddBool FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAddBool IA__FcPatternAddBool
extern __typeof (FcPatternAddLangSet) IA__FcPatternAddLangSet FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAddLangSet IA__FcPatternAddLangSet
extern __typeof (FcPatternAddRange) IA__FcPatternAddRange FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternAddRange IA__FcPatternAddRange
extern __typeof (FcPatternGetInteger) IA__FcPatternGetInteger FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGetInteger IA__FcPatternGetInteger
extern __typeof (FcPatternGetDouble) IA__FcPatternGetDouble FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGetDouble IA__FcPatternGetDouble
extern __typeof (FcPatternGetString) IA__FcPatternGetString FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGetString IA__FcPatternGetString
extern __typeof (FcPatternGetMatrix) IA__FcPatternGetMatrix FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGetMatrix IA__FcPatternGetMatrix
extern __typeof (FcPatternGetCharSet) IA__FcPatternGetCharSet FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGetCharSet IA__FcPatternGetCharSet
extern __typeof (FcPatternGetBool) IA__FcPatternGetBool FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGetBool IA__FcPatternGetBool
extern __typeof (FcPatternGetLangSet) IA__FcPatternGetLangSet FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGetLangSet IA__FcPatternGetLangSet
extern __typeof (FcPatternGetRange) IA__FcPatternGetRange FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternGetRange IA__FcPatternGetRange
extern __typeof (FcPatternVaBuild) IA__FcPatternVaBuild FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternVaBuild IA__FcPatternVaBuild
extern __typeof (FcPatternBuild) IA__FcPatternBuild FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternBuild IA__FcPatternBuild
extern __typeof (FcPatternFormat) IA__FcPatternFormat FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternFormat IA__FcPatternFormat
extern __typeof (FcRangeCreateDouble) IA__FcRangeCreateDouble FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcRangeCreateDouble IA__FcRangeCreateDouble
extern __typeof (FcRangeCreateInteger) IA__FcRangeCreateInteger FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcRangeCreateInteger IA__FcRangeCreateInteger
extern __typeof (FcRangeDestroy) IA__FcRangeDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcRangeDestroy IA__FcRangeDestroy
extern __typeof (FcRangeCopy) IA__FcRangeCopy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcRangeCopy IA__FcRangeCopy
extern __typeof (FcRangeGetDouble) IA__FcRangeGetDouble FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcRangeGetDouble IA__FcRangeGetDouble
extern __typeof (FcPatternIterStart) IA__FcPatternIterStart FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternIterStart IA__FcPatternIterStart
extern __typeof (FcPatternIterNext) IA__FcPatternIterNext FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternIterNext IA__FcPatternIterNext
extern __typeof (FcPatternIterEqual) IA__FcPatternIterEqual FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternIterEqual IA__FcPatternIterEqual
extern __typeof (FcPatternFindIter) IA__FcPatternFindIter FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternFindIter IA__FcPatternFindIter
extern __typeof (FcPatternIterIsValid) IA__FcPatternIterIsValid FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternIterIsValid IA__FcPatternIterIsValid
extern __typeof (FcPatternIterGetObject) IA__FcPatternIterGetObject FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternIterGetObject IA__FcPatternIterGetObject
extern __typeof (FcPatternIterValueCount) IA__FcPatternIterValueCount FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternIterValueCount IA__FcPatternIterValueCount
extern __typeof (FcPatternIterGetValue) IA__FcPatternIterGetValue FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcPatternIterGetValue IA__FcPatternIterGetValue
extern __typeof (FcWeightFromOpenType) IA__FcWeightFromOpenType FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcWeightFromOpenType IA__FcWeightFromOpenType
extern __typeof (FcWeightFromOpenTypeDouble) IA__FcWeightFromOpenTypeDouble FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcWeightFromOpenTypeDouble IA__FcWeightFromOpenTypeDouble
extern __typeof (FcWeightToOpenType) IA__FcWeightToOpenType FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcWeightToOpenType IA__FcWeightToOpenType
extern __typeof (FcWeightToOpenTypeDouble) IA__FcWeightToOpenTypeDouble FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcWeightToOpenTypeDouble IA__FcWeightToOpenTypeDouble
extern __typeof (FcStrCopy) IA__FcStrCopy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrCopy IA__FcStrCopy
extern __typeof (FcStrCopyFilename) IA__FcStrCopyFilename FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrCopyFilename IA__FcStrCopyFilename
extern __typeof (FcStrPlus) IA__FcStrPlus FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrPlus IA__FcStrPlus
extern __typeof (FcStrFree) IA__FcStrFree FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrFree IA__FcStrFree
extern __typeof (FcStrDowncase) IA__FcStrDowncase FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrDowncase IA__FcStrDowncase
extern __typeof (FcStrCmpIgnoreCase) IA__FcStrCmpIgnoreCase FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrCmpIgnoreCase IA__FcStrCmpIgnoreCase
extern __typeof (FcStrCmp) IA__FcStrCmp FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrCmp IA__FcStrCmp
extern __typeof (FcStrStrIgnoreCase) IA__FcStrStrIgnoreCase FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrStrIgnoreCase IA__FcStrStrIgnoreCase
extern __typeof (FcStrStr) IA__FcStrStr FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrStr IA__FcStrStr
extern __typeof (FcUtf8ToUcs4) IA__FcUtf8ToUcs4 FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcUtf8ToUcs4 IA__FcUtf8ToUcs4
extern __typeof (FcUtf8Len) IA__FcUtf8Len FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcUtf8Len IA__FcUtf8Len
extern __typeof (FcUcs4ToUtf8) IA__FcUcs4ToUtf8 FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcUcs4ToUtf8 IA__FcUcs4ToUtf8
extern __typeof (FcUtf16ToUcs4) IA__FcUtf16ToUcs4 FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcUtf16ToUcs4 IA__FcUtf16ToUcs4
extern __typeof (FcUtf16Len) IA__FcUtf16Len FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcUtf16Len IA__FcUtf16Len
extern __typeof (FcStrBuildFilename) IA__FcStrBuildFilename FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrBuildFilename IA__FcStrBuildFilename
extern __typeof (FcStrDirname) IA__FcStrDirname FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrDirname IA__FcStrDirname
extern __typeof (FcStrBasename) IA__FcStrBasename FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrBasename IA__FcStrBasename
extern __typeof (FcStrSetCreate) IA__FcStrSetCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrSetCreate IA__FcStrSetCreate
extern __typeof (FcStrSetMember) IA__FcStrSetMember FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrSetMember IA__FcStrSetMember
extern __typeof (FcStrSetEqual) IA__FcStrSetEqual FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrSetEqual IA__FcStrSetEqual
extern __typeof (FcStrSetAdd) IA__FcStrSetAdd FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrSetAdd IA__FcStrSetAdd
extern __typeof (FcStrSetAddFilename) IA__FcStrSetAddFilename FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrSetAddFilename IA__FcStrSetAddFilename
extern __typeof (FcStrSetDel) IA__FcStrSetDel FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrSetDel IA__FcStrSetDel
extern __typeof (FcStrSetDestroy) IA__FcStrSetDestroy FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrSetDestroy IA__FcStrSetDestroy
extern __typeof (FcStrListCreate) IA__FcStrListCreate FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrListCreate IA__FcStrListCreate
extern __typeof (FcStrListFirst) IA__FcStrListFirst FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrListFirst IA__FcStrListFirst
extern __typeof (FcStrListNext) IA__FcStrListNext FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrListNext IA__FcStrListNext
extern __typeof (FcStrListDone) IA__FcStrListDone FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcStrListDone IA__FcStrListDone
extern __typeof (FcConfigParseAndLoad) IA__FcConfigParseAndLoad FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigParseAndLoad IA__FcConfigParseAndLoad
extern __typeof (FcConfigParseAndLoadFromMemory) IA__FcConfigParseAndLoadFromMemory FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigParseAndLoadFromMemory IA__FcConfigParseAndLoadFromMemory
extern __typeof (FcConfigGetRescanInverval) IA__FcConfigGetRescanInverval FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigGetRescanInverval IA__FcConfigGetRescanInverval
extern __typeof (FcConfigSetRescanInverval) IA__FcConfigSetRescanInverval FC_ATTRIBUTE_VISIBILITY_HIDDEN;
#define FcConfigSetRescanInverval IA__FcConfigSetRescanInverval
