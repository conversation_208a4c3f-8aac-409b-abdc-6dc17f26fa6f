# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/work/fontconfig

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/work/fontconfig/build-cmake

# Utility rule file for generated_headers.

# Include any custom commands dependencies for this target.
include src/CMakeFiles/generated_headers.dir/compiler_depend.make

# Include the progress variables for this target.
include src/CMakeFiles/generated_headers.dir/progress.make

src/CMakeFiles/generated_headers: src/fcalias.h
src/CMakeFiles/generated_headers: src/fcaliastail.h
src/CMakeFiles/generated_headers: src/fcftalias.h
src/CMakeFiles/generated_headers: src/fcftaliastail.h
src/CMakeFiles/generated_headers: src/fcobjshash.h
src/CMakeFiles/generated_headers: src/fcstdint.h

src/fcalias.h: /Users/<USER>/work/fontconfig/src/makealias.py
src/fcalias.h: fontconfig/fontconfig.h
src/fcalias.h: /Users/<USER>/work/fontconfig/src/fcdeprecate.h
src/fcalias.h: /Users/<USER>/work/fontconfig/fontconfig/fcprivate.h
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating alias headers"
	cd /Users/<USER>/work/fontconfig/build-cmake/src && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/src/makealias.py /Users/<USER>/work/fontconfig/src /Users/<USER>/work/fontconfig/build-cmake/src/fcalias.h /Users/<USER>/work/fontconfig/build-cmake/src/fcaliastail.h /Users/<USER>/work/fontconfig/build-cmake/src/../fontconfig/fontconfig.h /Users/<USER>/work/fontconfig/src/fcdeprecate.h /Users/<USER>/work/fontconfig/src/../fontconfig/fcprivate.h

src/fcaliastail.h: src/fcalias.h
	@$(CMAKE_COMMAND) -E touch_nocreate src/fcaliastail.h

src/fcftalias.h: /Users/<USER>/work/fontconfig/src/makealias.py
src/fcftalias.h: /Users/<USER>/work/fontconfig/fontconfig/fcfreetype.h
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Generating FreeType alias headers"
	cd /Users/<USER>/work/fontconfig/build-cmake/src && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/src/makealias.py /Users/<USER>/work/fontconfig/src /Users/<USER>/work/fontconfig/build-cmake/src/fcftalias.h /Users/<USER>/work/fontconfig/build-cmake/src/fcftaliastail.h /Users/<USER>/work/fontconfig/src/../fontconfig/fcfreetype.h

src/fcftaliastail.h: src/fcftalias.h
	@$(CMAKE_COMMAND) -E touch_nocreate src/fcftaliastail.h

src/fcobjshash.h: src/fcobjshash.gperf
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Generating fcobjshash.h with gperf"
	cd /Users/<USER>/work/fontconfig/build-cmake/src && /usr/bin/gperf --pic -m 100 /Users/<USER>/work/fontconfig/build-cmake/src/fcobjshash.gperf --output-file /Users/<USER>/work/fontconfig/build-cmake/src/fcobjshash.h

src/fcobjshash.gperf: /Users/<USER>/work/fontconfig/src/fcobjshash.gperf.h
src/fcobjshash.gperf: /Users/<USER>/work/fontconfig/src/cutout.py
src/fcobjshash.gperf: /Users/<USER>/work/fontconfig/src/process-gperf.py
src/fcobjshash.gperf: /Users/<USER>/work/fontconfig/src/fcobjs.h
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Generating fcobjshash.gperf"
	cd /Users/<USER>/work/fontconfig/build-cmake/src && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/src/cutout.py /Users/<USER>/work/fontconfig/src/fcobjshash.gperf.h /Users/<USER>/work/fontconfig/build-cmake/src/fcobjshash.gperf.tmp
	cd /Users/<USER>/work/fontconfig/build-cmake/src && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/src/process-gperf.py /usr/bin/cc /Users/<USER>/work/fontconfig/build-cmake/src/fcobjshash.gperf.tmp /Users/<USER>/work/fontconfig/build-cmake/src/fcobjshash.gperf

src/CMakeFiles/generated_headers.dir/codegen:
.PHONY : src/CMakeFiles/generated_headers.dir/codegen

generated_headers: src/CMakeFiles/generated_headers
generated_headers: src/fcalias.h
generated_headers: src/fcaliastail.h
generated_headers: src/fcftalias.h
generated_headers: src/fcftaliastail.h
generated_headers: src/fcobjshash.gperf
generated_headers: src/fcobjshash.h
generated_headers: src/CMakeFiles/generated_headers.dir/build.make
.PHONY : generated_headers

# Rule to build all files generated by this target.
src/CMakeFiles/generated_headers.dir/build: generated_headers
.PHONY : src/CMakeFiles/generated_headers.dir/build

src/CMakeFiles/generated_headers.dir/clean:
	cd /Users/<USER>/work/fontconfig/build-cmake/src && $(CMAKE_COMMAND) -P CMakeFiles/generated_headers.dir/cmake_clean.cmake
.PHONY : src/CMakeFiles/generated_headers.dir/clean

src/CMakeFiles/generated_headers.dir/depend:
	cd /Users/<USER>/work/fontconfig/build-cmake && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/work/fontconfig /Users/<USER>/work/fontconfig/src /Users/<USER>/work/fontconfig/build-cmake /Users/<USER>/work/fontconfig/build-cmake/src /Users/<USER>/work/fontconfig/build-cmake/src/CMakeFiles/generated_headers.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/CMakeFiles/generated_headers.dir/depend

