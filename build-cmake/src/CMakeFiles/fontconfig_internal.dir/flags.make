# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile C with /usr/bin/cc
C_DEFINES = -DHAVE_CONFIG_H

C_INCLUDES = -I/Users/<USER>/work/fontconfig -I/Users/<USER>/work/fontconfig/build-cmake/src -I/Users/<USER>/work/fontconfig/build-cmake/src/.. -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -I/Users/<USER>/work/fontconfig/build-cmake/src/../fc-lang -I/Users/<USER>/work/fontconfig/build-cmake/src/../fc-case

C_FLAGS = -std=gnu11

