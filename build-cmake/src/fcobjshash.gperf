%{
%}
%struct-type
%language=ANSI-C
%includes
%enum
%readonly-tables
%define slot-name name
%define hash-function-name FcObjectTypeHash
%define lookup-function-name FcObjectTypeLookup
%pic
%define string-pool-name FcObjectTypeNamePool
struct FcObjectTypeInfo {
int name;
int id;
};
%%
FC_FAMILY,FC_FAMILY_OBJECT
FC_FAMILYLANG,FC_FAMILYLANG_OBJECT
FC_STYLE,FC_STYLE_OBJECT
FC_STYLELANG,FC_STYLELANG_OBJECT
FC_FULLNAME,FC_FULLNAME_OBJECT
FC_FULLNAMELANG,FC_FULLNAMELANG_OBJECT
FC_SLANT,FC_SLANT_OBJECT
FC_WEIGHT,FC_WEIGHT_OBJECT
FC_WIDTH,FC_WIDTH_OBJECT
FC_SIZE,FC_SIZE_OBJECT
FC_ASPECT,FC_ASPECT_OBJECT
FC_PIXEL_SIZE,FC_PIXEL_SIZE_OBJECT
FC_SPACING,FC_SPACING_OBJECT
FC_FOUNDRY,FC_FOUNDRY_OBJECT
FC_ANTIALIAS,FC_ANTIALIAS_OBJECT
FC_HINT_STYLE,FC_HINT_STYLE_OBJECT
FC_HINTING,FC_HINTING_OBJECT
FC_VERTICAL_LAYOUT,FC_VERTICAL_LAYOUT_OBJECT
FC_AUTOHINT,FC_AUTOHINT_OBJECT
FC_GLOBAL_ADVANCE,FC_GLOBAL_ADVANCE_OBJECT
FC_FILE,FC_FILE_OBJECT
FC_INDEX,FC_INDEX_OBJECT
FC_RASTERIZER,FC_RASTERIZER_OBJECT
FC_OUTLINE,FC_OUTLINE_OBJECT
FC_SCALABLE,FC_SCALABLE_OBJECT
FC_DPI,FC_DPI_OBJECT
FC_RGBA,FC_RGBA_OBJECT
FC_SCALE,FC_SCALE_OBJECT
FC_MINSPACE,FC_MINSPACE_OBJECT
FC_CHARWIDTH,FC_CHARWIDTH_OBJECT
FC_CHAR_HEIGHT,FC_CHAR_HEIGHT_OBJECT
FC_MATRIX,FC_MATRIX_OBJECT
FC_CHARSET,FC_CHARSET_OBJECT
FC_LANG,FC_LANG_OBJECT
FC_FONTVERSION,FC_FONTVERSION_OBJECT
FC_CAPABILITY,FC_CAPABILITY_OBJECT
FC_FONTFORMAT,FC_FONTFORMAT_OBJECT
FC_EMBOLDEN,FC_EMBOLDEN_OBJECT
FC_EMBEDDED_BITMAP,FC_EMBEDDED_BITMAP_OBJECT
FC_DECORATIVE,FC_DECORATIVE_OBJECT
FC_LCD_FILTER,FC_LCD_FILTER_OBJECT
FC_NAMELANG,FC_NAMELANG_OBJECT
FC_FONT_FEATURES,FC_FONT_FEATURES_OBJECT
FC_PRGNAME,FC_PRGNAME_OBJECT
FC_HASH,FC_HASH_OBJECT
FC_POSTSCRIPT_NAME,FC_POSTSCRIPT_NAME_OBJECT
FC_COLOR,FC_COLOR_OBJECT
FC_SYMBOL,FC_SYMBOL_OBJECT
FC_FONT_VARIATIONS,FC_FONT_VARIATIONS_OBJECT
FC_VARIABLE,FC_VARIABLE_OBJECT
FC_FONT_HAS_HINT,FC_FONT_HAS_HINT_OBJECT
FC_ORDER,FC_ORDER_OBJECT
FC_DESKTOP_NAME,FC_DESKTOP_NAME_OBJECT
FC_NAMED_INSTANCE,FC_NAMED_INSTANCE_OBJECT
FC_FONT_WRAPPER,FC_FONT_WRAPPER_OBJECT
