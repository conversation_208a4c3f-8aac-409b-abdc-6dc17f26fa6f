# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/work/fontconfig

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/work/fontconfig/build-cmake

# Utility rule file for lang_normalize_conf.

# Include any custom commands dependencies for this target.
include conf.d/CMakeFiles/lang_normalize_conf.dir/compiler_depend.make

# Include the progress variables for this target.
include conf.d/CMakeFiles/lang_normalize_conf.dir/progress.make

conf.d/CMakeFiles/lang_normalize_conf: conf.d/35-lang-normalize.conf

conf.d/35-lang-normalize.conf: /Users/<USER>/work/fontconfig/conf.d/write-35-lang-normalize-conf.py
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating 35-lang-normalize.conf"
	cd /Users/<USER>/work/fontconfig/build-cmake/conf.d && /usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13 /Users/<USER>/work/fontconfig/conf.d/write-35-lang-normalize-conf.py aa\ ab\ af\ agr\ ak\ am\ an\ anp\ ar\ as\ ast\ av\ ay\ ayc\ ba\ be\ bem\ bg\ bh\ bhb\ bho\ bi\ bin\ bm\ bn\ bo\ br\ brx\ bs\ bua\ byn\ ca\ ce\ ch\ chm\ chr\ ckb\ cmn\ co\ cop\ crh\ cs\ csb\ cu\ cv\ cy\ da\ de\ doi\ dsb\ dv\ dz\ ee\ el\ en\ eo\ es\ et\ eu\ fa\ fat\ ff\ fi\ fil\ fj\ fo\ fr\ fur\ fy\ ga\ gd\ gez\ gl\ gn\ got\ gu\ gv\ ha\ hak\ haw\ he\ hi\ hif\ hne\ ho\ hr\ hsb\ ht\ hu\ hy\ hz\ ia\ id\ ie\ ig\ ii\ ik\ io\ is\ it\ iu\ ja\ jv\ ka\ kaa\ kab\ ki\ kj\ kk\ kl\ km\ kn\ ko\ kok\ kr\ ks\ kum\ kv\ kw\ kwm\ ky\ la\ lah\ lb\ lez\ lg\ li\ lij\ ln\ lo\ lt\ lv\ lzh\ mag\ mai\ mfe\ mg\ mh\ mhr\ mi\ miq\ mjw\ mk\ ml\ mni\ mnw\ mo\ mr\ ms\ mt\ my\ na\ nan\ nb\ nds\ ne\ ng\ nhn\ niu\ nl\ nn\ no\ nqo\ nr\ nso\ nv\ ny\ oc\ om\ or\ os\ ota\ pa\ pes\ pl\ prs\ pt\ qu\ quz\ raj\ rif\ rm\ rn\ ro\ ru\ rw\ sa\ sah\ sat\ sc\ sco\ sd\ se\ sel\ sg\ sgs\ sh\ shn\ shs\ si\ sid\ sk\ sl\ sm\ sma\ smj\ smn\ sms\ sn\ so\ sq\ sr\ ss\ st\ su\ sv\ sw\ syr\ szl\ ta\ tcy\ te\ tg\ th\ the\ tig\ tk\ tl\ tn\ to\ tpi\ tr\ ts\ tt\ tw\ ty\ tyv\ ug\ uk\ unm\ ur\ uz\ ve\ vi\ vo\ vot\ wa\ wae\ wal\ wen\ wo\ xh\ yap\ yi\ yo\ yue\ yuw\ za\ zu /Users/<USER>/work/fontconfig/build-cmake/conf.d/35-lang-normalize.conf

conf.d/CMakeFiles/lang_normalize_conf.dir/codegen:
.PHONY : conf.d/CMakeFiles/lang_normalize_conf.dir/codegen

lang_normalize_conf: conf.d/35-lang-normalize.conf
lang_normalize_conf: conf.d/CMakeFiles/lang_normalize_conf
lang_normalize_conf: conf.d/CMakeFiles/lang_normalize_conf.dir/build.make
.PHONY : lang_normalize_conf

# Rule to build all files generated by this target.
conf.d/CMakeFiles/lang_normalize_conf.dir/build: lang_normalize_conf
.PHONY : conf.d/CMakeFiles/lang_normalize_conf.dir/build

conf.d/CMakeFiles/lang_normalize_conf.dir/clean:
	cd /Users/<USER>/work/fontconfig/build-cmake/conf.d && $(CMAKE_COMMAND) -P CMakeFiles/lang_normalize_conf.dir/cmake_clean.cmake
.PHONY : conf.d/CMakeFiles/lang_normalize_conf.dir/clean

conf.d/CMakeFiles/lang_normalize_conf.dir/depend:
	cd /Users/<USER>/work/fontconfig/build-cmake && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/work/fontconfig /Users/<USER>/work/fontconfig/conf.d /Users/<USER>/work/fontconfig/build-cmake /Users/<USER>/work/fontconfig/build-cmake/conf.d /Users/<USER>/work/fontconfig/build-cmake/conf.d/CMakeFiles/lang_normalize_conf.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : conf.d/CMakeFiles/lang_normalize_conf.dir/depend

