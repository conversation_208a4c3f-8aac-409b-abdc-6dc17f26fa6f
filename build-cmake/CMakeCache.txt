# This is the CMakeCache file.
# For build in directory: /Users/<USER>/work/fontconfig/build-cmake
# It was generated by CMake: /usr/local/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Additional font directories (semicolon separated)
ADDITIONAL_FONTS_DIRS:STRING=

//Default bitmap font configuration
BITMAP_CONF:STRING=no-except-emoji

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:STRING=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=Font configuration and customization library

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=fontconfig

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=2.16.2

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=2

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=16

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=2

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Default font directories (semicolon separated)
DEFAULT_FONTS_DIRS:STRING=

//Default hinting configuration
DEFAULT_HINTING:STRING=slight

//Default sub-pixel rendering
DEFAULT_SUB_PIXEL_RENDERING:STRING=none

//Path to a program.
DOCBOOK2HTML_EXECUTABLE:FILEPATH=DOCBOOK2HTML_EXECUTABLE-NOTFOUND

//Path to a program.
DOCBOOK2MAN_EXECUTABLE:FILEPATH=DOCBOOK2MAN_EXECUTABLE-NOTFOUND

//Path to a program.
DOCBOOK2PDF_EXECUTABLE:FILEPATH=DOCBOOK2PDF_EXECUTABLE-NOTFOUND

//Path to a program.
DOCBOOK2TXT_EXECUTABLE:FILEPATH=DOCBOOK2TXT_EXECUTABLE-NOTFOUND

//Run fc-cache on install
ENABLE_CACHE_BUILD:BOOL=ON

//Build documentation
ENABLE_DOCS:BOOL=ON

//Use Fontations for indexing
ENABLE_FONTATIONS:BOOL=OFF

//Enable iconv support
ENABLE_ICONV:BOOL=OFF

//Enable native language support
ENABLE_NLS:BOOL=ON

//Enable unit tests
ENABLE_TESTS:BOOL=ON

//Build command-line tools
ENABLE_TOOLS:BOOL=ON

//Base config directory
FC_BASECONFIGDIR:PATH=/usr/local/etc/fonts

//Cache directory
FC_CACHEDIR:PATH=/usr/local/var/cache/fontconfig

//Config directory
FC_CONFIGDIR:PATH=/usr/local/etc/fonts/conf.d

//Template directory
FC_TEMPLATEDIR:PATH=/usr/local/share/fontconfig/conf.avail

//XML directory
FC_XMLDIR:PATH=/usr/local/share/xml/fontconfig

//Path to a program.
GPERF_EXECUTABLE:FILEPATH=/usr/bin/gperf

//libintl include directory
Intl_INCLUDE_DIR:PATH=Intl_INCLUDE_DIR-NOTFOUND

//libintl libraries (if not in the C library)
Intl_LIBRARY:FILEPATH=/usr/local/lib/libintl.dylib

//Path to a library.
MATH_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/local/bin/pkg-config

//Path to a program.
PYTEST_EXECUTABLE:FILEPATH=PYTEST_EXECUTABLE-NOTFOUND

//XML backend to use (auto, expat, libxml2)
XML_BACKEND:STRING=auto

//Value Computed by CMake
fontconfig_BINARY_DIR:STATIC=/Users/<USER>/work/fontconfig/build-cmake

//Value Computed by CMake
fontconfig_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
fontconfig_SOURCE_DIR:STATIC=/Users/<USER>/work/fontconfig

//Path to a library.
pkgcfg_lib_EXPAT_expat:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libexpat.tbd

//Path to a library.
pkgcfg_lib_FREETYPE2_freetype:FILEPATH=/usr/local/opt/freetype/lib/libfreetype.dylib


########################
# INTERNAL cache entries
########################

//STRINGS property for variable: BITMAP_CONF
BITMAP_CONF-STRINGS:INTERNAL=yes;no;no-except-emoji
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/work/fontconfig/build-cmake
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=2
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/local/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/local/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/local/bin/ctest
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/local/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/work/fontconfig
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=18
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/local/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//STRINGS property for variable: DEFAULT_HINTING
DEFAULT_HINTING-STRINGS:INTERNAL=none;slight;medium;full
//STRINGS property for variable: DEFAULT_SUB_PIXEL_RENDERING
DEFAULT_SUB_PIXEL_RENDERING-STRINGS:INTERNAL=none;bgr;rgb;vbgr;vrgb
EXPAT_CFLAGS:INTERNAL=
EXPAT_CFLAGS_I:INTERNAL=
EXPAT_CFLAGS_OTHER:INTERNAL=
EXPAT_FOUND:INTERNAL=1
EXPAT_INCLUDEDIR:INTERNAL=/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include
EXPAT_INCLUDE_DIRS:INTERNAL=
EXPAT_LDFLAGS:INTERNAL=-L/usr/lib;-lexpat
EXPAT_LDFLAGS_OTHER:INTERNAL=
EXPAT_LIBDIR:INTERNAL=/usr/lib
EXPAT_LIBRARIES:INTERNAL=expat
EXPAT_LIBRARY_DIRS:INTERNAL=/usr/lib
EXPAT_LIBS:INTERNAL=
EXPAT_LIBS_L:INTERNAL=
EXPAT_LIBS_OTHER:INTERNAL=
EXPAT_LIBS_PATHS:INTERNAL=
EXPAT_MODULE_NAME:INTERNAL=expat
EXPAT_PREFIX:INTERNAL=/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr
EXPAT_STATIC_CFLAGS:INTERNAL=
EXPAT_STATIC_CFLAGS_I:INTERNAL=
EXPAT_STATIC_CFLAGS_OTHER:INTERNAL=
EXPAT_STATIC_INCLUDE_DIRS:INTERNAL=
EXPAT_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lexpat
EXPAT_STATIC_LDFLAGS_OTHER:INTERNAL=
EXPAT_STATIC_LIBDIR:INTERNAL=
EXPAT_STATIC_LIBRARIES:INTERNAL=expat
EXPAT_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
EXPAT_STATIC_LIBS:INTERNAL=
EXPAT_STATIC_LIBS_L:INTERNAL=
EXPAT_STATIC_LIBS_OTHER:INTERNAL=
EXPAT_STATIC_LIBS_PATHS:INTERNAL=
EXPAT_VERSION:INTERNAL=2.4.1
EXPAT_expat_INCLUDEDIR:INTERNAL=
EXPAT_expat_LIBDIR:INTERNAL=
EXPAT_expat_PREFIX:INTERNAL=
EXPAT_expat_VERSION:INTERNAL=
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/local/bin/pkg-config][v2.4.3()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13][found components: Interpreter ][v3.13.3()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
FREETYPE2_CFLAGS:INTERNAL=-I/usr/local/opt/freetype/include/freetype2;-I/usr/local/opt/libpng/include/libpng16
FREETYPE2_CFLAGS_I:INTERNAL=
FREETYPE2_CFLAGS_OTHER:INTERNAL=
FREETYPE2_FOUND:INTERNAL=1
FREETYPE2_INCLUDEDIR:INTERNAL=/usr/local/opt/freetype/include
FREETYPE2_INCLUDE_DIRS:INTERNAL=/usr/local/opt/freetype/include/freetype2;/usr/local/opt/libpng/include/libpng16
FREETYPE2_LDFLAGS:INTERNAL=-L/usr/local/opt/freetype/lib;-lfreetype
FREETYPE2_LDFLAGS_OTHER:INTERNAL=
FREETYPE2_LIBDIR:INTERNAL=/usr/local/opt/freetype/lib
FREETYPE2_LIBRARIES:INTERNAL=freetype
FREETYPE2_LIBRARY_DIRS:INTERNAL=/usr/local/opt/freetype/lib
FREETYPE2_LIBS:INTERNAL=
FREETYPE2_LIBS_L:INTERNAL=
FREETYPE2_LIBS_OTHER:INTERNAL=
FREETYPE2_LIBS_PATHS:INTERNAL=
FREETYPE2_MODULE_NAME:INTERNAL=freetype2
FREETYPE2_PREFIX:INTERNAL=/usr/local/opt/freetype
FREETYPE2_STATIC_CFLAGS:INTERNAL=-I/usr/local/opt/freetype/include/freetype2;-I/usr/local/opt/libpng/include/libpng16
FREETYPE2_STATIC_CFLAGS_I:INTERNAL=
FREETYPE2_STATIC_CFLAGS_OTHER:INTERNAL=
FREETYPE2_STATIC_INCLUDE_DIRS:INTERNAL=/usr/local/opt/freetype/include/freetype2;/usr/local/opt/libpng/include/libpng16
FREETYPE2_STATIC_LDFLAGS:INTERNAL=-L/usr/local/opt/freetype/lib;-lfreetype;-lbz2;-L/usr/local/opt/libpng/lib;-lpng16;-L/usr/lib;-lz
FREETYPE2_STATIC_LDFLAGS_OTHER:INTERNAL=
FREETYPE2_STATIC_LIBDIR:INTERNAL=
FREETYPE2_STATIC_LIBRARIES:INTERNAL=freetype;bz2;png16;z
FREETYPE2_STATIC_LIBRARY_DIRS:INTERNAL=/usr/local/opt/freetype/lib;/usr/local/opt/libpng/lib;/usr/lib
FREETYPE2_STATIC_LIBS:INTERNAL=
FREETYPE2_STATIC_LIBS_L:INTERNAL=
FREETYPE2_STATIC_LIBS_OTHER:INTERNAL=
FREETYPE2_STATIC_LIBS_PATHS:INTERNAL=
FREETYPE2_VERSION:INTERNAL=26.2.20
FREETYPE2_freetype2_INCLUDEDIR:INTERNAL=
FREETYPE2_freetype2_LIBDIR:INTERNAL=
FREETYPE2_freetype2_PREFIX:INTERNAL=
FREETYPE2_freetype2_VERSION:INTERNAL=
//Test FREETYPE_PCF_LONG_FAMILY_NAMES
FREETYPE_PCF_LONG_FAMILY_NAMES:INTERNAL=1
//Have function dcgettext
HAVE_DCGETTEXT:INTERNAL=
//Have include dirent.h
HAVE_DIRENT_H:INTERNAL=1
//Have include dlfcn.h
HAVE_DLFCN_H:INTERNAL=1
//Have include fcntl.h
HAVE_FCNTL_H:INTERNAL=1
//Test HAVE_FLEXIBLE_ARRAY_MEMBER
HAVE_FLEXIBLE_ARRAY_MEMBER:INTERNAL=1
//Have function fstatfs
HAVE_FSTATFS:INTERNAL=1
//Have function fstatvfs
HAVE_FSTATVFS:INTERNAL=1
//Have function FT_Done_MM_Var
HAVE_FT_DONE_MM_VAR:INTERNAL=1
//Have function FT_Get_BDF_Property
HAVE_FT_GET_BDF_PROPERTY:INTERNAL=1
//Have function FT_Get_PS_Font_Info
HAVE_FT_GET_PS_FONT_INFO:INTERNAL=1
//Have function FT_Get_X11_Font_Format
HAVE_FT_GET_X11_FONT_FORMAT:INTERNAL=1
//Have function FT_Has_PS_Glyph_Names
HAVE_FT_HAS_PS_GLYPH_NAMES:INTERNAL=1
//Have function getexecname
HAVE_GETEXECNAME:INTERNAL=
//Have function getopt
HAVE_GETOPT:INTERNAL=1
//Have function getopt_long
HAVE_GETOPT_LONG:INTERNAL=1
//Have function getpagesize
HAVE_GETPAGESIZE:INTERNAL=1
//Have function getpid
HAVE_GETPID:INTERNAL=1
//Have function getprogname
HAVE_GETPROGNAME:INTERNAL=1
//Have function gettext
HAVE_GETTEXT:INTERNAL=
//Result of TRY_COMPILE
HAVE_INT32_T:INTERNAL=TRUE
//Test HAVE_INTEL_ATOMIC_PRIMITIVES
HAVE_INTEL_ATOMIC_PRIMITIVES:INTERNAL=1
//Result of TRY_COMPILE
HAVE_INTPTR_T:INTERNAL=TRUE
//Have include inttypes.h
HAVE_INTTYPES_H:INTERNAL=1
//Have function link
HAVE_LINK:INTERNAL=1
//Have function localtime_r
HAVE_LOCALTIME_R:INTERNAL=1
//Have function lrand48
HAVE_LRAND48:INTERNAL=1
//Have function lstat
HAVE_LSTAT:INTERNAL=1
//Have function mkdtemp
HAVE_MKDTEMP:INTERNAL=1
//Have function mkostemp
HAVE_MKOSTEMP:INTERNAL=1
//Have function mkstemp
HAVE_MKSTEMP:INTERNAL=1
//Have function mmap
HAVE_MMAP:INTERNAL=1
//Have symbol posix_fadvise
HAVE_POSIX_FADVISE:INTERNAL=
//Test HAVE_PTHREAD_PRIO_INHERIT
HAVE_PTHREAD_PRIO_INHERIT:INTERNAL=1
//Have function rand
HAVE_RAND:INTERNAL=1
//Have function random
HAVE_RANDOM:INTERNAL=1
//Have function random_r
HAVE_RANDOM_R:INTERNAL=
//Have function rand_r
HAVE_RAND_R:INTERNAL=1
//Have function readlink
HAVE_READLINK:INTERNAL=1
//Result of TRY_COMPILE
HAVE_SIZEOF_VOID_P:INTERNAL=TRUE
//Test HAVE_SOLARIS_ATOMIC_OPS
HAVE_SOLARIS_ATOMIC_OPS:INTERNAL=
//Test HAVE_STDATOMIC_PRIMITIVES
HAVE_STDATOMIC_PRIMITIVES:INTERNAL=1
//Have include stddef.h
HAVE_STDDEF_H:INTERNAL=1
//Have include stdint.h
HAVE_STDINT_H:INTERNAL=1
//Have include stdio.h
HAVE_STDIO_H:INTERNAL=1
//Have include stdlib.h
HAVE_STDLIB_H:INTERNAL=1
//Have function strerror
HAVE_STRERROR:INTERNAL=1
//Have function strerror_r
HAVE_STRERROR_R:INTERNAL=1
//Have include strings.h
HAVE_STRINGS_H:INTERNAL=1
//Have include string.h
HAVE_STRING_H:INTERNAL=1
//Test HAVE_STRUCT_DIRENT_D_TYPE
HAVE_STRUCT_DIRENT_D_TYPE:INTERNAL=1
//Test HAVE_STRUCT_STATFS_F_FLAGS
HAVE_STRUCT_STATFS_F_FLAGS:INTERNAL=
//Test HAVE_STRUCT_STATFS_F_FSTYPENAME
HAVE_STRUCT_STATFS_F_FSTYPENAME:INTERNAL=
//Test HAVE_STRUCT_STATVFS_F_BASETYPE
HAVE_STRUCT_STATVFS_F_BASETYPE:INTERNAL=
//Test HAVE_STRUCT_STATVFS_F_FSTYPENAME
HAVE_STRUCT_STATVFS_F_FSTYPENAME:INTERNAL=
//Test HAVE_STRUCT_STAT_ST_MTIM
HAVE_STRUCT_STAT_ST_MTIM:INTERNAL=
//Have include sys/mount.h
HAVE_SYS_MOUNT_H:INTERNAL=1
//Have include sys/param.h
HAVE_SYS_PARAM_H:INTERNAL=1
//Have include sys/statfs.h
HAVE_SYS_STATFS_H:INTERNAL=
//Have include sys/statvfs.h
HAVE_SYS_STATVFS_H:INTERNAL=1
//Have include sys/stat.h
HAVE_SYS_STAT_H:INTERNAL=1
//Have include sys/types.h
HAVE_SYS_TYPES_H:INTERNAL=1
//Have include sys/vfs.h
HAVE_SYS_VFS_H:INTERNAL=
//Have include time.h
HAVE_TIME_H:INTERNAL=1
//Result of TRY_COMPILE
HAVE_UINT64_T:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_UINTPTR_T:INTERNAL=TRUE
//Have include unistd.h
HAVE_UNISTD_H:INTERNAL=1
//Have function vprintf
HAVE_VPRINTF:INTERNAL=1
//Have function vsnprintf
HAVE_VSNPRINTF:INTERNAL=1
//Have function vsprintf
HAVE_VSPRINTF:INTERNAL=1
//Have include wchar.h
HAVE_WCHAR_H:INTERNAL=1
//Have function _mktemp_s
HAVE__MKTEMP_S:INTERNAL=
//CHECK_TYPE_SIZE: sizeof(int32_t)
INT32_T:INTERNAL=4
//CHECK_TYPE_SIZE: sizeof(intptr_t)
INTPTR_T:INTERNAL=8
//ADVANCED property for variable: Intl_INCLUDE_DIR
Intl_INCLUDE_DIR-ADVANCED:INTERNAL=1
//Test Intl_IS_BUILT_IN
Intl_IS_BUILT_IN:INTERNAL=
//ADVANCED property for variable: Intl_LIBRARY
Intl_LIBRARY-ADVANCED:INTERNAL=1
JSONC_CFLAGS:INTERNAL=
JSONC_CFLAGS_I:INTERNAL=
JSONC_CFLAGS_OTHER:INTERNAL=
JSONC_FOUND:INTERNAL=
JSONC_INCLUDEDIR:INTERNAL=
JSONC_LIBDIR:INTERNAL=
JSONC_LIBS:INTERNAL=
JSONC_LIBS_L:INTERNAL=
JSONC_LIBS_OTHER:INTERNAL=
JSONC_LIBS_PATHS:INTERNAL=
JSONC_MODULE_NAME:INTERNAL=
JSONC_PREFIX:INTERNAL=
JSONC_STATIC_CFLAGS:INTERNAL=
JSONC_STATIC_CFLAGS_I:INTERNAL=
JSONC_STATIC_CFLAGS_OTHER:INTERNAL=
JSONC_STATIC_LIBDIR:INTERNAL=
JSONC_STATIC_LIBS:INTERNAL=
JSONC_STATIC_LIBS_L:INTERNAL=
JSONC_STATIC_LIBS_OTHER:INTERNAL=
JSONC_STATIC_LIBS_PATHS:INTERNAL=
JSONC_VERSION:INTERNAL=
JSONC_json-c_INCLUDEDIR:INTERNAL=
JSONC_json-c_LIBDIR:INTERNAL=
JSONC_json-c_PREFIX:INTERNAL=
JSONC_json-c_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//CHECK_TYPE_SIZE: sizeof(void *)
SIZEOF_VOID_P:INTERNAL=8
//CHECK_TYPE_SIZE: sizeof(uint64_t)
UINT64_T:INTERNAL=8
//CHECK_TYPE_SIZE: sizeof(uintptr_t)
UINTPTR_T:INTERNAL=8
//STRINGS property for variable: XML_BACKEND
XML_BACKEND-STRINGS:INTERNAL=auto;expat;libxml2
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local
//Compiler reason failure
_Python3_Compiler_REASON_FAILURE:INTERNAL=
//Development reason failure
_Python3_Development_REASON_FAILURE:INTERNAL=
//Path to a program.
_Python3_EXECUTABLE:INTERNAL=/usr/local/Frameworks/Python.framework/Versions/3.13/bin/python3.13
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;13;3;64;32;<none>;cpython-313-darwin;abi3;/usr/local/opt/python@3.13/Frameworks/Python.framework/Versions/3.13/lib/python3.13;/usr/local/opt/python@3.13/Frameworks/Python.framework/Versions/3.13/lib/python3.13;/usr/local/lib/python3.13/site-packages;/usr/local/lib/python3.13/site-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=133e2ff1251621b076d962675dda2845
//NumPy reason failure
_Python3_NumPy_REASON_FAILURE:INTERNAL=
__pkg_config_arguments_EXPAT:INTERNAL=expat
__pkg_config_arguments_FREETYPE2:INTERNAL=REQUIRED;freetype2>=21.0.15
__pkg_config_checked_EXPAT:INTERNAL=1
__pkg_config_checked_FREETYPE2:INTERNAL=1
__pkg_config_checked_JSONC:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_EXPAT_expat
pkgcfg_lib_EXPAT_expat-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FREETYPE2_freetype
pkgcfg_lib_FREETYPE2_freetype-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib

