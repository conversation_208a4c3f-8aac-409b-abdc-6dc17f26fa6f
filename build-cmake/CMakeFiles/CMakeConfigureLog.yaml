
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Darwin - 21.6.0 - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/4.0.2/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting C compiler apple sysroot: "/usr/bin/cc" "-E" "apple-sdk.c"
        # 1 "apple-sdk.c"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 370 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.c" 2
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 242 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 165 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 166 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 167 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 243 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.c" 2
        
        
      Found apple sysroot: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MqZN20"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MqZN20"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MqZN20'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_53a70/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_53a70.dir/build.make CMakeFiles/cmTC_53a70.dir/build
        Building C object CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -v -Wl,-v -MD -MT CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MqZN20 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking C executable cmTC_53a70
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_53a70.dir/link.txt --verbose=1
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_53a70 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld  PROJECT:ld64-820.1
        BUILD 20:07:01 Nov  7 2022
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        Library search paths:
        	/usr/local/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -o cmTC_53a70
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "x86_64"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/usr/local/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MqZN20']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_53a70/fast]
        ignore line: [/Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_53a70.dir/build.make CMakeFiles/cmTC_53a70.dir/build]
        ignore line: [Building C object CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -v -Wl -v -MD -MT CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MqZN20 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_53a70]
        ignore line: [/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_53a70.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_53a70 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [x86_64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [12.0.0] ==> ignore
          arg [13.1] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_53a70] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_53a70.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld  PROJECT:ld64-820.1
      BUILD 20:07:01 Nov  7 2022
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 14.0.0, (clang-1400.0.29.202) (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 14.0.0 (tapi-1400.0.11)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindIntl.cmake:113 (check_c_source_compiles)"
      - "CMakeLists.txt:109 (find_package)"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-JacR0P"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-JacR0P"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "Intl_IS_BUILT_IN"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-JacR0P'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_c28c0/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_c28c0.dir/build.make CMakeFiles/cmTC_c28c0.dir/build
        Building C object CMakeFiles/cmTC_c28c0.dir/src.c.o
        /usr/bin/cc -DIntl_IS_BUILT_IN  -std=gnu11 -MD -MT CMakeFiles/cmTC_c28c0.dir/src.c.o -MF CMakeFiles/cmTC_c28c0.dir/src.c.o.d -o CMakeFiles/cmTC_c28c0.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-JacR0P/src.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-JacR0P/src.c:1:10: fatal error: 'libintl.h' file not found
        #include <libintl.h>
                 ^~~~~~~~~~~
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_c28c0.dir/src.c.o] Error 1
        make: *** [cmTC_c28c0/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:131 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-j8x8tB"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-j8x8tB"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-j8x8tB'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b155e/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_b155e.dir/build.make CMakeFiles/cmTC_b155e.dir/build
        Building C object CMakeFiles/cmTC_b155e.dir/src.c.o
        /usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD  -std=gnu11 -MD -MT CMakeFiles/cmTC_b155e.dir/src.c.o -MF CMakeFiles/cmTC_b155e.dir/src.c.o.d -o CMakeFiles/cmTC_b155e.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-j8x8tB/src.c
        Linking C executable cmTC_b155e
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b155e.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_b155e.dir/src.c.o -o cmTC_b155e
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for dirent.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-bzM7E5"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-bzM7E5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DIRENT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-bzM7E5'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_16d3f/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_16d3f.dir/build.make CMakeFiles/cmTC_16d3f.dir/build
        Building C object CMakeFiles/cmTC_16d3f.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_16d3f.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_16d3f.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_16d3f.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-bzM7E5/CheckIncludeFile.c
        Linking C executable cmTC_16d3f
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_16d3f.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_16d3f.dir/CheckIncludeFile.c.o -o cmTC_16d3f
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for dlfcn.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-8XkJug"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-8XkJug"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DLFCN_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-8XkJug'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_13bdb/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_13bdb.dir/build.make CMakeFiles/cmTC_13bdb.dir/build
        Building C object CMakeFiles/cmTC_13bdb.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_13bdb.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_13bdb.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_13bdb.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-8XkJug/CheckIncludeFile.c
        Linking C executable cmTC_13bdb
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_13bdb.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_13bdb.dir/CheckIncludeFile.c.o -o cmTC_13bdb
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for fcntl.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-nnlK7A"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-nnlK7A"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FCNTL_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-nnlK7A'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_230ab/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_230ab.dir/build.make CMakeFiles/cmTC_230ab.dir/build
        Building C object CMakeFiles/cmTC_230ab.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_230ab.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_230ab.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_230ab.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-nnlK7A/CheckIncludeFile.c
        Linking C executable cmTC_230ab
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_230ab.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_230ab.dir/CheckIncludeFile.c.o -o cmTC_230ab
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for inttypes.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-v5vUq0"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-v5vUq0"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INTTYPES_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-v5vUq0'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_42b42/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_42b42.dir/build.make CMakeFiles/cmTC_42b42.dir/build
        Building C object CMakeFiles/cmTC_42b42.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_42b42.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_42b42.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_42b42.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-v5vUq0/CheckIncludeFile.c
        Linking C executable cmTC_42b42
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_42b42.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_42b42.dir/CheckIncludeFile.c.o -o cmTC_42b42
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for stdint.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-x8pjoq"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-x8pjoq"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDINT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-x8pjoq'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_c10e5/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_c10e5.dir/build.make CMakeFiles/cmTC_c10e5.dir/build
        Building C object CMakeFiles/cmTC_c10e5.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_c10e5.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_c10e5.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_c10e5.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-x8pjoq/CheckIncludeFile.c
        Linking C executable cmTC_c10e5
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c10e5.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_c10e5.dir/CheckIncludeFile.c.o -o cmTC_c10e5
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for stdio.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-mnNMHg"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-mnNMHg"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDIO_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-mnNMHg'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_6b8d2/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_6b8d2.dir/build.make CMakeFiles/cmTC_6b8d2.dir/build
        Building C object CMakeFiles/cmTC_6b8d2.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_6b8d2.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_6b8d2.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_6b8d2.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-mnNMHg/CheckIncludeFile.c
        Linking C executable cmTC_6b8d2
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_6b8d2.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_6b8d2.dir/CheckIncludeFile.c.o -o cmTC_6b8d2
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for stdlib.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-b1m2Kv"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-b1m2Kv"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDLIB_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-b1m2Kv'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_3b41f/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_3b41f.dir/build.make CMakeFiles/cmTC_3b41f.dir/build
        Building C object CMakeFiles/cmTC_3b41f.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_3b41f.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_3b41f.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_3b41f.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-b1m2Kv/CheckIncludeFile.c
        Linking C executable cmTC_3b41f
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_3b41f.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_3b41f.dir/CheckIncludeFile.c.o -o cmTC_3b41f
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for strings.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-fdogST"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-fdogST"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRINGS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-fdogST'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_133d2/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_133d2.dir/build.make CMakeFiles/cmTC_133d2.dir/build
        Building C object CMakeFiles/cmTC_133d2.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_133d2.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_133d2.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_133d2.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-fdogST/CheckIncludeFile.c
        Linking C executable cmTC_133d2
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_133d2.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_133d2.dir/CheckIncludeFile.c.o -o cmTC_133d2
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for string.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-M7f64f"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-M7f64f"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRING_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-M7f64f'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_0769a/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_0769a.dir/build.make CMakeFiles/cmTC_0769a.dir/build
        Building C object CMakeFiles/cmTC_0769a.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_0769a.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_0769a.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_0769a.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-M7f64f/CheckIncludeFile.c
        Linking C executable cmTC_0769a
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0769a.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_0769a.dir/CheckIncludeFile.c.o -o cmTC_0769a
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for unistd.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Bweu5b"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Bweu5b"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UNISTD_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Bweu5b'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_d7a59/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_d7a59.dir/build.make CMakeFiles/cmTC_d7a59.dir/build
        Building C object CMakeFiles/cmTC_d7a59.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_d7a59.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_d7a59.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_d7a59.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Bweu5b/CheckIncludeFile.c
        Linking C executable cmTC_d7a59
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d7a59.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_d7a59.dir/CheckIncludeFile.c.o -o cmTC_d7a59
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for sys/statvfs.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-2oKGO7"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-2oKGO7"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_STATVFS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-2oKGO7'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_cafc3/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_cafc3.dir/build.make CMakeFiles/cmTC_cafc3.dir/build
        Building C object CMakeFiles/cmTC_cafc3.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_cafc3.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_cafc3.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_cafc3.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-2oKGO7/CheckIncludeFile.c
        Linking C executable cmTC_cafc3
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_cafc3.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_cafc3.dir/CheckIncludeFile.c.o -o cmTC_cafc3
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for sys/vfs.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-xAdfO4"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-xAdfO4"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_VFS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-xAdfO4'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_92212/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_92212.dir/build.make CMakeFiles/cmTC_92212.dir/build
        Building C object CMakeFiles/cmTC_92212.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_92212.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_92212.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_92212.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-xAdfO4/CheckIncludeFile.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-xAdfO4/CheckIncludeFile.c:1:10: fatal error: 'sys/vfs.h' file not found
        #include <sys/vfs.h>
                 ^~~~~~~~~~~
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_92212.dir/CheckIncludeFile.c.o] Error 1
        make: *** [cmTC_92212/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for sys/statfs.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sVwXYx"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sVwXYx"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_STATFS_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sVwXYx'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_f45b3/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_f45b3.dir/build.make CMakeFiles/cmTC_f45b3.dir/build
        Building C object CMakeFiles/cmTC_f45b3.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_f45b3.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_f45b3.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_f45b3.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sVwXYx/CheckIncludeFile.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sVwXYx/CheckIncludeFile.c:1:10: fatal error: 'sys/statfs.h' file not found
        #include <sys/statfs.h>
                 ^~~~~~~~~~~~~~
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_f45b3.dir/CheckIncludeFile.c.o] Error 1
        make: *** [cmTC_f45b3/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for sys/stat.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-q5twto"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-q5twto"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_STAT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-q5twto'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_3756b/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_3756b.dir/build.make CMakeFiles/cmTC_3756b.dir/build
        Building C object CMakeFiles/cmTC_3756b.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_3756b.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_3756b.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_3756b.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-q5twto/CheckIncludeFile.c
        Linking C executable cmTC_3756b
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_3756b.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_3756b.dir/CheckIncludeFile.c.o -o cmTC_3756b
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for sys/types.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MwFWIK"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MwFWIK"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_TYPES_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MwFWIK'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_5dc6c/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_5dc6c.dir/build.make CMakeFiles/cmTC_5dc6c.dir/build
        Building C object CMakeFiles/cmTC_5dc6c.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_5dc6c.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_5dc6c.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_5dc6c.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MwFWIK/CheckIncludeFile.c
        Linking C executable cmTC_5dc6c
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5dc6c.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_5dc6c.dir/CheckIncludeFile.c.o -o cmTC_5dc6c
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for sys/param.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RWh8Xn"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RWh8Xn"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_PARAM_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RWh8Xn'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_ec5b1/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_ec5b1.dir/build.make CMakeFiles/cmTC_ec5b1.dir/build
        Building C object CMakeFiles/cmTC_ec5b1.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_ec5b1.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_ec5b1.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_ec5b1.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RWh8Xn/CheckIncludeFile.c
        Linking C executable cmTC_ec5b1
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ec5b1.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_ec5b1.dir/CheckIncludeFile.c.o -o cmTC_ec5b1
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for sys/mount.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sOEGcf"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sOEGcf"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_MOUNT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sOEGcf'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_64c38/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_64c38.dir/build.make CMakeFiles/cmTC_64c38.dir/build
        Building C object CMakeFiles/cmTC_64c38.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_64c38.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_64c38.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_64c38.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sOEGcf/CheckIncludeFile.c
        Linking C executable cmTC_64c38
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_64c38.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_64c38.dir/CheckIncludeFile.c.o -o cmTC_64c38
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for time.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-SAUogb"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-SAUogb"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_TIME_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-SAUogb'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_ddc77/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_ddc77.dir/build.make CMakeFiles/cmTC_ddc77.dir/build
        Building C object CMakeFiles/cmTC_ddc77.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_ddc77.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_ddc77.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_ddc77.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-SAUogb/CheckIncludeFile.c
        Linking C executable cmTC_ddc77
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ddc77.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_ddc77.dir/CheckIncludeFile.c.o -o cmTC_ddc77
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "cmake/ConfigureChecks.cmake:30 (check_include_file)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for wchar.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-KokxfD"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-KokxfD"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_WCHAR_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-KokxfD'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_74d58/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_74d58.dir/build.make CMakeFiles/cmTC_74d58.dir/build
        Building C object CMakeFiles/cmTC_74d58.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_74d58.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_74d58.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_74d58.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-KokxfD/CheckIncludeFile.c
        Linking C executable cmTC_74d58
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_74d58.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_74d58.dir/CheckIncludeFile.c.o -o cmTC_74d58
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for link"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-aINxbW"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-aINxbW"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LINK"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-aINxbW'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_f6179/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_f6179.dir/build.make CMakeFiles/cmTC_f6179.dir/build
        Building C object CMakeFiles/cmTC_f6179.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=link -std=gnu11 -MD -MT CMakeFiles/cmTC_f6179.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_f6179.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_f6179.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-aINxbW/CheckFunctionExists.c
        Linking C executable cmTC_f6179
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f6179.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=link -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_f6179.dir/CheckFunctionExists.c.o -o cmTC_f6179
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for mkstemp"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-z3kt1C"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-z3kt1C"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MKSTEMP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-z3kt1C'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_aab81/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_aab81.dir/build.make CMakeFiles/cmTC_aab81.dir/build
        Building C object CMakeFiles/cmTC_aab81.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=mkstemp -std=gnu11 -MD -MT CMakeFiles/cmTC_aab81.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_aab81.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_aab81.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-z3kt1C/CheckFunctionExists.c
        Linking C executable cmTC_aab81
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_aab81.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=mkstemp -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_aab81.dir/CheckFunctionExists.c.o -o cmTC_aab81
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for mkostemp"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-UuC2ws"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-UuC2ws"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MKOSTEMP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-UuC2ws'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_64331/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_64331.dir/build.make CMakeFiles/cmTC_64331.dir/build
        Building C object CMakeFiles/cmTC_64331.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=mkostemp -std=gnu11 -MD -MT CMakeFiles/cmTC_64331.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_64331.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_64331.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-UuC2ws/CheckFunctionExists.c
        Linking C executable cmTC_64331
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_64331.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=mkostemp -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_64331.dir/CheckFunctionExists.c.o -o cmTC_64331
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for _mktemp_s"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-2CQV1a"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-2CQV1a"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE__MKTEMP_S"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-2CQV1a'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_0794f/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_0794f.dir/build.make CMakeFiles/cmTC_0794f.dir/build
        Building C object CMakeFiles/cmTC_0794f.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=_mktemp_s -std=gnu11 -MD -MT CMakeFiles/cmTC_0794f.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_0794f.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_0794f.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-2CQV1a/CheckFunctionExists.c
        Linking C executable cmTC_0794f
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0794f.dir/link.txt --verbose=1
        Undefined symbols for architecture x86_64:
          "__mktemp_s", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=_mktemp_s -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_0794f.dir/CheckFunctionExists.c.o -o cmTC_0794f
        make[1]: *** [cmTC_0794f] Error 1
        make: *** [cmTC_0794f/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for mkdtemp"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-NYu3mV"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-NYu3mV"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MKDTEMP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-NYu3mV'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_38785/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_38785.dir/build.make CMakeFiles/cmTC_38785.dir/build
        Building C object CMakeFiles/cmTC_38785.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=mkdtemp -std=gnu11 -MD -MT CMakeFiles/cmTC_38785.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_38785.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_38785.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-NYu3mV/CheckFunctionExists.c
        Linking C executable cmTC_38785
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_38785.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=mkdtemp -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_38785.dir/CheckFunctionExists.c.o -o cmTC_38785
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for getopt"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-W8EdPJ"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-W8EdPJ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETOPT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-W8EdPJ'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b7808/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_b7808.dir/build.make CMakeFiles/cmTC_b7808.dir/build
        Building C object CMakeFiles/cmTC_b7808.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=getopt -std=gnu11 -MD -MT CMakeFiles/cmTC_b7808.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_b7808.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_b7808.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-W8EdPJ/CheckFunctionExists.c
        Linking C executable cmTC_b7808
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b7808.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=getopt -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_b7808.dir/CheckFunctionExists.c.o -o cmTC_b7808
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for getopt_long"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-uCl4fY"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-uCl4fY"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETOPT_LONG"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-uCl4fY'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_c8958/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_c8958.dir/build.make CMakeFiles/cmTC_c8958.dir/build
        Building C object CMakeFiles/cmTC_c8958.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=getopt_long -std=gnu11 -MD -MT CMakeFiles/cmTC_c8958.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_c8958.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_c8958.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-uCl4fY/CheckFunctionExists.c
        Linking C executable cmTC_c8958
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c8958.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=getopt_long -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_c8958.dir/CheckFunctionExists.c.o -o cmTC_c8958
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for getprogname"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0dpIu0"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0dpIu0"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPROGNAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0dpIu0'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_bacc5/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_bacc5.dir/build.make CMakeFiles/cmTC_bacc5.dir/build
        Building C object CMakeFiles/cmTC_bacc5.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=getprogname -std=gnu11 -MD -MT CMakeFiles/cmTC_bacc5.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_bacc5.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_bacc5.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0dpIu0/CheckFunctionExists.c
        Linking C executable cmTC_bacc5
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_bacc5.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=getprogname -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_bacc5.dir/CheckFunctionExists.c.o -o cmTC_bacc5
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for getexecname"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-gKkjWV"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-gKkjWV"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETEXECNAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-gKkjWV'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_41956/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_41956.dir/build.make CMakeFiles/cmTC_41956.dir/build
        Building C object CMakeFiles/cmTC_41956.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=getexecname -std=gnu11 -MD -MT CMakeFiles/cmTC_41956.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_41956.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_41956.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-gKkjWV/CheckFunctionExists.c
        Linking C executable cmTC_41956
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_41956.dir/link.txt --verbose=1
        Undefined symbols for architecture x86_64:
          "_getexecname", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=getexecname -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_41956.dir/CheckFunctionExists.c.o -o cmTC_41956
        make[1]: *** [cmTC_41956] Error 1
        make: *** [cmTC_41956/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for rand"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-uiXJKB"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-uiXJKB"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RAND"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-uiXJKB'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_df8b7/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_df8b7.dir/build.make CMakeFiles/cmTC_df8b7.dir/build
        Building C object CMakeFiles/cmTC_df8b7.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=rand -std=gnu11 -MD -MT CMakeFiles/cmTC_df8b7.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_df8b7.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_df8b7.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-uiXJKB/CheckFunctionExists.c
        Linking C executable cmTC_df8b7
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_df8b7.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=rand -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_df8b7.dir/CheckFunctionExists.c.o -o cmTC_df8b7
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for random"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-byxVch"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-byxVch"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RANDOM"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-byxVch'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_002e2/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_002e2.dir/build.make CMakeFiles/cmTC_002e2.dir/build
        Building C object CMakeFiles/cmTC_002e2.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=random -std=gnu11 -MD -MT CMakeFiles/cmTC_002e2.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_002e2.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_002e2.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-byxVch/CheckFunctionExists.c
        Linking C executable cmTC_002e2
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_002e2.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=random -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_002e2.dir/CheckFunctionExists.c.o -o cmTC_002e2
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for lrand48"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-zLXAKv"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-zLXAKv"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LRAND48"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-zLXAKv'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_66b20/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_66b20.dir/build.make CMakeFiles/cmTC_66b20.dir/build
        Building C object CMakeFiles/cmTC_66b20.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=lrand48 -std=gnu11 -MD -MT CMakeFiles/cmTC_66b20.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_66b20.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_66b20.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-zLXAKv/CheckFunctionExists.c
        Linking C executable cmTC_66b20
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_66b20.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=lrand48 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_66b20.dir/CheckFunctionExists.c.o -o cmTC_66b20
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for random_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-BSGkSV"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-BSGkSV"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RANDOM_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-BSGkSV'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_98c6d/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_98c6d.dir/build.make CMakeFiles/cmTC_98c6d.dir/build
        Building C object CMakeFiles/cmTC_98c6d.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=random_r -std=gnu11 -MD -MT CMakeFiles/cmTC_98c6d.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_98c6d.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_98c6d.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-BSGkSV/CheckFunctionExists.c
        Linking C executable cmTC_98c6d
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_98c6d.dir/link.txt --verbose=1
        Undefined symbols for architecture x86_64:
          "_random_r", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=random_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_98c6d.dir/CheckFunctionExists.c.o -o cmTC_98c6d
        make[1]: *** [cmTC_98c6d] Error 1
        make: *** [cmTC_98c6d/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for rand_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-lZj5CH"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-lZj5CH"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_RAND_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-lZj5CH'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_844ca/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_844ca.dir/build.make CMakeFiles/cmTC_844ca.dir/build
        Building C object CMakeFiles/cmTC_844ca.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=rand_r -std=gnu11 -MD -MT CMakeFiles/cmTC_844ca.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_844ca.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_844ca.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-lZj5CH/CheckFunctionExists.c
        Linking C executable cmTC_844ca
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_844ca.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=rand_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_844ca.dir/CheckFunctionExists.c.o -o cmTC_844ca
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for readlink"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-AvUT1S"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-AvUT1S"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_READLINK"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-AvUT1S'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_889e2/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_889e2.dir/build.make CMakeFiles/cmTC_889e2.dir/build
        Building C object CMakeFiles/cmTC_889e2.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=readlink -std=gnu11 -MD -MT CMakeFiles/cmTC_889e2.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_889e2.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_889e2.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-AvUT1S/CheckFunctionExists.c
        Linking C executable cmTC_889e2
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_889e2.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=readlink -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_889e2.dir/CheckFunctionExists.c.o -o cmTC_889e2
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for fstatvfs"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-f27N4L"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-f27N4L"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FSTATVFS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-f27N4L'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_ed244/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_ed244.dir/build.make CMakeFiles/cmTC_ed244.dir/build
        Building C object CMakeFiles/cmTC_ed244.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=fstatvfs -std=gnu11 -MD -MT CMakeFiles/cmTC_ed244.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_ed244.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_ed244.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-f27N4L/CheckFunctionExists.c
        Linking C executable cmTC_ed244
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ed244.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=fstatvfs -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_ed244.dir/CheckFunctionExists.c.o -o cmTC_ed244
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for fstatfs"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-5hhMFP"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-5hhMFP"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FSTATFS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-5hhMFP'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_4aa7e/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_4aa7e.dir/build.make CMakeFiles/cmTC_4aa7e.dir/build
        Building C object CMakeFiles/cmTC_4aa7e.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=fstatfs -std=gnu11 -MD -MT CMakeFiles/cmTC_4aa7e.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_4aa7e.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_4aa7e.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-5hhMFP/CheckFunctionExists.c
        Linking C executable cmTC_4aa7e
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_4aa7e.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=fstatfs -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_4aa7e.dir/CheckFunctionExists.c.o -o cmTC_4aa7e
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for lstat"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RqOQ5F"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RqOQ5F"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LSTAT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RqOQ5F'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_8e54b/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_8e54b.dir/build.make CMakeFiles/cmTC_8e54b.dir/build
        Building C object CMakeFiles/cmTC_8e54b.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=lstat -std=gnu11 -MD -MT CMakeFiles/cmTC_8e54b.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_8e54b.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_8e54b.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RqOQ5F/CheckFunctionExists.c
        Linking C executable cmTC_8e54b
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8e54b.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=lstat -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_8e54b.dir/CheckFunctionExists.c.o -o cmTC_8e54b
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for strerror"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-qG1cqO"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-qG1cqO"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRERROR"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-qG1cqO'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_7946e/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_7946e.dir/build.make CMakeFiles/cmTC_7946e.dir/build
        Building C object CMakeFiles/cmTC_7946e.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=strerror -std=gnu11 -MD -MT CMakeFiles/cmTC_7946e.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_7946e.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_7946e.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-qG1cqO/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-qG1cqO/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'strerror' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS strerror
                                      ^
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-qG1cqO/CheckFunctionExists.c:7:3: note: 'strerror' is a builtin with type 'char *(int)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS strerror
                                      ^
        1 warning generated.
        Linking C executable cmTC_7946e
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7946e.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=strerror -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_7946e.dir/CheckFunctionExists.c.o -o cmTC_7946e
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for strerror_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-J29oYL"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-J29oYL"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRERROR_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-J29oYL'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_495b7/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_495b7.dir/build.make CMakeFiles/cmTC_495b7.dir/build
        Building C object CMakeFiles/cmTC_495b7.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=strerror_r -std=gnu11 -MD -MT CMakeFiles/cmTC_495b7.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_495b7.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_495b7.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-J29oYL/CheckFunctionExists.c
        Linking C executable cmTC_495b7
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_495b7.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=strerror_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_495b7.dir/CheckFunctionExists.c.o -o cmTC_495b7
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for mmap"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MpayHR"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MpayHR"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MMAP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MpayHR'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_c07a5/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_c07a5.dir/build.make CMakeFiles/cmTC_c07a5.dir/build
        Building C object CMakeFiles/cmTC_c07a5.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=mmap -std=gnu11 -MD -MT CMakeFiles/cmTC_c07a5.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_c07a5.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_c07a5.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-MpayHR/CheckFunctionExists.c
        Linking C executable cmTC_c07a5
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c07a5.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=mmap -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_c07a5.dir/CheckFunctionExists.c.o -o cmTC_c07a5
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for vprintf"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-6ntfCd"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-6ntfCd"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_VPRINTF"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-6ntfCd'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_f1d7f/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_f1d7f.dir/build.make CMakeFiles/cmTC_f1d7f.dir/build
        Building C object CMakeFiles/cmTC_f1d7f.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=vprintf -std=gnu11 -MD -MT CMakeFiles/cmTC_f1d7f.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_f1d7f.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_f1d7f.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-6ntfCd/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-6ntfCd/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'vprintf' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vprintf
                                      ^
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-6ntfCd/CheckFunctionExists.c:7:3: note: 'vprintf' is a builtin with type 'int (const char *, struct __va_list_tag *)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vprintf
                                      ^
        1 warning generated.
        Linking C executable cmTC_f1d7f
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f1d7f.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=vprintf -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_f1d7f.dir/CheckFunctionExists.c.o -o cmTC_f1d7f
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for vsnprintf"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-swDkoN"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-swDkoN"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_VSNPRINTF"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-swDkoN'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_dd888/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_dd888.dir/build.make CMakeFiles/cmTC_dd888.dir/build
        Building C object CMakeFiles/cmTC_dd888.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=vsnprintf -std=gnu11 -MD -MT CMakeFiles/cmTC_dd888.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_dd888.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_dd888.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-swDkoN/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-swDkoN/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'vsnprintf' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsnprintf
                                      ^
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-swDkoN/CheckFunctionExists.c:7:3: note: 'vsnprintf' is a builtin with type 'int (char *, unsigned long, const char *, struct __va_list_tag *)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsnprintf
                                      ^
        1 warning generated.
        Linking C executable cmTC_dd888
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_dd888.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=vsnprintf -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_dd888.dir/CheckFunctionExists.c.o -o cmTC_dd888
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for vsprintf"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ug03Gw"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ug03Gw"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_VSPRINTF"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ug03Gw'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_fc3c7/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_fc3c7.dir/build.make CMakeFiles/cmTC_fc3c7.dir/build
        Building C object CMakeFiles/cmTC_fc3c7.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=vsprintf -std=gnu11 -MD -MT CMakeFiles/cmTC_fc3c7.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_fc3c7.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_fc3c7.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ug03Gw/CheckFunctionExists.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ug03Gw/CheckFunctionExists.c:7:3: warning: incompatible redeclaration of library function 'vsprintf' [-Wincompatible-library-redeclaration]
          CHECK_FUNCTION_EXISTS(void);
          ^
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsprintf
                                      ^
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ug03Gw/CheckFunctionExists.c:7:3: note: 'vsprintf' is a builtin with type 'int (char *, const char *, struct __va_list_tag *)'
        <command line>:1:31: note: expanded from here
        #define CHECK_FUNCTION_EXISTS vsprintf
                                      ^
        1 warning generated.
        Linking C executable cmTC_fc3c7
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_fc3c7.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=vsprintf -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_fc3c7.dir/CheckFunctionExists.c.o -o cmTC_fc3c7
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for getpagesize"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-V3UQQY"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-V3UQQY"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPAGESIZE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-V3UQQY'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_9c370/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_9c370.dir/build.make CMakeFiles/cmTC_9c370.dir/build
        Building C object CMakeFiles/cmTC_9c370.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=getpagesize -std=gnu11 -MD -MT CMakeFiles/cmTC_9c370.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_9c370.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_9c370.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-V3UQQY/CheckFunctionExists.c
        Linking C executable cmTC_9c370
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_9c370.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=getpagesize -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_9c370.dir/CheckFunctionExists.c.o -o cmTC_9c370
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for getpid"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RFWfS0"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RFWfS0"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPID"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RFWfS0'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_2f3ed/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_2f3ed.dir/build.make CMakeFiles/cmTC_2f3ed.dir/build
        Building C object CMakeFiles/cmTC_2f3ed.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=getpid -std=gnu11 -MD -MT CMakeFiles/cmTC_2f3ed.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_2f3ed.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_2f3ed.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-RFWfS0/CheckFunctionExists.c
        Linking C executable cmTC_2f3ed
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2f3ed.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=getpid -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_2f3ed.dir/CheckFunctionExists.c.o -o cmTC_2f3ed
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for dcgettext"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-iQ5HqQ"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-iQ5HqQ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_DCGETTEXT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-iQ5HqQ'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_3006c/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_3006c.dir/build.make CMakeFiles/cmTC_3006c.dir/build
        Building C object CMakeFiles/cmTC_3006c.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=dcgettext -std=gnu11 -MD -MT CMakeFiles/cmTC_3006c.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_3006c.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_3006c.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-iQ5HqQ/CheckFunctionExists.c
        Linking C executable cmTC_3006c
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_3006c.dir/link.txt --verbose=1
        Undefined symbols for architecture x86_64:
          "_dcgettext", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=dcgettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_3006c.dir/CheckFunctionExists.c.o -o cmTC_3006c
        make[1]: *** [cmTC_3006c] Error 1
        make: *** [cmTC_3006c/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for gettext"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-LfzT0C"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-LfzT0C"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETTEXT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-LfzT0C'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_c5fea/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_c5fea.dir/build.make CMakeFiles/cmTC_c5fea.dir/build
        Building C object CMakeFiles/cmTC_c5fea.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=gettext -std=gnu11 -MD -MT CMakeFiles/cmTC_c5fea.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_c5fea.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_c5fea.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-LfzT0C/CheckFunctionExists.c
        Linking C executable cmTC_c5fea
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c5fea.dir/link.txt --verbose=1
        Undefined symbols for architecture x86_64:
          "_gettext", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=gettext -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_c5fea.dir/CheckFunctionExists.c.o -o cmTC_c5fea
        make[1]: *** [cmTC_c5fea] Error 1
        make: *** [cmTC_c5fea/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:68 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for localtime_r"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0KSlGN"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0KSlGN"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_LOCALTIME_R"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0KSlGN'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_05e4f/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_05e4f.dir/build.make CMakeFiles/cmTC_05e4f.dir/build
        Building C object CMakeFiles/cmTC_05e4f.dir/CheckFunctionExists.c.o
        /usr/bin/cc   -DCHECK_FUNCTION_EXISTS=localtime_r -std=gnu11 -MD -MT CMakeFiles/cmTC_05e4f.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_05e4f.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_05e4f.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0KSlGN/CheckFunctionExists.c
        Linking C executable cmTC_05e4f
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_05e4f.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=localtime_r -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_05e4f.dir/CheckFunctionExists.c.o -o cmTC_05e4f
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:76 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for FT_Get_BDF_Property"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-YoK8xR"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-YoK8xR"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_GET_BDF_PROPERTY"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-YoK8xR'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_2bb67/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_2bb67.dir/build.make CMakeFiles/cmTC_2bb67.dir/build
        Building C object CMakeFiles/cmTC_2bb67.dir/CheckFunctionExists.c.o
        /usr/bin/cc  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Get_BDF_Property -std=gnu11 -MD -MT CMakeFiles/cmTC_2bb67.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_2bb67.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_2bb67.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-YoK8xR/CheckFunctionExists.c
        Linking C executable cmTC_2bb67
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2bb67.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=FT_Get_BDF_Property -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_2bb67.dir/CheckFunctionExists.c.o -o cmTC_2bb67  -lfreetype
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:77 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for FT_Get_PS_Font_Info"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-s9ssO6"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-s9ssO6"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_GET_PS_FONT_INFO"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-s9ssO6'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_37b65/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_37b65.dir/build.make CMakeFiles/cmTC_37b65.dir/build
        Building C object CMakeFiles/cmTC_37b65.dir/CheckFunctionExists.c.o
        /usr/bin/cc  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Get_PS_Font_Info -std=gnu11 -MD -MT CMakeFiles/cmTC_37b65.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_37b65.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_37b65.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-s9ssO6/CheckFunctionExists.c
        Linking C executable cmTC_37b65
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_37b65.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=FT_Get_PS_Font_Info -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_37b65.dir/CheckFunctionExists.c.o -o cmTC_37b65  -lfreetype
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:78 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for FT_Has_PS_Glyph_Names"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Agrfe6"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Agrfe6"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_HAS_PS_GLYPH_NAMES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Agrfe6'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_56cc1/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_56cc1.dir/build.make CMakeFiles/cmTC_56cc1.dir/build
        Building C object CMakeFiles/cmTC_56cc1.dir/CheckFunctionExists.c.o
        /usr/bin/cc  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Has_PS_Glyph_Names -std=gnu11 -MD -MT CMakeFiles/cmTC_56cc1.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_56cc1.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_56cc1.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Agrfe6/CheckFunctionExists.c
        Linking C executable cmTC_56cc1
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_56cc1.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=FT_Has_PS_Glyph_Names -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_56cc1.dir/CheckFunctionExists.c.o -o cmTC_56cc1  -lfreetype
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:79 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for FT_Get_X11_Font_Format"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-UjDq6M"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-UjDq6M"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_GET_X11_FONT_FORMAT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-UjDq6M'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_f22db/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_f22db.dir/build.make CMakeFiles/cmTC_f22db.dir/build
        Building C object CMakeFiles/cmTC_f22db.dir/CheckFunctionExists.c.o
        /usr/bin/cc  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Get_X11_Font_Format -std=gnu11 -MD -MT CMakeFiles/cmTC_f22db.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_f22db.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_f22db.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-UjDq6M/CheckFunctionExists.c
        Linking C executable cmTC_f22db
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f22db.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=FT_Get_X11_Font_Format -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_f22db.dir/CheckFunctionExists.c.o -o cmTC_f22db  -lfreetype
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "cmake/ConfigureChecks.cmake:80 (check_function_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for FT_Done_MM_Var"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-SHzhMp"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-SHzhMp"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FT_DONE_MM_VAR"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-SHzhMp'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_4ff55/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_4ff55.dir/build.make CMakeFiles/cmTC_4ff55.dir/build
        Building C object CMakeFiles/cmTC_4ff55.dir/CheckFunctionExists.c.o
        /usr/bin/cc  -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -DCHECK_FUNCTION_EXISTS=FT_Done_MM_Var -std=gnu11 -MD -MT CMakeFiles/cmTC_4ff55.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_4ff55.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_4ff55.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-SHzhMp/CheckFunctionExists.c
        Linking C executable cmTC_4ff55
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_4ff55.dir/link.txt --verbose=1
        /usr/bin/cc  -DCHECK_FUNCTION_EXISTS=FT_Done_MM_Var -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_4ff55.dir/CheckFunctionExists.c.o -o cmTC_4ff55  -lfreetype
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "cmake/ConfigureChecks.cmake:87 (check_symbol_exists)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for posix_fadvise"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-y0hC2Q"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-y0hC2Q"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_POSIX_FADVISE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-y0hC2Q'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_cc874/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_cc874.dir/build.make CMakeFiles/cmTC_cc874.dir/build
        Building C object CMakeFiles/cmTC_cc874.dir/CheckSymbolExists.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_cc874.dir/CheckSymbolExists.c.o -MF CMakeFiles/cmTC_cc874.dir/CheckSymbolExists.c.o.d -o CMakeFiles/cmTC_cc874.dir/CheckSymbolExists.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-y0hC2Q/CheckSymbolExists.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-y0hC2Q/CheckSymbolExists.c:8:19: error: use of undeclared identifier 'posix_fadvise'
          return ((int*)(&posix_fadvise))[argc];
                          ^
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_cc874.dir/CheckSymbolExists.c.o] Error 1
        make: *** [cmTC_cc874/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:90 (check_struct_has_member)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATVFS_F_BASETYPE"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0aF0aw"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0aF0aw"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATVFS_F_BASETYPE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0aF0aw'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_1b54f/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_1b54f.dir/build.make CMakeFiles/cmTC_1b54f.dir/build
        Building C object CMakeFiles/cmTC_1b54f.dir/src.c.o
        /usr/bin/cc -DHAVE_STRUCT_STATVFS_F_BASETYPE  -std=gnu11 -MD -MT CMakeFiles/cmTC_1b54f.dir/src.c.o -MF CMakeFiles/cmTC_1b54f.dir/src.c.o.d -o CMakeFiles/cmTC_1b54f.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0aF0aw/src.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-0aF0aw/src.c:6:39: error: no member named 'f_basetype' in 'struct statvfs'
          (void)sizeof(((struct statvfs *)0)->f_basetype);
                       ~~~~~~~~~~~~~~~~~~~~~  ^
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_1b54f.dir/src.c.o] Error 1
        make: *** [cmTC_1b54f/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:91 (check_struct_has_member)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATVFS_F_FSTYPENAME"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-zPmnlA"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-zPmnlA"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATVFS_F_FSTYPENAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-zPmnlA'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_03ebf/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_03ebf.dir/build.make CMakeFiles/cmTC_03ebf.dir/build
        Building C object CMakeFiles/cmTC_03ebf.dir/src.c.o
        /usr/bin/cc -DHAVE_STRUCT_STATVFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_03ebf.dir/src.c.o -MF CMakeFiles/cmTC_03ebf.dir/src.c.o.d -o CMakeFiles/cmTC_03ebf.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-zPmnlA/src.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-zPmnlA/src.c:6:39: error: no member named 'f_fstypename' in 'struct statvfs'
          (void)sizeof(((struct statvfs *)0)->f_fstypename);
                       ~~~~~~~~~~~~~~~~~~~~~  ^
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_03ebf.dir/src.c.o] Error 1
        make: *** [cmTC_03ebf/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:92 (check_struct_has_member)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATFS_F_FLAGS"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-oKmy45"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-oKmy45"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATFS_F_FLAGS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-oKmy45'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_37cb5/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_37cb5.dir/build.make CMakeFiles/cmTC_37cb5.dir/build
        Building C object CMakeFiles/cmTC_37cb5.dir/src.c.o
        /usr/bin/cc -DHAVE_STRUCT_STATFS_F_FLAGS  -std=gnu11 -MD -MT CMakeFiles/cmTC_37cb5.dir/src.c.o -MF CMakeFiles/cmTC_37cb5.dir/src.c.o.d -o CMakeFiles/cmTC_37cb5.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-oKmy45/src.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-oKmy45/src.c:2:10: fatal error: 'sys/vfs.h' file not found
        #include <sys/vfs.h>
                 ^~~~~~~~~~~
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_37cb5.dir/src.c.o] Error 1
        make: *** [cmTC_37cb5/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:93 (check_struct_has_member)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STATFS_F_FSTYPENAME"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-dfSi10"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-dfSi10"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STATFS_F_FSTYPENAME"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-dfSi10'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_65673/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_65673.dir/build.make CMakeFiles/cmTC_65673.dir/build
        Building C object CMakeFiles/cmTC_65673.dir/src.c.o
        /usr/bin/cc -DHAVE_STRUCT_STATFS_F_FSTYPENAME  -std=gnu11 -MD -MT CMakeFiles/cmTC_65673.dir/src.c.o -MF CMakeFiles/cmTC_65673.dir/src.c.o.d -o CMakeFiles/cmTC_65673.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-dfSi10/src.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-dfSi10/src.c:2:10: fatal error: 'sys/vfs.h' file not found
        #include <sys/vfs.h>
                 ^~~~~~~~~~~
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_65673.dir/src.c.o] Error 1
        make: *** [cmTC_65673/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:94 (check_struct_has_member)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_STAT_ST_MTIM"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ukRvyp"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ukRvyp"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_STAT_ST_MTIM"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ukRvyp'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_3ec1d/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_3ec1d.dir/build.make CMakeFiles/cmTC_3ec1d.dir/build
        Building C object CMakeFiles/cmTC_3ec1d.dir/src.c.o
        /usr/bin/cc -DHAVE_STRUCT_STAT_ST_MTIM  -std=gnu11 -MD -MT CMakeFiles/cmTC_3ec1d.dir/src.c.o -MF CMakeFiles/cmTC_3ec1d.dir/src.c.o.d -o CMakeFiles/cmTC_3ec1d.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ukRvyp/src.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-ukRvyp/src.c:6:36: error: no member named 'st_mtim' in 'struct stat'
          (void)sizeof(((struct stat *)0)->st_mtim);
                       ~~~~~~~~~~~~~~~~~~  ^
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_3ec1d.dir/src.c.o] Error 1
        make: *** [cmTC_3ec1d/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSourceCompiles.cmake:89 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/CheckStructHasMember.cmake:85 (check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:95 (check_struct_has_member)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_STRUCT_DIRENT_D_TYPE"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-XnwEM5"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-XnwEM5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STRUCT_DIRENT_D_TYPE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-XnwEM5'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_7873e/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_7873e.dir/build.make CMakeFiles/cmTC_7873e.dir/build
        Building C object CMakeFiles/cmTC_7873e.dir/src.c.o
        /usr/bin/cc -DHAVE_STRUCT_DIRENT_D_TYPE  -std=gnu11 -MD -MT CMakeFiles/cmTC_7873e.dir/src.c.o -MF CMakeFiles/cmTC_7873e.dir/src.c.o.d -o CMakeFiles/cmTC_7873e.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-XnwEM5/src.c
        Linking C executable cmTC_7873e
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7873e.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_7873e.dir/src.c.o -o cmTC_7873e
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:320 (check_include_file)"
      - "cmake/ConfigureChecks.cmake:98 (check_type_size)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Looking for stddef.h"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sEVPO4"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sEVPO4"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDDEF_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sEVPO4'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_05e22/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_05e22.dir/build.make CMakeFiles/cmTC_05e22.dir/build
        Building C object CMakeFiles/cmTC_05e22.dir/CheckIncludeFile.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_05e22.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_05e22.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_05e22.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sEVPO4/CheckIncludeFile.c
        Linking C executable cmTC_05e22
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_05e22.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_05e22.dir/CheckIncludeFile.c.o -o cmTC_05e22
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:98 (check_type_size)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Check size of void *"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-lo9YUG"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-lo9YUG"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SIZEOF_VOID_P"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-lo9YUG'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_bf98b/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_bf98b.dir/build.make CMakeFiles/cmTC_bf98b.dir/build
        Building C object CMakeFiles/cmTC_bf98b.dir/SIZEOF_VOID_P.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_bf98b.dir/SIZEOF_VOID_P.c.o -MF CMakeFiles/cmTC_bf98b.dir/SIZEOF_VOID_P.c.o.d -o CMakeFiles/cmTC_bf98b.dir/SIZEOF_VOID_P.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-lo9YUG/SIZEOF_VOID_P.c
        Linking C executable cmTC_bf98b
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_bf98b.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_bf98b.dir/SIZEOF_VOID_P.c.o -o cmTC_bf98b
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:106 (check_c_source_compiles)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_FLEXIBLE_ARRAY_MEMBER"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-7kcVWT"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-7kcVWT"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FLEXIBLE_ARRAY_MEMBER"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-7kcVWT'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_d7c25/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_d7c25.dir/build.make CMakeFiles/cmTC_d7c25.dir/build
        Building C object CMakeFiles/cmTC_d7c25.dir/src.c.o
        /usr/bin/cc -DHAVE_FLEXIBLE_ARRAY_MEMBER  -std=gnu11 -MD -MT CMakeFiles/cmTC_d7c25.dir/src.c.o -MF CMakeFiles/cmTC_d7c25.dir/src.c.o.d -o CMakeFiles/cmTC_d7c25.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-7kcVWT/src.c
        Linking C executable cmTC_d7c25
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d7c25.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_d7c25.dir/src.c.o -o cmTC_d7c25
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:122 (check_c_source_compiles)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_PTHREAD_PRIO_INHERIT"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-3uNDBt"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-3uNDBt"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_PTHREAD_PRIO_INHERIT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-3uNDBt'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_7e821/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_7e821.dir/build.make CMakeFiles/cmTC_7e821.dir/build
        Building C object CMakeFiles/cmTC_7e821.dir/src.c.o
        /usr/bin/cc -DHAVE_PTHREAD_PRIO_INHERIT  -std=gnu11 -MD -MT CMakeFiles/cmTC_7e821.dir/src.c.o -MF CMakeFiles/cmTC_7e821.dir/src.c.o.d -o CMakeFiles/cmTC_7e821.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-3uNDBt/src.c
        Linking C executable cmTC_7e821
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7e821.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_7e821.dir/src.c.o -o cmTC_7e821
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:134 (check_c_source_compiles)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_STDATOMIC_PRIMITIVES"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sxUIKw"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sxUIKw"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDATOMIC_PRIMITIVES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sxUIKw'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_5e39e/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_5e39e.dir/build.make CMakeFiles/cmTC_5e39e.dir/build
        Building C object CMakeFiles/cmTC_5e39e.dir/src.c.o
        /usr/bin/cc -DHAVE_STDATOMIC_PRIMITIVES  -std=gnu11 -MD -MT CMakeFiles/cmTC_5e39e.dir/src.c.o -MF CMakeFiles/cmTC_5e39e.dir/src.c.o.d -o CMakeFiles/cmTC_5e39e.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sxUIKw/src.c
        Linking C executable cmTC_5e39e
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5e39e.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_5e39e.dir/src.c.o -o cmTC_5e39e
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:143 (check_c_source_compiles)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_INTEL_ATOMIC_PRIMITIVES"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-LW8Vi1"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-LW8Vi1"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INTEL_ATOMIC_PRIMITIVES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-LW8Vi1'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_05144/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_05144.dir/build.make CMakeFiles/cmTC_05144.dir/build
        Building C object CMakeFiles/cmTC_05144.dir/src.c.o
        /usr/bin/cc -DHAVE_INTEL_ATOMIC_PRIMITIVES  -std=gnu11 -MD -MT CMakeFiles/cmTC_05144.dir/src.c.o -MF CMakeFiles/cmTC_05144.dir/src.c.o.d -o CMakeFiles/cmTC_05144.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-LW8Vi1/src.c
        Linking C executable cmTC_05144
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_05144.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_05144.dir/src.c.o -o cmTC_05144
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:151 (check_c_source_compiles)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test HAVE_SOLARIS_ATOMIC_OPS"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-hrvkZs"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-hrvkZs"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SOLARIS_ATOMIC_OPS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-hrvkZs'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_39eb3/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_39eb3.dir/build.make CMakeFiles/cmTC_39eb3.dir/build
        Building C object CMakeFiles/cmTC_39eb3.dir/src.c.o
        /usr/bin/cc -DHAVE_SOLARIS_ATOMIC_OPS  -std=gnu11 -MD -MT CMakeFiles/cmTC_39eb3.dir/src.c.o -MF CMakeFiles/cmTC_39eb3.dir/src.c.o.d -o CMakeFiles/cmTC_39eb3.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-hrvkZs/src.c
        /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-hrvkZs/src.c:2:10: fatal error: 'atomic.h' file not found
        #include <atomic.h>
                 ^~~~~~~~~~
        1 error generated.
        make[1]: *** [CMakeFiles/cmTC_39eb3.dir/src.c.o] Error 1
        make: *** [cmTC_39eb3/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "cmake/ConfigureChecks.cmake:165 (check_c_source_compiles)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Performing Test FREETYPE_PCF_LONG_FAMILY_NAMES"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-NXc77D"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-NXc77D"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "FREETYPE_PCF_LONG_FAMILY_NAMES"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-NXc77D'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_1f543/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_1f543.dir/build.make CMakeFiles/cmTC_1f543.dir/build
        Building C object CMakeFiles/cmTC_1f543.dir/src.c.o
        /usr/bin/cc -DFREETYPE_PCF_LONG_FAMILY_NAMES -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -std=gnu11 -MD -MT CMakeFiles/cmTC_1f543.dir/src.c.o -MF CMakeFiles/cmTC_1f543.dir/src.c.o.d -o CMakeFiles/cmTC_1f543.dir/src.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-NXc77D/src.c
        Linking C executable cmTC_1f543
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_1f543.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_1f543.dir/src.c.o -o cmTC_1f543  -lfreetype
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:203 (check_type_size)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Check size of uint64_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Y0fjUC"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Y0fjUC"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UINT64_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Y0fjUC'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_e3e8a/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_e3e8a.dir/build.make CMakeFiles/cmTC_e3e8a.dir/build
        Building C object CMakeFiles/cmTC_e3e8a.dir/UINT64_T.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_e3e8a.dir/UINT64_T.c.o -MF CMakeFiles/cmTC_e3e8a.dir/UINT64_T.c.o.d -o CMakeFiles/cmTC_e3e8a.dir/UINT64_T.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-Y0fjUC/UINT64_T.c
        Linking C executable cmTC_e3e8a
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e3e8a.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_e3e8a.dir/UINT64_T.c.o -o cmTC_e3e8a
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:204 (check_type_size)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Check size of int32_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-4qqv9y"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-4qqv9y"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INT32_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-4qqv9y'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_ca5cd/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_ca5cd.dir/build.make CMakeFiles/cmTC_ca5cd.dir/build
        Building C object CMakeFiles/cmTC_ca5cd.dir/INT32_T.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_ca5cd.dir/INT32_T.c.o -MF CMakeFiles/cmTC_ca5cd.dir/INT32_T.c.o.d -o CMakeFiles/cmTC_ca5cd.dir/INT32_T.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-4qqv9y/INT32_T.c
        Linking C executable cmTC_ca5cd
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ca5cd.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_ca5cd.dir/INT32_T.c.o -o cmTC_ca5cd
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:205 (check_type_size)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Check size of uintptr_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sDOZic"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sDOZic"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UINTPTR_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sDOZic'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_12b0d/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_12b0d.dir/build.make CMakeFiles/cmTC_12b0d.dir/build
        Building C object CMakeFiles/cmTC_12b0d.dir/UINTPTR_T.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_12b0d.dir/UINTPTR_T.c.o -MF CMakeFiles/cmTC_12b0d.dir/UINTPTR_T.c.o.d -o CMakeFiles/cmTC_12b0d.dir/UINTPTR_T.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-sDOZic/UINTPTR_T.c
        Linking C executable cmTC_12b0d
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_12b0d.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_12b0d.dir/UINTPTR_T.c.o -o cmTC_12b0d
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "cmake/ConfigureChecks.cmake:206 (check_type_size)"
      - "CMakeLists.txt:242 (include)"
    checks:
      - "Check size of intptr_t"
    directories:
      source: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-dsZKdE"
      binary: "/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-dsZKdE"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_INTPTR_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-dsZKdE'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_f6216/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_f6216.dir/build.make CMakeFiles/cmTC_f6216.dir/build
        Building C object CMakeFiles/cmTC_f6216.dir/INTPTR_T.c.o
        /usr/bin/cc   -std=gnu11 -MD -MT CMakeFiles/cmTC_f6216.dir/INTPTR_T.c.o -MF CMakeFiles/cmTC_f6216.dir/INTPTR_T.c.o.d -o CMakeFiles/cmTC_f6216.dir/INTPTR_T.c.o -c /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles/CMakeScratch/TryCompile-dsZKdE/INTPTR_T.c
        Linking C executable cmTC_f6216
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f6216.dir/link.txt --verbose=1
        /usr/bin/cc -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_f6216.dir/INTPTR_T.c.o -o cmTC_f6216
        
      exitCode: 0
...
