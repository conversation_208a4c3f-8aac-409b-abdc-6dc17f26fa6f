# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/work/fontconfig

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/work/fontconfig/build-cmake

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: fontconfig/all
all: fc-case/all
all: fc-lang/all
all: src/all
all: fc-cache/all
all: fc-cat/all
all: fc-conflist/all
all: fc-list/all
all: fc-match/all
all: fc-pattern/all
all: fc-query/all
all: fc-scan/all
all: fc-validate/all
all: test/all
all: conf.d/all
all: its/all
all: doc/all
.PHONY : all

# The main recursive "codegen" target.
codegen: fontconfig/codegen
codegen: fc-case/codegen
codegen: fc-lang/codegen
codegen: src/codegen
codegen: fc-cache/codegen
codegen: fc-cat/codegen
codegen: fc-conflist/codegen
codegen: fc-list/codegen
codegen: fc-match/codegen
codegen: fc-pattern/codegen
codegen: fc-query/codegen
codegen: fc-scan/codegen
codegen: fc-validate/codegen
codegen: test/codegen
codegen: conf.d/codegen
codegen: its/codegen
codegen: doc/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: fontconfig/preinstall
preinstall: fc-case/preinstall
preinstall: fc-lang/preinstall
preinstall: src/preinstall
preinstall: fc-cache/preinstall
preinstall: fc-cat/preinstall
preinstall: fc-conflist/preinstall
preinstall: fc-list/preinstall
preinstall: fc-match/preinstall
preinstall: fc-pattern/preinstall
preinstall: fc-query/preinstall
preinstall: fc-scan/preinstall
preinstall: fc-validate/preinstall
preinstall: test/preinstall
preinstall: conf.d/preinstall
preinstall: its/preinstall
preinstall: doc/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: fontconfig/clean
clean: fc-case/clean
clean: fc-lang/clean
clean: src/clean
clean: fc-cache/clean
clean: fc-cat/clean
clean: fc-conflist/clean
clean: fc-list/clean
clean: fc-match/clean
clean: fc-pattern/clean
clean: fc-query/clean
clean: fc-scan/clean
clean: fc-validate/clean
clean: test/clean
clean: conf.d/clean
clean: its/clean
clean: doc/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory conf.d

# Recursive "all" directory target.
conf.d/all: conf.d/CMakeFiles/lang_normalize_conf.dir/all
.PHONY : conf.d/all

# Recursive "codegen" directory target.
conf.d/codegen: conf.d/CMakeFiles/lang_normalize_conf.dir/codegen
.PHONY : conf.d/codegen

# Recursive "preinstall" directory target.
conf.d/preinstall:
.PHONY : conf.d/preinstall

# Recursive "clean" directory target.
conf.d/clean: conf.d/CMakeFiles/lang_normalize_conf.dir/clean
.PHONY : conf.d/clean

#=============================================================================
# Directory level rules for directory doc

# Recursive "all" directory target.
doc/all:
.PHONY : doc/all

# Recursive "codegen" directory target.
doc/codegen:
.PHONY : doc/codegen

# Recursive "preinstall" directory target.
doc/preinstall:
.PHONY : doc/preinstall

# Recursive "clean" directory target.
doc/clean:
.PHONY : doc/clean

#=============================================================================
# Directory level rules for directory fc-cache

# Recursive "all" directory target.
fc-cache/all: fc-cache/CMakeFiles/fc-cache.dir/all
.PHONY : fc-cache/all

# Recursive "codegen" directory target.
fc-cache/codegen: fc-cache/CMakeFiles/fc-cache.dir/codegen
.PHONY : fc-cache/codegen

# Recursive "preinstall" directory target.
fc-cache/preinstall:
.PHONY : fc-cache/preinstall

# Recursive "clean" directory target.
fc-cache/clean: fc-cache/CMakeFiles/fc-cache.dir/clean
.PHONY : fc-cache/clean

#=============================================================================
# Directory level rules for directory fc-case

# Recursive "all" directory target.
fc-case/all:
.PHONY : fc-case/all

# Recursive "codegen" directory target.
fc-case/codegen:
.PHONY : fc-case/codegen

# Recursive "preinstall" directory target.
fc-case/preinstall:
.PHONY : fc-case/preinstall

# Recursive "clean" directory target.
fc-case/clean: fc-case/CMakeFiles/fccase_h.dir/clean
.PHONY : fc-case/clean

#=============================================================================
# Directory level rules for directory fc-cat

# Recursive "all" directory target.
fc-cat/all: fc-cat/CMakeFiles/fc-cat.dir/all
.PHONY : fc-cat/all

# Recursive "codegen" directory target.
fc-cat/codegen: fc-cat/CMakeFiles/fc-cat.dir/codegen
.PHONY : fc-cat/codegen

# Recursive "preinstall" directory target.
fc-cat/preinstall:
.PHONY : fc-cat/preinstall

# Recursive "clean" directory target.
fc-cat/clean: fc-cat/CMakeFiles/fc-cat.dir/clean
.PHONY : fc-cat/clean

#=============================================================================
# Directory level rules for directory fc-conflist

# Recursive "all" directory target.
fc-conflist/all: fc-conflist/CMakeFiles/fc-conflist.dir/all
.PHONY : fc-conflist/all

# Recursive "codegen" directory target.
fc-conflist/codegen: fc-conflist/CMakeFiles/fc-conflist.dir/codegen
.PHONY : fc-conflist/codegen

# Recursive "preinstall" directory target.
fc-conflist/preinstall:
.PHONY : fc-conflist/preinstall

# Recursive "clean" directory target.
fc-conflist/clean: fc-conflist/CMakeFiles/fc-conflist.dir/clean
.PHONY : fc-conflist/clean

#=============================================================================
# Directory level rules for directory fc-lang

# Recursive "all" directory target.
fc-lang/all:
.PHONY : fc-lang/all

# Recursive "codegen" directory target.
fc-lang/codegen:
.PHONY : fc-lang/codegen

# Recursive "preinstall" directory target.
fc-lang/preinstall:
.PHONY : fc-lang/preinstall

# Recursive "clean" directory target.
fc-lang/clean: fc-lang/CMakeFiles/fclang_h.dir/clean
.PHONY : fc-lang/clean

#=============================================================================
# Directory level rules for directory fc-list

# Recursive "all" directory target.
fc-list/all: fc-list/CMakeFiles/fc-list.dir/all
.PHONY : fc-list/all

# Recursive "codegen" directory target.
fc-list/codegen: fc-list/CMakeFiles/fc-list.dir/codegen
.PHONY : fc-list/codegen

# Recursive "preinstall" directory target.
fc-list/preinstall:
.PHONY : fc-list/preinstall

# Recursive "clean" directory target.
fc-list/clean: fc-list/CMakeFiles/fc-list.dir/clean
.PHONY : fc-list/clean

#=============================================================================
# Directory level rules for directory fc-match

# Recursive "all" directory target.
fc-match/all: fc-match/CMakeFiles/fc-match.dir/all
.PHONY : fc-match/all

# Recursive "codegen" directory target.
fc-match/codegen: fc-match/CMakeFiles/fc-match.dir/codegen
.PHONY : fc-match/codegen

# Recursive "preinstall" directory target.
fc-match/preinstall:
.PHONY : fc-match/preinstall

# Recursive "clean" directory target.
fc-match/clean: fc-match/CMakeFiles/fc-match.dir/clean
.PHONY : fc-match/clean

#=============================================================================
# Directory level rules for directory fc-pattern

# Recursive "all" directory target.
fc-pattern/all: fc-pattern/CMakeFiles/fc-pattern.dir/all
.PHONY : fc-pattern/all

# Recursive "codegen" directory target.
fc-pattern/codegen: fc-pattern/CMakeFiles/fc-pattern.dir/codegen
.PHONY : fc-pattern/codegen

# Recursive "preinstall" directory target.
fc-pattern/preinstall:
.PHONY : fc-pattern/preinstall

# Recursive "clean" directory target.
fc-pattern/clean: fc-pattern/CMakeFiles/fc-pattern.dir/clean
.PHONY : fc-pattern/clean

#=============================================================================
# Directory level rules for directory fc-query

# Recursive "all" directory target.
fc-query/all: fc-query/CMakeFiles/fc-query.dir/all
.PHONY : fc-query/all

# Recursive "codegen" directory target.
fc-query/codegen: fc-query/CMakeFiles/fc-query.dir/codegen
.PHONY : fc-query/codegen

# Recursive "preinstall" directory target.
fc-query/preinstall:
.PHONY : fc-query/preinstall

# Recursive "clean" directory target.
fc-query/clean: fc-query/CMakeFiles/fc-query.dir/clean
.PHONY : fc-query/clean

#=============================================================================
# Directory level rules for directory fc-scan

# Recursive "all" directory target.
fc-scan/all: fc-scan/CMakeFiles/fc-scan.dir/all
.PHONY : fc-scan/all

# Recursive "codegen" directory target.
fc-scan/codegen: fc-scan/CMakeFiles/fc-scan.dir/codegen
.PHONY : fc-scan/codegen

# Recursive "preinstall" directory target.
fc-scan/preinstall:
.PHONY : fc-scan/preinstall

# Recursive "clean" directory target.
fc-scan/clean: fc-scan/CMakeFiles/fc-scan.dir/clean
.PHONY : fc-scan/clean

#=============================================================================
# Directory level rules for directory fc-validate

# Recursive "all" directory target.
fc-validate/all: fc-validate/CMakeFiles/fc-validate.dir/all
.PHONY : fc-validate/all

# Recursive "codegen" directory target.
fc-validate/codegen: fc-validate/CMakeFiles/fc-validate.dir/codegen
.PHONY : fc-validate/codegen

# Recursive "preinstall" directory target.
fc-validate/preinstall:
.PHONY : fc-validate/preinstall

# Recursive "clean" directory target.
fc-validate/clean: fc-validate/CMakeFiles/fc-validate.dir/clean
.PHONY : fc-validate/clean

#=============================================================================
# Directory level rules for directory fontconfig

# Recursive "all" directory target.
fontconfig/all:
.PHONY : fontconfig/all

# Recursive "codegen" directory target.
fontconfig/codegen:
.PHONY : fontconfig/codegen

# Recursive "preinstall" directory target.
fontconfig/preinstall:
.PHONY : fontconfig/preinstall

# Recursive "clean" directory target.
fontconfig/clean:
.PHONY : fontconfig/clean

#=============================================================================
# Directory level rules for directory its

# Recursive "all" directory target.
its/all:
.PHONY : its/all

# Recursive "codegen" directory target.
its/codegen:
.PHONY : its/codegen

# Recursive "preinstall" directory target.
its/preinstall:
.PHONY : its/preinstall

# Recursive "clean" directory target.
its/clean:
.PHONY : its/clean

#=============================================================================
# Directory level rules for directory src

# Recursive "all" directory target.
src/all: src/CMakeFiles/patternlib_internal.dir/all
src/all: src/CMakeFiles/fontconfig.dir/all
src/all: src/CMakeFiles/fontconfig_internal.dir/all
.PHONY : src/all

# Recursive "codegen" directory target.
src/codegen: src/CMakeFiles/patternlib_internal.dir/codegen
src/codegen: src/CMakeFiles/fontconfig.dir/codegen
src/codegen: src/CMakeFiles/fontconfig_internal.dir/codegen
.PHONY : src/codegen

# Recursive "preinstall" directory target.
src/preinstall:
.PHONY : src/preinstall

# Recursive "clean" directory target.
src/clean: src/CMakeFiles/patternlib_internal.dir/clean
src/clean: src/CMakeFiles/generated_headers.dir/clean
src/clean: src/CMakeFiles/fontconfig.dir/clean
src/clean: src/CMakeFiles/fontconfig_internal.dir/clean
.PHONY : src/clean

#=============================================================================
# Directory level rules for directory test

# Recursive "all" directory target.
test/all: test/CMakeFiles/test_bz89617.dir/all
test/all: test/CMakeFiles/test_bz131804.dir/all
test/all: test/CMakeFiles/test_bz96676.dir/all
test/all: test/CMakeFiles/test_name_parse.dir/all
test/all: test/CMakeFiles/test_bz106618.dir/all
test/all: test/CMakeFiles/test_bz1744377.dir/all
test/all: test/CMakeFiles/test_issue180.dir/all
test/all: test/CMakeFiles/test_family_matching.dir/all
test/all: test/CMakeFiles/test_ptrlist.dir/all
test/all: test/CMakeFiles/test_bz106632.dir/all
test/all: test/CMakeFiles/test_issue107.dir/all
test/all: test/CMakeFiles/test_crbug1004254.dir/all
test/all: test/CMakeFiles/test_mt_fccfg.dir/all
.PHONY : test/all

# Recursive "codegen" directory target.
test/codegen: test/CMakeFiles/test_bz89617.dir/codegen
test/codegen: test/CMakeFiles/test_bz131804.dir/codegen
test/codegen: test/CMakeFiles/test_bz96676.dir/codegen
test/codegen: test/CMakeFiles/test_name_parse.dir/codegen
test/codegen: test/CMakeFiles/test_bz106618.dir/codegen
test/codegen: test/CMakeFiles/test_bz1744377.dir/codegen
test/codegen: test/CMakeFiles/test_issue180.dir/codegen
test/codegen: test/CMakeFiles/test_family_matching.dir/codegen
test/codegen: test/CMakeFiles/test_ptrlist.dir/codegen
test/codegen: test/CMakeFiles/test_bz106632.dir/codegen
test/codegen: test/CMakeFiles/test_issue107.dir/codegen
test/codegen: test/CMakeFiles/test_crbug1004254.dir/codegen
test/codegen: test/CMakeFiles/test_mt_fccfg.dir/codegen
.PHONY : test/codegen

# Recursive "preinstall" directory target.
test/preinstall:
.PHONY : test/preinstall

# Recursive "clean" directory target.
test/clean: test/CMakeFiles/fetch_test_fonts.dir/clean
test/clean: test/CMakeFiles/test_bz89617.dir/clean
test/clean: test/CMakeFiles/test_bz131804.dir/clean
test/clean: test/CMakeFiles/test_bz96676.dir/clean
test/clean: test/CMakeFiles/test_name_parse.dir/clean
test/clean: test/CMakeFiles/test_bz106618.dir/clean
test/clean: test/CMakeFiles/test_bz1744377.dir/clean
test/clean: test/CMakeFiles/test_issue180.dir/clean
test/clean: test/CMakeFiles/test_family_matching.dir/clean
test/clean: test/CMakeFiles/test_ptrlist.dir/clean
test/clean: test/CMakeFiles/test_bz106632.dir/clean
test/clean: test/CMakeFiles/test_issue107.dir/clean
test/clean: test/CMakeFiles/test_crbug1004254.dir/clean
test/clean: test/CMakeFiles/test_mt_fccfg.dir/clean
.PHONY : test/clean

#=============================================================================
# Target rules for target fc-case/CMakeFiles/fccase_h.dir

# All Build rule for target.
fc-case/CMakeFiles/fccase_h.dir/all:
	$(MAKE) $(MAKESILENT) -f fc-case/CMakeFiles/fccase_h.dir/build.make fc-case/CMakeFiles/fccase_h.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-case/CMakeFiles/fccase_h.dir/build.make fc-case/CMakeFiles/fccase_h.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=17 "Built target fccase_h"
.PHONY : fc-case/CMakeFiles/fccase_h.dir/all

# Build rule for subdir invocation for target.
fc-case/CMakeFiles/fccase_h.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-case/CMakeFiles/fccase_h.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-case/CMakeFiles/fccase_h.dir/rule

# Convenience name for target.
fccase_h: fc-case/CMakeFiles/fccase_h.dir/rule
.PHONY : fccase_h

# codegen rule for target.
fc-case/CMakeFiles/fccase_h.dir/codegen:
	$(MAKE) $(MAKESILENT) -f fc-case/CMakeFiles/fccase_h.dir/build.make fc-case/CMakeFiles/fccase_h.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=17 "Finished codegen for target fccase_h"
.PHONY : fc-case/CMakeFiles/fccase_h.dir/codegen

# clean rule for target.
fc-case/CMakeFiles/fccase_h.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-case/CMakeFiles/fccase_h.dir/build.make fc-case/CMakeFiles/fccase_h.dir/clean
.PHONY : fc-case/CMakeFiles/fccase_h.dir/clean

#=============================================================================
# Target rules for target fc-lang/CMakeFiles/fclang_h.dir

# All Build rule for target.
fc-lang/CMakeFiles/fclang_h.dir/all:
	$(MAKE) $(MAKESILENT) -f fc-lang/CMakeFiles/fclang_h.dir/build.make fc-lang/CMakeFiles/fclang_h.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-lang/CMakeFiles/fclang_h.dir/build.make fc-lang/CMakeFiles/fclang_h.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=18 "Built target fclang_h"
.PHONY : fc-lang/CMakeFiles/fclang_h.dir/all

# Build rule for subdir invocation for target.
fc-lang/CMakeFiles/fclang_h.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-lang/CMakeFiles/fclang_h.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-lang/CMakeFiles/fclang_h.dir/rule

# Convenience name for target.
fclang_h: fc-lang/CMakeFiles/fclang_h.dir/rule
.PHONY : fclang_h

# codegen rule for target.
fc-lang/CMakeFiles/fclang_h.dir/codegen:
	$(MAKE) $(MAKESILENT) -f fc-lang/CMakeFiles/fclang_h.dir/build.make fc-lang/CMakeFiles/fclang_h.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=18 "Finished codegen for target fclang_h"
.PHONY : fc-lang/CMakeFiles/fclang_h.dir/codegen

# clean rule for target.
fc-lang/CMakeFiles/fclang_h.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-lang/CMakeFiles/fclang_h.dir/build.make fc-lang/CMakeFiles/fclang_h.dir/clean
.PHONY : fc-lang/CMakeFiles/fclang_h.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/patternlib_internal.dir

# All Build rule for target.
src/CMakeFiles/patternlib_internal.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
src/CMakeFiles/patternlib_internal.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
src/CMakeFiles/patternlib_internal.dir/all: src/CMakeFiles/generated_headers.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/patternlib_internal.dir/build.make src/CMakeFiles/patternlib_internal.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/patternlib_internal.dir/build.make src/CMakeFiles/patternlib_internal.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=76 "Built target patternlib_internal"
.PHONY : src/CMakeFiles/patternlib_internal.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/patternlib_internal.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/patternlib_internal.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : src/CMakeFiles/patternlib_internal.dir/rule

# Convenience name for target.
patternlib_internal: src/CMakeFiles/patternlib_internal.dir/rule
.PHONY : patternlib_internal

# codegen rule for target.
src/CMakeFiles/patternlib_internal.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
src/CMakeFiles/patternlib_internal.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
src/CMakeFiles/patternlib_internal.dir/codegen: src/CMakeFiles/generated_headers.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/patternlib_internal.dir/build.make src/CMakeFiles/patternlib_internal.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=76 "Finished codegen for target patternlib_internal"
.PHONY : src/CMakeFiles/patternlib_internal.dir/codegen

# clean rule for target.
src/CMakeFiles/patternlib_internal.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/patternlib_internal.dir/build.make src/CMakeFiles/patternlib_internal.dir/clean
.PHONY : src/CMakeFiles/patternlib_internal.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/generated_headers.dir

# All Build rule for target.
src/CMakeFiles/generated_headers.dir/all:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/generated_headers.dir/build.make src/CMakeFiles/generated_headers.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/generated_headers.dir/build.make src/CMakeFiles/generated_headers.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=71,72,73,74 "Built target generated_headers"
.PHONY : src/CMakeFiles/generated_headers.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/generated_headers.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/generated_headers.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : src/CMakeFiles/generated_headers.dir/rule

# Convenience name for target.
generated_headers: src/CMakeFiles/generated_headers.dir/rule
.PHONY : generated_headers

# codegen rule for target.
src/CMakeFiles/generated_headers.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/generated_headers.dir/build.make src/CMakeFiles/generated_headers.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=71,72,73,74 "Finished codegen for target generated_headers"
.PHONY : src/CMakeFiles/generated_headers.dir/codegen

# clean rule for target.
src/CMakeFiles/generated_headers.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/generated_headers.dir/build.make src/CMakeFiles/generated_headers.dir/clean
.PHONY : src/CMakeFiles/generated_headers.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/fontconfig.dir

# All Build rule for target.
src/CMakeFiles/fontconfig.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
src/CMakeFiles/fontconfig.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
src/CMakeFiles/fontconfig.dir/all: src/CMakeFiles/patternlib_internal.dir/all
src/CMakeFiles/fontconfig.dir/all: src/CMakeFiles/generated_headers.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/fontconfig.dir/build.make src/CMakeFiles/fontconfig.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/fontconfig.dir/build.make src/CMakeFiles/fontconfig.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44 "Built target fontconfig"
.PHONY : src/CMakeFiles/fontconfig.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/fontconfig.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 32
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/fontconfig.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : src/CMakeFiles/fontconfig.dir/rule

# Convenience name for target.
fontconfig: src/CMakeFiles/fontconfig.dir/rule
.PHONY : fontconfig

# codegen rule for target.
src/CMakeFiles/fontconfig.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
src/CMakeFiles/fontconfig.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
src/CMakeFiles/fontconfig.dir/codegen: src/CMakeFiles/generated_headers.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/fontconfig.dir/build.make src/CMakeFiles/fontconfig.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44 "Finished codegen for target fontconfig"
.PHONY : src/CMakeFiles/fontconfig.dir/codegen

# clean rule for target.
src/CMakeFiles/fontconfig.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/fontconfig.dir/build.make src/CMakeFiles/fontconfig.dir/clean
.PHONY : src/CMakeFiles/fontconfig.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/fontconfig_internal.dir

# All Build rule for target.
src/CMakeFiles/fontconfig_internal.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
src/CMakeFiles/fontconfig_internal.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
src/CMakeFiles/fontconfig_internal.dir/all: src/CMakeFiles/patternlib_internal.dir/all
src/CMakeFiles/fontconfig_internal.dir/all: src/CMakeFiles/generated_headers.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/fontconfig_internal.dir/build.make src/CMakeFiles/fontconfig_internal.dir/depend
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/fontconfig_internal.dir/build.make src/CMakeFiles/fontconfig_internal.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70 "Built target fontconfig_internal"
.PHONY : src/CMakeFiles/fontconfig_internal.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/fontconfig_internal.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 33
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/CMakeFiles/fontconfig_internal.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : src/CMakeFiles/fontconfig_internal.dir/rule

# Convenience name for target.
fontconfig_internal: src/CMakeFiles/fontconfig_internal.dir/rule
.PHONY : fontconfig_internal

# codegen rule for target.
src/CMakeFiles/fontconfig_internal.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
src/CMakeFiles/fontconfig_internal.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
src/CMakeFiles/fontconfig_internal.dir/codegen: src/CMakeFiles/generated_headers.dir/all
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/fontconfig_internal.dir/build.make src/CMakeFiles/fontconfig_internal.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70 "Finished codegen for target fontconfig_internal"
.PHONY : src/CMakeFiles/fontconfig_internal.dir/codegen

# clean rule for target.
src/CMakeFiles/fontconfig_internal.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/CMakeFiles/fontconfig_internal.dir/build.make src/CMakeFiles/fontconfig_internal.dir/clean
.PHONY : src/CMakeFiles/fontconfig_internal.dir/clean

#=============================================================================
# Target rules for target fc-cache/CMakeFiles/fc-cache.dir

# All Build rule for target.
fc-cache/CMakeFiles/fc-cache.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
fc-cache/CMakeFiles/fc-cache.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
fc-cache/CMakeFiles/fc-cache.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f fc-cache/CMakeFiles/fc-cache.dir/build.make fc-cache/CMakeFiles/fc-cache.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-cache/CMakeFiles/fc-cache.dir/build.make fc-cache/CMakeFiles/fc-cache.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=1 "Built target fc-cache"
.PHONY : fc-cache/CMakeFiles/fc-cache.dir/all

# Build rule for subdir invocation for target.
fc-cache/CMakeFiles/fc-cache.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 34
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-cache/CMakeFiles/fc-cache.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-cache/CMakeFiles/fc-cache.dir/rule

# Convenience name for target.
fc-cache: fc-cache/CMakeFiles/fc-cache.dir/rule
.PHONY : fc-cache

# codegen rule for target.
fc-cache/CMakeFiles/fc-cache.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
fc-cache/CMakeFiles/fc-cache.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f fc-cache/CMakeFiles/fc-cache.dir/build.make fc-cache/CMakeFiles/fc-cache.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=1 "Finished codegen for target fc-cache"
.PHONY : fc-cache/CMakeFiles/fc-cache.dir/codegen

# clean rule for target.
fc-cache/CMakeFiles/fc-cache.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-cache/CMakeFiles/fc-cache.dir/build.make fc-cache/CMakeFiles/fc-cache.dir/clean
.PHONY : fc-cache/CMakeFiles/fc-cache.dir/clean

#=============================================================================
# Target rules for target fc-cat/CMakeFiles/fc-cat.dir

# All Build rule for target.
fc-cat/CMakeFiles/fc-cat.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
fc-cat/CMakeFiles/fc-cat.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
fc-cat/CMakeFiles/fc-cat.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f fc-cat/CMakeFiles/fc-cat.dir/build.make fc-cat/CMakeFiles/fc-cat.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-cat/CMakeFiles/fc-cat.dir/build.make fc-cat/CMakeFiles/fc-cat.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=2,3 "Built target fc-cat"
.PHONY : fc-cat/CMakeFiles/fc-cat.dir/all

# Build rule for subdir invocation for target.
fc-cat/CMakeFiles/fc-cat.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-cat/CMakeFiles/fc-cat.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-cat/CMakeFiles/fc-cat.dir/rule

# Convenience name for target.
fc-cat: fc-cat/CMakeFiles/fc-cat.dir/rule
.PHONY : fc-cat

# codegen rule for target.
fc-cat/CMakeFiles/fc-cat.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
fc-cat/CMakeFiles/fc-cat.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f fc-cat/CMakeFiles/fc-cat.dir/build.make fc-cat/CMakeFiles/fc-cat.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=2,3 "Finished codegen for target fc-cat"
.PHONY : fc-cat/CMakeFiles/fc-cat.dir/codegen

# clean rule for target.
fc-cat/CMakeFiles/fc-cat.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-cat/CMakeFiles/fc-cat.dir/build.make fc-cat/CMakeFiles/fc-cat.dir/clean
.PHONY : fc-cat/CMakeFiles/fc-cat.dir/clean

#=============================================================================
# Target rules for target fc-conflist/CMakeFiles/fc-conflist.dir

# All Build rule for target.
fc-conflist/CMakeFiles/fc-conflist.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
fc-conflist/CMakeFiles/fc-conflist.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
fc-conflist/CMakeFiles/fc-conflist.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f fc-conflist/CMakeFiles/fc-conflist.dir/build.make fc-conflist/CMakeFiles/fc-conflist.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-conflist/CMakeFiles/fc-conflist.dir/build.make fc-conflist/CMakeFiles/fc-conflist.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=4,5 "Built target fc-conflist"
.PHONY : fc-conflist/CMakeFiles/fc-conflist.dir/all

# Build rule for subdir invocation for target.
fc-conflist/CMakeFiles/fc-conflist.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-conflist/CMakeFiles/fc-conflist.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-conflist/CMakeFiles/fc-conflist.dir/rule

# Convenience name for target.
fc-conflist: fc-conflist/CMakeFiles/fc-conflist.dir/rule
.PHONY : fc-conflist

# codegen rule for target.
fc-conflist/CMakeFiles/fc-conflist.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
fc-conflist/CMakeFiles/fc-conflist.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f fc-conflist/CMakeFiles/fc-conflist.dir/build.make fc-conflist/CMakeFiles/fc-conflist.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=4,5 "Finished codegen for target fc-conflist"
.PHONY : fc-conflist/CMakeFiles/fc-conflist.dir/codegen

# clean rule for target.
fc-conflist/CMakeFiles/fc-conflist.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-conflist/CMakeFiles/fc-conflist.dir/build.make fc-conflist/CMakeFiles/fc-conflist.dir/clean
.PHONY : fc-conflist/CMakeFiles/fc-conflist.dir/clean

#=============================================================================
# Target rules for target fc-list/CMakeFiles/fc-list.dir

# All Build rule for target.
fc-list/CMakeFiles/fc-list.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
fc-list/CMakeFiles/fc-list.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
fc-list/CMakeFiles/fc-list.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f fc-list/CMakeFiles/fc-list.dir/build.make fc-list/CMakeFiles/fc-list.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-list/CMakeFiles/fc-list.dir/build.make fc-list/CMakeFiles/fc-list.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=6,7 "Built target fc-list"
.PHONY : fc-list/CMakeFiles/fc-list.dir/all

# Build rule for subdir invocation for target.
fc-list/CMakeFiles/fc-list.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-list/CMakeFiles/fc-list.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-list/CMakeFiles/fc-list.dir/rule

# Convenience name for target.
fc-list: fc-list/CMakeFiles/fc-list.dir/rule
.PHONY : fc-list

# codegen rule for target.
fc-list/CMakeFiles/fc-list.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
fc-list/CMakeFiles/fc-list.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f fc-list/CMakeFiles/fc-list.dir/build.make fc-list/CMakeFiles/fc-list.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=6,7 "Finished codegen for target fc-list"
.PHONY : fc-list/CMakeFiles/fc-list.dir/codegen

# clean rule for target.
fc-list/CMakeFiles/fc-list.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-list/CMakeFiles/fc-list.dir/build.make fc-list/CMakeFiles/fc-list.dir/clean
.PHONY : fc-list/CMakeFiles/fc-list.dir/clean

#=============================================================================
# Target rules for target fc-match/CMakeFiles/fc-match.dir

# All Build rule for target.
fc-match/CMakeFiles/fc-match.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
fc-match/CMakeFiles/fc-match.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
fc-match/CMakeFiles/fc-match.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f fc-match/CMakeFiles/fc-match.dir/build.make fc-match/CMakeFiles/fc-match.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-match/CMakeFiles/fc-match.dir/build.make fc-match/CMakeFiles/fc-match.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=8,9 "Built target fc-match"
.PHONY : fc-match/CMakeFiles/fc-match.dir/all

# Build rule for subdir invocation for target.
fc-match/CMakeFiles/fc-match.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-match/CMakeFiles/fc-match.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-match/CMakeFiles/fc-match.dir/rule

# Convenience name for target.
fc-match: fc-match/CMakeFiles/fc-match.dir/rule
.PHONY : fc-match

# codegen rule for target.
fc-match/CMakeFiles/fc-match.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
fc-match/CMakeFiles/fc-match.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f fc-match/CMakeFiles/fc-match.dir/build.make fc-match/CMakeFiles/fc-match.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=8,9 "Finished codegen for target fc-match"
.PHONY : fc-match/CMakeFiles/fc-match.dir/codegen

# clean rule for target.
fc-match/CMakeFiles/fc-match.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-match/CMakeFiles/fc-match.dir/build.make fc-match/CMakeFiles/fc-match.dir/clean
.PHONY : fc-match/CMakeFiles/fc-match.dir/clean

#=============================================================================
# Target rules for target fc-pattern/CMakeFiles/fc-pattern.dir

# All Build rule for target.
fc-pattern/CMakeFiles/fc-pattern.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
fc-pattern/CMakeFiles/fc-pattern.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
fc-pattern/CMakeFiles/fc-pattern.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f fc-pattern/CMakeFiles/fc-pattern.dir/build.make fc-pattern/CMakeFiles/fc-pattern.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-pattern/CMakeFiles/fc-pattern.dir/build.make fc-pattern/CMakeFiles/fc-pattern.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=10,11 "Built target fc-pattern"
.PHONY : fc-pattern/CMakeFiles/fc-pattern.dir/all

# Build rule for subdir invocation for target.
fc-pattern/CMakeFiles/fc-pattern.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-pattern/CMakeFiles/fc-pattern.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-pattern/CMakeFiles/fc-pattern.dir/rule

# Convenience name for target.
fc-pattern: fc-pattern/CMakeFiles/fc-pattern.dir/rule
.PHONY : fc-pattern

# codegen rule for target.
fc-pattern/CMakeFiles/fc-pattern.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
fc-pattern/CMakeFiles/fc-pattern.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f fc-pattern/CMakeFiles/fc-pattern.dir/build.make fc-pattern/CMakeFiles/fc-pattern.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=10,11 "Finished codegen for target fc-pattern"
.PHONY : fc-pattern/CMakeFiles/fc-pattern.dir/codegen

# clean rule for target.
fc-pattern/CMakeFiles/fc-pattern.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-pattern/CMakeFiles/fc-pattern.dir/build.make fc-pattern/CMakeFiles/fc-pattern.dir/clean
.PHONY : fc-pattern/CMakeFiles/fc-pattern.dir/clean

#=============================================================================
# Target rules for target fc-query/CMakeFiles/fc-query.dir

# All Build rule for target.
fc-query/CMakeFiles/fc-query.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
fc-query/CMakeFiles/fc-query.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
fc-query/CMakeFiles/fc-query.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f fc-query/CMakeFiles/fc-query.dir/build.make fc-query/CMakeFiles/fc-query.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-query/CMakeFiles/fc-query.dir/build.make fc-query/CMakeFiles/fc-query.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=12 "Built target fc-query"
.PHONY : fc-query/CMakeFiles/fc-query.dir/all

# Build rule for subdir invocation for target.
fc-query/CMakeFiles/fc-query.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 34
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-query/CMakeFiles/fc-query.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-query/CMakeFiles/fc-query.dir/rule

# Convenience name for target.
fc-query: fc-query/CMakeFiles/fc-query.dir/rule
.PHONY : fc-query

# codegen rule for target.
fc-query/CMakeFiles/fc-query.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
fc-query/CMakeFiles/fc-query.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f fc-query/CMakeFiles/fc-query.dir/build.make fc-query/CMakeFiles/fc-query.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=12 "Finished codegen for target fc-query"
.PHONY : fc-query/CMakeFiles/fc-query.dir/codegen

# clean rule for target.
fc-query/CMakeFiles/fc-query.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-query/CMakeFiles/fc-query.dir/build.make fc-query/CMakeFiles/fc-query.dir/clean
.PHONY : fc-query/CMakeFiles/fc-query.dir/clean

#=============================================================================
# Target rules for target fc-scan/CMakeFiles/fc-scan.dir

# All Build rule for target.
fc-scan/CMakeFiles/fc-scan.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
fc-scan/CMakeFiles/fc-scan.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
fc-scan/CMakeFiles/fc-scan.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f fc-scan/CMakeFiles/fc-scan.dir/build.make fc-scan/CMakeFiles/fc-scan.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-scan/CMakeFiles/fc-scan.dir/build.make fc-scan/CMakeFiles/fc-scan.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=13,14 "Built target fc-scan"
.PHONY : fc-scan/CMakeFiles/fc-scan.dir/all

# Build rule for subdir invocation for target.
fc-scan/CMakeFiles/fc-scan.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-scan/CMakeFiles/fc-scan.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-scan/CMakeFiles/fc-scan.dir/rule

# Convenience name for target.
fc-scan: fc-scan/CMakeFiles/fc-scan.dir/rule
.PHONY : fc-scan

# codegen rule for target.
fc-scan/CMakeFiles/fc-scan.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
fc-scan/CMakeFiles/fc-scan.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f fc-scan/CMakeFiles/fc-scan.dir/build.make fc-scan/CMakeFiles/fc-scan.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=13,14 "Finished codegen for target fc-scan"
.PHONY : fc-scan/CMakeFiles/fc-scan.dir/codegen

# clean rule for target.
fc-scan/CMakeFiles/fc-scan.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-scan/CMakeFiles/fc-scan.dir/build.make fc-scan/CMakeFiles/fc-scan.dir/clean
.PHONY : fc-scan/CMakeFiles/fc-scan.dir/clean

#=============================================================================
# Target rules for target fc-validate/CMakeFiles/fc-validate.dir

# All Build rule for target.
fc-validate/CMakeFiles/fc-validate.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
fc-validate/CMakeFiles/fc-validate.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
fc-validate/CMakeFiles/fc-validate.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f fc-validate/CMakeFiles/fc-validate.dir/build.make fc-validate/CMakeFiles/fc-validate.dir/depend
	$(MAKE) $(MAKESILENT) -f fc-validate/CMakeFiles/fc-validate.dir/build.make fc-validate/CMakeFiles/fc-validate.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=15,16 "Built target fc-validate"
.PHONY : fc-validate/CMakeFiles/fc-validate.dir/all

# Build rule for subdir invocation for target.
fc-validate/CMakeFiles/fc-validate.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fc-validate/CMakeFiles/fc-validate.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : fc-validate/CMakeFiles/fc-validate.dir/rule

# Convenience name for target.
fc-validate: fc-validate/CMakeFiles/fc-validate.dir/rule
.PHONY : fc-validate

# codegen rule for target.
fc-validate/CMakeFiles/fc-validate.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
fc-validate/CMakeFiles/fc-validate.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f fc-validate/CMakeFiles/fc-validate.dir/build.make fc-validate/CMakeFiles/fc-validate.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=15,16 "Finished codegen for target fc-validate"
.PHONY : fc-validate/CMakeFiles/fc-validate.dir/codegen

# clean rule for target.
fc-validate/CMakeFiles/fc-validate.dir/clean:
	$(MAKE) $(MAKESILENT) -f fc-validate/CMakeFiles/fc-validate.dir/build.make fc-validate/CMakeFiles/fc-validate.dir/clean
.PHONY : fc-validate/CMakeFiles/fc-validate.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/fetch_test_fonts.dir

# All Build rule for target.
test/CMakeFiles/fetch_test_fonts.dir/all:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/fetch_test_fonts.dir/build.make test/CMakeFiles/fetch_test_fonts.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/fetch_test_fonts.dir/build.make test/CMakeFiles/fetch_test_fonts.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=19 "Built target fetch_test_fonts"
.PHONY : test/CMakeFiles/fetch_test_fonts.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/fetch_test_fonts.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/fetch_test_fonts.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/fetch_test_fonts.dir/rule

# Convenience name for target.
fetch_test_fonts: test/CMakeFiles/fetch_test_fonts.dir/rule
.PHONY : fetch_test_fonts

# codegen rule for target.
test/CMakeFiles/fetch_test_fonts.dir/codegen:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/fetch_test_fonts.dir/build.make test/CMakeFiles/fetch_test_fonts.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=19 "Finished codegen for target fetch_test_fonts"
.PHONY : test/CMakeFiles/fetch_test_fonts.dir/codegen

# clean rule for target.
test/CMakeFiles/fetch_test_fonts.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/fetch_test_fonts.dir/build.make test/CMakeFiles/fetch_test_fonts.dir/clean
.PHONY : test/CMakeFiles/fetch_test_fonts.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_bz89617.dir

# All Build rule for target.
test/CMakeFiles/test_bz89617.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz89617.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_bz89617.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz89617.dir/build.make test/CMakeFiles/test_bz89617.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz89617.dir/build.make test/CMakeFiles/test_bz89617.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=84,85 "Built target test_bz89617"
.PHONY : test/CMakeFiles/test_bz89617.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_bz89617.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_bz89617.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_bz89617.dir/rule

# Convenience name for target.
test_bz89617: test/CMakeFiles/test_bz89617.dir/rule
.PHONY : test_bz89617

# codegen rule for target.
test/CMakeFiles/test_bz89617.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz89617.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz89617.dir/build.make test/CMakeFiles/test_bz89617.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=84,85 "Finished codegen for target test_bz89617"
.PHONY : test/CMakeFiles/test_bz89617.dir/codegen

# clean rule for target.
test/CMakeFiles/test_bz89617.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz89617.dir/build.make test/CMakeFiles/test_bz89617.dir/clean
.PHONY : test/CMakeFiles/test_bz89617.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_bz131804.dir

# All Build rule for target.
test/CMakeFiles/test_bz131804.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz131804.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_bz131804.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz131804.dir/build.make test/CMakeFiles/test_bz131804.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz131804.dir/build.make test/CMakeFiles/test_bz131804.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=80,81 "Built target test_bz131804"
.PHONY : test/CMakeFiles/test_bz131804.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_bz131804.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_bz131804.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_bz131804.dir/rule

# Convenience name for target.
test_bz131804: test/CMakeFiles/test_bz131804.dir/rule
.PHONY : test_bz131804

# codegen rule for target.
test/CMakeFiles/test_bz131804.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz131804.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz131804.dir/build.make test/CMakeFiles/test_bz131804.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=80,81 "Finished codegen for target test_bz131804"
.PHONY : test/CMakeFiles/test_bz131804.dir/codegen

# clean rule for target.
test/CMakeFiles/test_bz131804.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz131804.dir/build.make test/CMakeFiles/test_bz131804.dir/clean
.PHONY : test/CMakeFiles/test_bz131804.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_bz96676.dir

# All Build rule for target.
test/CMakeFiles/test_bz96676.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz96676.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_bz96676.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz96676.dir/build.make test/CMakeFiles/test_bz96676.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz96676.dir/build.make test/CMakeFiles/test_bz96676.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=86,87 "Built target test_bz96676"
.PHONY : test/CMakeFiles/test_bz96676.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_bz96676.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_bz96676.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_bz96676.dir/rule

# Convenience name for target.
test_bz96676: test/CMakeFiles/test_bz96676.dir/rule
.PHONY : test_bz96676

# codegen rule for target.
test/CMakeFiles/test_bz96676.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz96676.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz96676.dir/build.make test/CMakeFiles/test_bz96676.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=86,87 "Finished codegen for target test_bz96676"
.PHONY : test/CMakeFiles/test_bz96676.dir/codegen

# clean rule for target.
test/CMakeFiles/test_bz96676.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz96676.dir/build.make test/CMakeFiles/test_bz96676.dir/clean
.PHONY : test/CMakeFiles/test_bz96676.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_name_parse.dir

# All Build rule for target.
test/CMakeFiles/test_name_parse.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_name_parse.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_name_parse.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_name_parse.dir/build.make test/CMakeFiles/test_name_parse.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_name_parse.dir/build.make test/CMakeFiles/test_name_parse.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=97,98 "Built target test_name_parse"
.PHONY : test/CMakeFiles/test_name_parse.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_name_parse.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_name_parse.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_name_parse.dir/rule

# Convenience name for target.
test_name_parse: test/CMakeFiles/test_name_parse.dir/rule
.PHONY : test_name_parse

# codegen rule for target.
test/CMakeFiles/test_name_parse.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_name_parse.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_name_parse.dir/build.make test/CMakeFiles/test_name_parse.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=97,98 "Finished codegen for target test_name_parse"
.PHONY : test/CMakeFiles/test_name_parse.dir/codegen

# clean rule for target.
test/CMakeFiles/test_name_parse.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_name_parse.dir/build.make test/CMakeFiles/test_name_parse.dir/clean
.PHONY : test/CMakeFiles/test_name_parse.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_bz106618.dir

# All Build rule for target.
test/CMakeFiles/test_bz106618.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz106618.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_bz106618.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz106618.dir/build.make test/CMakeFiles/test_bz106618.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz106618.dir/build.make test/CMakeFiles/test_bz106618.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=77 "Built target test_bz106618"
.PHONY : test/CMakeFiles/test_bz106618.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_bz106618.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 34
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_bz106618.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_bz106618.dir/rule

# Convenience name for target.
test_bz106618: test/CMakeFiles/test_bz106618.dir/rule
.PHONY : test_bz106618

# codegen rule for target.
test/CMakeFiles/test_bz106618.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz106618.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz106618.dir/build.make test/CMakeFiles/test_bz106618.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=77 "Finished codegen for target test_bz106618"
.PHONY : test/CMakeFiles/test_bz106618.dir/codegen

# clean rule for target.
test/CMakeFiles/test_bz106618.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz106618.dir/build.make test/CMakeFiles/test_bz106618.dir/clean
.PHONY : test/CMakeFiles/test_bz106618.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_bz1744377.dir

# All Build rule for target.
test/CMakeFiles/test_bz1744377.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz1744377.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_bz1744377.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz1744377.dir/build.make test/CMakeFiles/test_bz1744377.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz1744377.dir/build.make test/CMakeFiles/test_bz1744377.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=82,83 "Built target test_bz1744377"
.PHONY : test/CMakeFiles/test_bz1744377.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_bz1744377.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_bz1744377.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_bz1744377.dir/rule

# Convenience name for target.
test_bz1744377: test/CMakeFiles/test_bz1744377.dir/rule
.PHONY : test_bz1744377

# codegen rule for target.
test/CMakeFiles/test_bz1744377.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz1744377.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz1744377.dir/build.make test/CMakeFiles/test_bz1744377.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=82,83 "Finished codegen for target test_bz1744377"
.PHONY : test/CMakeFiles/test_bz1744377.dir/codegen

# clean rule for target.
test/CMakeFiles/test_bz1744377.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz1744377.dir/build.make test/CMakeFiles/test_bz1744377.dir/clean
.PHONY : test/CMakeFiles/test_bz1744377.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_issue180.dir

# All Build rule for target.
test/CMakeFiles/test_issue180.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_issue180.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_issue180.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_issue180.dir/build.make test/CMakeFiles/test_issue180.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_issue180.dir/build.make test/CMakeFiles/test_issue180.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=93,94 "Built target test_issue180"
.PHONY : test/CMakeFiles/test_issue180.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_issue180.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_issue180.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_issue180.dir/rule

# Convenience name for target.
test_issue180: test/CMakeFiles/test_issue180.dir/rule
.PHONY : test_issue180

# codegen rule for target.
test/CMakeFiles/test_issue180.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_issue180.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_issue180.dir/build.make test/CMakeFiles/test_issue180.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=93,94 "Finished codegen for target test_issue180"
.PHONY : test/CMakeFiles/test_issue180.dir/codegen

# clean rule for target.
test/CMakeFiles/test_issue180.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_issue180.dir/build.make test/CMakeFiles/test_issue180.dir/clean
.PHONY : test/CMakeFiles/test_issue180.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_family_matching.dir

# All Build rule for target.
test/CMakeFiles/test_family_matching.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_family_matching.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_family_matching.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_family_matching.dir/build.make test/CMakeFiles/test_family_matching.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_family_matching.dir/build.make test/CMakeFiles/test_family_matching.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=89,90 "Built target test_family_matching"
.PHONY : test/CMakeFiles/test_family_matching.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_family_matching.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_family_matching.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_family_matching.dir/rule

# Convenience name for target.
test_family_matching: test/CMakeFiles/test_family_matching.dir/rule
.PHONY : test_family_matching

# codegen rule for target.
test/CMakeFiles/test_family_matching.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_family_matching.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_family_matching.dir/build.make test/CMakeFiles/test_family_matching.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=89,90 "Finished codegen for target test_family_matching"
.PHONY : test/CMakeFiles/test_family_matching.dir/codegen

# clean rule for target.
test/CMakeFiles/test_family_matching.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_family_matching.dir/build.make test/CMakeFiles/test_family_matching.dir/clean
.PHONY : test/CMakeFiles/test_family_matching.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_ptrlist.dir

# All Build rule for target.
test/CMakeFiles/test_ptrlist.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_ptrlist.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_ptrlist.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_ptrlist.dir/build.make test/CMakeFiles/test_ptrlist.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_ptrlist.dir/build.make test/CMakeFiles/test_ptrlist.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=99,100 "Built target test_ptrlist"
.PHONY : test/CMakeFiles/test_ptrlist.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_ptrlist.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_ptrlist.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_ptrlist.dir/rule

# Convenience name for target.
test_ptrlist: test/CMakeFiles/test_ptrlist.dir/rule
.PHONY : test_ptrlist

# codegen rule for target.
test/CMakeFiles/test_ptrlist.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_ptrlist.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_ptrlist.dir/build.make test/CMakeFiles/test_ptrlist.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=99,100 "Finished codegen for target test_ptrlist"
.PHONY : test/CMakeFiles/test_ptrlist.dir/codegen

# clean rule for target.
test/CMakeFiles/test_ptrlist.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_ptrlist.dir/build.make test/CMakeFiles/test_ptrlist.dir/clean
.PHONY : test/CMakeFiles/test_ptrlist.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_bz106632.dir

# All Build rule for target.
test/CMakeFiles/test_bz106632.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz106632.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_bz106632.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz106632.dir/build.make test/CMakeFiles/test_bz106632.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz106632.dir/build.make test/CMakeFiles/test_bz106632.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=78,79 "Built target test_bz106632"
.PHONY : test/CMakeFiles/test_bz106632.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_bz106632.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_bz106632.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_bz106632.dir/rule

# Convenience name for target.
test_bz106632: test/CMakeFiles/test_bz106632.dir/rule
.PHONY : test_bz106632

# codegen rule for target.
test/CMakeFiles/test_bz106632.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_bz106632.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz106632.dir/build.make test/CMakeFiles/test_bz106632.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=78,79 "Finished codegen for target test_bz106632"
.PHONY : test/CMakeFiles/test_bz106632.dir/codegen

# clean rule for target.
test/CMakeFiles/test_bz106632.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_bz106632.dir/build.make test/CMakeFiles/test_bz106632.dir/clean
.PHONY : test/CMakeFiles/test_bz106632.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_issue107.dir

# All Build rule for target.
test/CMakeFiles/test_issue107.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_issue107.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_issue107.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_issue107.dir/build.make test/CMakeFiles/test_issue107.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_issue107.dir/build.make test/CMakeFiles/test_issue107.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=91,92 "Built target test_issue107"
.PHONY : test/CMakeFiles/test_issue107.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_issue107.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_issue107.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_issue107.dir/rule

# Convenience name for target.
test_issue107: test/CMakeFiles/test_issue107.dir/rule
.PHONY : test_issue107

# codegen rule for target.
test/CMakeFiles/test_issue107.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_issue107.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_issue107.dir/build.make test/CMakeFiles/test_issue107.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=91,92 "Finished codegen for target test_issue107"
.PHONY : test/CMakeFiles/test_issue107.dir/codegen

# clean rule for target.
test/CMakeFiles/test_issue107.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_issue107.dir/build.make test/CMakeFiles/test_issue107.dir/clean
.PHONY : test/CMakeFiles/test_issue107.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_crbug1004254.dir

# All Build rule for target.
test/CMakeFiles/test_crbug1004254.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_crbug1004254.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_crbug1004254.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_crbug1004254.dir/build.make test/CMakeFiles/test_crbug1004254.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_crbug1004254.dir/build.make test/CMakeFiles/test_crbug1004254.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=88 "Built target test_crbug1004254"
.PHONY : test/CMakeFiles/test_crbug1004254.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_crbug1004254.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 34
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_crbug1004254.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_crbug1004254.dir/rule

# Convenience name for target.
test_crbug1004254: test/CMakeFiles/test_crbug1004254.dir/rule
.PHONY : test_crbug1004254

# codegen rule for target.
test/CMakeFiles/test_crbug1004254.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_crbug1004254.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_crbug1004254.dir/build.make test/CMakeFiles/test_crbug1004254.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=88 "Finished codegen for target test_crbug1004254"
.PHONY : test/CMakeFiles/test_crbug1004254.dir/codegen

# clean rule for target.
test/CMakeFiles/test_crbug1004254.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_crbug1004254.dir/build.make test/CMakeFiles/test_crbug1004254.dir/clean
.PHONY : test/CMakeFiles/test_crbug1004254.dir/clean

#=============================================================================
# Target rules for target test/CMakeFiles/test_mt_fccfg.dir

# All Build rule for target.
test/CMakeFiles/test_mt_fccfg.dir/all: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_mt_fccfg.dir/all: fc-lang/CMakeFiles/fclang_h.dir/all
test/CMakeFiles/test_mt_fccfg.dir/all: src/CMakeFiles/fontconfig_internal.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_mt_fccfg.dir/build.make test/CMakeFiles/test_mt_fccfg.dir/depend
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_mt_fccfg.dir/build.make test/CMakeFiles/test_mt_fccfg.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=95,96 "Built target test_mt_fccfg"
.PHONY : test/CMakeFiles/test_mt_fccfg.dir/all

# Build rule for subdir invocation for target.
test/CMakeFiles/test_mt_fccfg.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test/CMakeFiles/test_mt_fccfg.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : test/CMakeFiles/test_mt_fccfg.dir/rule

# Convenience name for target.
test_mt_fccfg: test/CMakeFiles/test_mt_fccfg.dir/rule
.PHONY : test_mt_fccfg

# codegen rule for target.
test/CMakeFiles/test_mt_fccfg.dir/codegen: fc-case/CMakeFiles/fccase_h.dir/all
test/CMakeFiles/test_mt_fccfg.dir/codegen: fc-lang/CMakeFiles/fclang_h.dir/all
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_mt_fccfg.dir/build.make test/CMakeFiles/test_mt_fccfg.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=95,96 "Finished codegen for target test_mt_fccfg"
.PHONY : test/CMakeFiles/test_mt_fccfg.dir/codegen

# clean rule for target.
test/CMakeFiles/test_mt_fccfg.dir/clean:
	$(MAKE) $(MAKESILENT) -f test/CMakeFiles/test_mt_fccfg.dir/build.make test/CMakeFiles/test_mt_fccfg.dir/clean
.PHONY : test/CMakeFiles/test_mt_fccfg.dir/clean

#=============================================================================
# Target rules for target conf.d/CMakeFiles/lang_normalize_conf.dir

# All Build rule for target.
conf.d/CMakeFiles/lang_normalize_conf.dir/all:
	$(MAKE) $(MAKESILENT) -f conf.d/CMakeFiles/lang_normalize_conf.dir/build.make conf.d/CMakeFiles/lang_normalize_conf.dir/depend
	$(MAKE) $(MAKESILENT) -f conf.d/CMakeFiles/lang_normalize_conf.dir/build.make conf.d/CMakeFiles/lang_normalize_conf.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=75 "Built target lang_normalize_conf"
.PHONY : conf.d/CMakeFiles/lang_normalize_conf.dir/all

# Build rule for subdir invocation for target.
conf.d/CMakeFiles/lang_normalize_conf.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 conf.d/CMakeFiles/lang_normalize_conf.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/work/fontconfig/build-cmake/CMakeFiles 0
.PHONY : conf.d/CMakeFiles/lang_normalize_conf.dir/rule

# Convenience name for target.
lang_normalize_conf: conf.d/CMakeFiles/lang_normalize_conf.dir/rule
.PHONY : lang_normalize_conf

# codegen rule for target.
conf.d/CMakeFiles/lang_normalize_conf.dir/codegen:
	$(MAKE) $(MAKESILENT) -f conf.d/CMakeFiles/lang_normalize_conf.dir/build.make conf.d/CMakeFiles/lang_normalize_conf.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/work/fontconfig/build-cmake/CMakeFiles --progress-num=75 "Finished codegen for target lang_normalize_conf"
.PHONY : conf.d/CMakeFiles/lang_normalize_conf.dir/codegen

# clean rule for target.
conf.d/CMakeFiles/lang_normalize_conf.dir/clean:
	$(MAKE) $(MAKESILENT) -f conf.d/CMakeFiles/lang_normalize_conf.dir/build.make conf.d/CMakeFiles/lang_normalize_conf.dir/clean
.PHONY : conf.d/CMakeFiles/lang_normalize_conf.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

