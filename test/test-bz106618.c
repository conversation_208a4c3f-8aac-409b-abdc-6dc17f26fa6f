/*
 * fontconfig/test/test-bz89617.c
 *
 * Copyright © 2000 <PERSON> Packard
 *
 * Permission to use, copy, modify, distribute, and sell this software and its
 * documentation for any purpose is hereby granted without fee, provided that
 * the above copyright notice appear in all copies and that both that
 * copyright notice and this permission notice appear in supporting
 * documentation, and that the name of the author(s) not be used in
 * advertising or publicity pertaining to distribution of the software without
 * specific, written prior permission.  The authors make no
 * representations about the suitability of this software for any purpose.  It
 * is provided "as is" without express or implied warranty.
 *
 * THE AUTHOR(S) DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
 * INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
 * EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
 * CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
 * DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
 * TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */
#include <fontconfig/fontconfig.h>

#include <stdio.h>

int
main (int argc, char **argv)
{
    const FcFontSet *fs = FcConfigGetFonts (NULL, FcSetSystem);
    int        i;

    if (!fs)
	return 1;
    for (i = 0; i < fs->nfont; i++) {
	FcPattern *p = fs->fonts[i];
	FcChar8   *file;

	if (FcPatternGetString (p, FC_FILE, 0, &file) != FcResultMatch)
	    return 1;
	printf ("%s\n", file);
    }
    FcFini();

    return 0;
}
