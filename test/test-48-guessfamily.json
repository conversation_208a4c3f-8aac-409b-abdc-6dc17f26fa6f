{"fonts": [], "tests": [{"method": "pattern", "query": {"family": "<PERSON><PERSON><PERSON>"}, "result": {"family": ["<PERSON><PERSON><PERSON>", "monospace"]}}, {"method": "pattern", "query": {"family": "Adwaita Sans"}, "result": {"family": ["Adwaita Sans", "sans-serif"]}}, {"method": "pattern", "query": {"family": "<PERSON><PERSON> <PERSON><PERSON>"}, "result": {"family": ["<PERSON><PERSON> <PERSON><PERSON>", "serif"]}}]}