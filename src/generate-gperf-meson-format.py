#!/usr/bin/env python3

import sys
import re
import os

def parse_fcobjs_h(fcobjs_path):
    """Parse fcobjs.h to extract object names and create string mappings"""
    objects = []

    with open(fcobjs_path, 'r') as f:
        content = f.read()

    # Find FC_OBJECT() macro definitions like: FC_OBJECT (FAMILY, FcTypeString, FcCompareFamily)
    fc_pattern = r'FC_OBJECT\s*\(\s*([A-Z_]+)\s*,\s*[^,]+\s*,\s*[^)]+\)'
    matches = re.findall(fc_pattern, content)

    # Create list of (string_literal, object_constant) pairs
    for macro_name in matches:
        # Convert macro name to lowercase string (e.g., FAMILY -> "family")
        string_val = macro_name.lower()
        object_const = f"FC_{macro_name}_OBJECT"
        objects.append((f'"{string_val}"', object_const))

    return objects

def generate_meson_format_gperf(template_path, fcobjs_path, output_path):
    """Generate gperf file in Meson-compatible format (using string literals)"""

    # Parse fcobjs.h to get object mappings
    objects = parse_fcobjs_h(fcobjs_path)

    # Write the output file with Meson-compatible minimal header
    with open(output_path, 'w') as f:
        # Write minimal header like Meson version
        f.write('%{\n')
        f.write('%}\n')
        f.write('%struct-type\n')
        f.write('%language=ANSI-C\n')
        f.write('%includes\n')
        f.write('%enum\n')
        f.write('%readonly-tables\n')
        f.write('%define slot-name name\n')
        f.write('%define hash-function-name FcObjectTypeHash\n')
        f.write('%define lookup-function-name FcObjectTypeLookup\n')
        f.write('%pic\n')
        f.write('%define string-pool-name FcObjectTypeNamePool\n')
        f.write('struct FcObjectTypeInfo {\n')
        f.write('int name;\n')
        f.write('int id;\n')
        f.write('};\n')
        f.write('%%\n')

        # Write object data in Meson format
        for string_literal, object_const in objects:
            f.write(f"{string_literal},{object_const}\n")

if __name__ == '__main__':
    if len(sys.argv) != 4:
        print("Usage: generate-gperf-meson-format.py <template_file> <fcobjs_file> <output_file>")
        sys.exit(1)

    template_path = sys.argv[1]
    fcobjs_path = sys.argv[2]
    output_path = sys.argv[3]

    generate_meson_format_gperf(template_path, fcobjs_path, output_path)