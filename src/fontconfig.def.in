EXPORTS
	FcAtomicCreate
	FcAtomicDeleteNew
	FcAtomicDestroy
	FcAtomicLock
	FcAtomicNewFile
	FcAtomicOrigFile
	FcAtomicReplaceOrig
	FcAtomicUnlock
	FcBlanksAdd
	FcBlanksCreate
	FcBlanksDestroy
	FcBlanksIsMember
	FcCacheCopySet
	FcCacheCreateTagFile
	FcCacheDir
	FcCacheNumFont
	FcCacheNumSubdir
	FcCacheSubdir
	FcCharSetAddChar
	FcCharSetCopy
	FcCharSetCount
	FcCharSetCoverage
	FcCharSetCreate
	FcCharSetDelChar
	FcCharSetDestroy
	FcCharSetEqual
	FcCharSetFirstPage
	FcCharSetHasChar
	FcCharSetIntersect
	FcCharSetIntersectCount
	FcCharSetIsSubset
	FcCharSetMerge
	FcCharSetNew
	FcCharSetNextPage
	FcCharSetSubtract
	FcCharSetSubtractCount
	FcCharSetUnion
	FcConfigAppFontAddDir
	FcConfigAppFontAddFile
	FcConfigAppFontClear
	FcConfigBuildFonts
	FcConfigCreate
	FcConfigDestroy
	FcConfigEnableHome
	FcConfigFileInfoIterGet
	FcConfigFileInfoIterInit
	FcConfigFileInfoIterNext
	FcConfigFilename
	FcConfigGetBlanks
	FcConfigGetCache
	FcConfigGetCacheDirs
	FcConfigGetConfigDirs
	FcConfigGetConfigFiles
	FcConfigGetCurrent
	FcConfigGetFontDirs
	FcConfigGetFonts
	FcConfigGetRescanInterval
	FcConfigGetRescanInverval
	FcConfigGetSysRoot
	FcConfigHome
	FcConfigParseAndLoad
	FcConfigParseAndLoadFromMemory
	FcConfigReference
	FcConfigSetCurrent
	FcConfigSetRescanInterval
	FcConfigSetRescanInverval
	FcConfigSetSysRoot
	FcConfigSubstitute
	FcConfigSubstituteWithPat
	FcConfigUptoDate
	FcDefaultSubstitute
	FcDirCacheClean
	FcDirCacheCreateUUID
	FcDirCacheDeleteUUID
	FcDirCacheLoad
	FcDirCacheLoadFile
	FcDirCacheRead
	FcDirCacheRescan
	FcDirCacheUnlink
	FcDirCacheUnload
	FcDirCacheValid
	FcDirSave
	FcDirScan
	FcFileIsDir
	FcFileScan
	FcFini
	FcFontList
	FcFontMatch
	FcFontRenderPrepare
	FcFontSetAdd
	FcFontSetCreate
	FcFontSetDestroy
	FcFontSetList
	FcFontSetMatch
	FcFontSetPrint
	FcFontSetSort
	FcFontSetSortDestroy
	FcFontSort
	FcFreeTypeCharIndex
	FcFreeTypeCharSet
	FcFreeTypeCharSetAndSpacing
	FcFreeTypeQuery
	FcFreeTypeQueryAll
	FcFreeTypeQueryFace
	FcGetDefaultLangs
	FcGetLangs
	FcGetVersion
	FcInit
	FcInitBringUptoDate
	FcInitLoadConfig
	FcInitLoadConfigAndFonts
	FcInitReinitialize
	FcLangGetCharSet
	FcLangNormalize
	FcLangSetAdd
	FcLangSetCompare
	FcLangSetContains
	FcLangSetCopy
	FcLangSetCreate
	FcLangSetDel
	FcLangSetDestroy
	FcLangSetEqual
	FcLangSetGetLangs
	FcLangSetHash
	FcLangSetHasLang
	FcLangSetSubtract
	FcLangSetUnion
	FcMatrixCopy
	FcMatrixEqual
	FcMatrixMultiply
	FcMatrixRotate
	FcMatrixScale
	FcMatrixShear
	FcNameConstant
	FcNameGetConstant
	FcNameGetObjectType
	FcNameParse
	FcNameRegisterConstants
	FcNameRegisterObjectTypes
	FcNameUnparse
	FcNameUnregisterConstants
	FcNameUnregisterObjectTypes
	FcObjectSetAdd
	FcObjectSetBuild
	FcObjectSetCreate
	FcObjectSetDestroy
	FcObjectSetVaBuild
	FcPatternAdd
	FcPatternAddBool
	FcPatternAddCharSet
	FcPatternAddDouble
	FcPatternAddFTFace
	FcPatternAddInteger
	FcPatternAddLangSet
	FcPatternAddMatrix
	FcPatternAddRange
	FcPatternAddString
	FcPatternAddWeak
	FcPatternBuild
	FcPatternCreate
	FcPatternDel
	FcPatternDestroy
	FcPatternDuplicate
	FcPatternEqual
	FcPatternEqualSubset
	FcPatternFilter
	FcPatternFindIter
	FcPatternFormat
	FcPatternGet
	FcPatternGetBool
	FcPatternGetCharSet
	FcPatternGetDouble
	FcPatternGetFTFace
	FcPatternGetInteger
	FcPatternGetLangSet
	FcPatternGetMatrix
	FcPatternGetRange
	FcPatternGetString
	FcPatternGetWithBinding
	FcPatternHash
	FcPatternIterEqual
	FcPatternIterGetObject
	FcPatternIterGetValue
	FcPatternIterIsValid
	FcPatternIterNext
	FcPatternIterStart
	FcPatternIterValueCount
	FcPatternObjectCount
	FcPatternPrint
	FcPatternReference
	FcPatternRemove
	FcPatternVaBuild
	FcRangeCopy
	FcRangeCreateDouble
	FcRangeCreateInteger
	FcRangeDestroy
	FcRangeGetDouble
	FcStrBasename
	FcStrBuildFilename
	FcStrCmp
	FcStrCmpIgnoreCase
	FcStrCopy
	FcStrCopyFilename
	FcStrDirname
	FcStrDowncase
	FcStrFree
	FcStrListCreate
	FcStrListDone
	FcStrListFirst
	FcStrListNext
	FcStrPlus
	FcStrSetAdd
	FcStrSetAddFilename
	FcStrSetCreate
	FcStrSetDel
	FcStrSetDestroy
	FcStrSetEqual
	FcStrSetMember
	FcStrStr
	FcStrStrIgnoreCase
	FcUcs4ToUtf8
	FcUtf16Len
	FcUtf16ToUcs4
	FcUtf8Len
	FcUtf8ToUcs4
	FcValueDestroy
	FcValueEqual
	FcValuePrint
	FcValueSave
	FcWeightFromOpenType
	FcWeightFromOpenTypeDouble
	FcWeightToOpenType
	FcWeightToOpenTypeDouble
