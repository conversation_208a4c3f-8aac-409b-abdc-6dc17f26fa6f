<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <!-- aa* -> aa -->
  <match>
    <test name="lang" compare="contains"><string>aa</string></test>
    <edit name="lang" mode="assign" binding="same"><string>aa</string></edit>
  </match>
  <!-- ab* -> ab -->
  <match>
    <test name="lang" compare="contains"><string>ab</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ab</string></edit>
  </match>
  <!-- af* -> af -->
  <match>
    <test name="lang" compare="contains"><string>af</string></test>
    <edit name="lang" mode="assign" binding="same"><string>af</string></edit>
  </match>
  <!-- agr* -> agr -->
  <match>
    <test name="lang" compare="contains"><string>agr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>agr</string></edit>
  </match>
  <!-- ak* -> ak -->
  <match>
    <test name="lang" compare="contains"><string>ak</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ak</string></edit>
  </match>
  <!-- am* -> am -->
  <match>
    <test name="lang" compare="contains"><string>am</string></test>
    <edit name="lang" mode="assign" binding="same"><string>am</string></edit>
  </match>
  <!-- an* -> an -->
  <match>
    <test name="lang" compare="contains"><string>an</string></test>
    <edit name="lang" mode="assign" binding="same"><string>an</string></edit>
  </match>
  <!-- anp* -> anp -->
  <match>
    <test name="lang" compare="contains"><string>anp</string></test>
    <edit name="lang" mode="assign" binding="same"><string>anp</string></edit>
  </match>
  <!-- ar* -> ar -->
  <match>
    <test name="lang" compare="contains"><string>ar</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ar</string></edit>
  </match>
  <!-- as* -> as -->
  <match>
    <test name="lang" compare="contains"><string>as</string></test>
    <edit name="lang" mode="assign" binding="same"><string>as</string></edit>
  </match>
  <!-- ast* -> ast -->
  <match>
    <test name="lang" compare="contains"><string>ast</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ast</string></edit>
  </match>
  <!-- av* -> av -->
  <match>
    <test name="lang" compare="contains"><string>av</string></test>
    <edit name="lang" mode="assign" binding="same"><string>av</string></edit>
  </match>
  <!-- ay* -> ay -->
  <match>
    <test name="lang" compare="contains"><string>ay</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ay</string></edit>
  </match>
  <!-- ayc* -> ayc -->
  <match>
    <test name="lang" compare="contains"><string>ayc</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ayc</string></edit>
  </match>
  <!-- ba* -> ba -->
  <match>
    <test name="lang" compare="contains"><string>ba</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ba</string></edit>
  </match>
  <!-- be* -> be -->
  <match>
    <test name="lang" compare="contains"><string>be</string></test>
    <edit name="lang" mode="assign" binding="same"><string>be</string></edit>
  </match>
  <!-- bem* -> bem -->
  <match>
    <test name="lang" compare="contains"><string>bem</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bem</string></edit>
  </match>
  <!-- bg* -> bg -->
  <match>
    <test name="lang" compare="contains"><string>bg</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bg</string></edit>
  </match>
  <!-- bh* -> bh -->
  <match>
    <test name="lang" compare="contains"><string>bh</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bh</string></edit>
  </match>
  <!-- bhb* -> bhb -->
  <match>
    <test name="lang" compare="contains"><string>bhb</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bhb</string></edit>
  </match>
  <!-- bho* -> bho -->
  <match>
    <test name="lang" compare="contains"><string>bho</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bho</string></edit>
  </match>
  <!-- bi* -> bi -->
  <match>
    <test name="lang" compare="contains"><string>bi</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bi</string></edit>
  </match>
  <!-- bin* -> bin -->
  <match>
    <test name="lang" compare="contains"><string>bin</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bin</string></edit>
  </match>
  <!-- bm* -> bm -->
  <match>
    <test name="lang" compare="contains"><string>bm</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bm</string></edit>
  </match>
  <!-- bn* -> bn -->
  <match>
    <test name="lang" compare="contains"><string>bn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bn</string></edit>
  </match>
  <!-- bo* -> bo -->
  <match>
    <test name="lang" compare="contains"><string>bo</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bo</string></edit>
  </match>
  <!-- br* -> br -->
  <match>
    <test name="lang" compare="contains"><string>br</string></test>
    <edit name="lang" mode="assign" binding="same"><string>br</string></edit>
  </match>
  <!-- brx* -> brx -->
  <match>
    <test name="lang" compare="contains"><string>brx</string></test>
    <edit name="lang" mode="assign" binding="same"><string>brx</string></edit>
  </match>
  <!-- bs* -> bs -->
  <match>
    <test name="lang" compare="contains"><string>bs</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bs</string></edit>
  </match>
  <!-- bua* -> bua -->
  <match>
    <test name="lang" compare="contains"><string>bua</string></test>
    <edit name="lang" mode="assign" binding="same"><string>bua</string></edit>
  </match>
  <!-- byn* -> byn -->
  <match>
    <test name="lang" compare="contains"><string>byn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>byn</string></edit>
  </match>
  <!-- ca* -> ca -->
  <match>
    <test name="lang" compare="contains"><string>ca</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ca</string></edit>
  </match>
  <!-- ce* -> ce -->
  <match>
    <test name="lang" compare="contains"><string>ce</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ce</string></edit>
  </match>
  <!-- ch* -> ch -->
  <match>
    <test name="lang" compare="contains"><string>ch</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ch</string></edit>
  </match>
  <!-- chm* -> chm -->
  <match>
    <test name="lang" compare="contains"><string>chm</string></test>
    <edit name="lang" mode="assign" binding="same"><string>chm</string></edit>
  </match>
  <!-- chr* -> chr -->
  <match>
    <test name="lang" compare="contains"><string>chr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>chr</string></edit>
  </match>
  <!-- ckb* -> ckb -->
  <match>
    <test name="lang" compare="contains"><string>ckb</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ckb</string></edit>
  </match>
  <!-- cmn* -> cmn -->
  <match>
    <test name="lang" compare="contains"><string>cmn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>cmn</string></edit>
  </match>
  <!-- co* -> co -->
  <match>
    <test name="lang" compare="contains"><string>co</string></test>
    <edit name="lang" mode="assign" binding="same"><string>co</string></edit>
  </match>
  <!-- cop* -> cop -->
  <match>
    <test name="lang" compare="contains"><string>cop</string></test>
    <edit name="lang" mode="assign" binding="same"><string>cop</string></edit>
  </match>
  <!-- crh* -> crh -->
  <match>
    <test name="lang" compare="contains"><string>crh</string></test>
    <edit name="lang" mode="assign" binding="same"><string>crh</string></edit>
  </match>
  <!-- cs* -> cs -->
  <match>
    <test name="lang" compare="contains"><string>cs</string></test>
    <edit name="lang" mode="assign" binding="same"><string>cs</string></edit>
  </match>
  <!-- csb* -> csb -->
  <match>
    <test name="lang" compare="contains"><string>csb</string></test>
    <edit name="lang" mode="assign" binding="same"><string>csb</string></edit>
  </match>
  <!-- cu* -> cu -->
  <match>
    <test name="lang" compare="contains"><string>cu</string></test>
    <edit name="lang" mode="assign" binding="same"><string>cu</string></edit>
  </match>
  <!-- cv* -> cv -->
  <match>
    <test name="lang" compare="contains"><string>cv</string></test>
    <edit name="lang" mode="assign" binding="same"><string>cv</string></edit>
  </match>
  <!-- cy* -> cy -->
  <match>
    <test name="lang" compare="contains"><string>cy</string></test>
    <edit name="lang" mode="assign" binding="same"><string>cy</string></edit>
  </match>
  <!-- da* -> da -->
  <match>
    <test name="lang" compare="contains"><string>da</string></test>
    <edit name="lang" mode="assign" binding="same"><string>da</string></edit>
  </match>
  <!-- de* -> de -->
  <match>
    <test name="lang" compare="contains"><string>de</string></test>
    <edit name="lang" mode="assign" binding="same"><string>de</string></edit>
  </match>
  <!-- doi* -> doi -->
  <match>
    <test name="lang" compare="contains"><string>doi</string></test>
    <edit name="lang" mode="assign" binding="same"><string>doi</string></edit>
  </match>
  <!-- dsb* -> dsb -->
  <match>
    <test name="lang" compare="contains"><string>dsb</string></test>
    <edit name="lang" mode="assign" binding="same"><string>dsb</string></edit>
  </match>
  <!-- dv* -> dv -->
  <match>
    <test name="lang" compare="contains"><string>dv</string></test>
    <edit name="lang" mode="assign" binding="same"><string>dv</string></edit>
  </match>
  <!-- dz* -> dz -->
  <match>
    <test name="lang" compare="contains"><string>dz</string></test>
    <edit name="lang" mode="assign" binding="same"><string>dz</string></edit>
  </match>
  <!-- ee* -> ee -->
  <match>
    <test name="lang" compare="contains"><string>ee</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ee</string></edit>
  </match>
  <!-- el* -> el -->
  <match>
    <test name="lang" compare="contains"><string>el</string></test>
    <edit name="lang" mode="assign" binding="same"><string>el</string></edit>
  </match>
  <!-- en* -> en -->
  <match>
    <test name="lang" compare="contains"><string>en</string></test>
    <edit name="lang" mode="assign" binding="same"><string>en</string></edit>
  </match>
  <!-- eo* -> eo -->
  <match>
    <test name="lang" compare="contains"><string>eo</string></test>
    <edit name="lang" mode="assign" binding="same"><string>eo</string></edit>
  </match>
  <!-- es* -> es -->
  <match>
    <test name="lang" compare="contains"><string>es</string></test>
    <edit name="lang" mode="assign" binding="same"><string>es</string></edit>
  </match>
  <!-- et* -> et -->
  <match>
    <test name="lang" compare="contains"><string>et</string></test>
    <edit name="lang" mode="assign" binding="same"><string>et</string></edit>
  </match>
  <!-- eu* -> eu -->
  <match>
    <test name="lang" compare="contains"><string>eu</string></test>
    <edit name="lang" mode="assign" binding="same"><string>eu</string></edit>
  </match>
  <!-- fa* -> fa -->
  <match>
    <test name="lang" compare="contains"><string>fa</string></test>
    <edit name="lang" mode="assign" binding="same"><string>fa</string></edit>
  </match>
  <!-- fat* -> fat -->
  <match>
    <test name="lang" compare="contains"><string>fat</string></test>
    <edit name="lang" mode="assign" binding="same"><string>fat</string></edit>
  </match>
  <!-- ff* -> ff -->
  <match>
    <test name="lang" compare="contains"><string>ff</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ff</string></edit>
  </match>
  <!-- fi* -> fi -->
  <match>
    <test name="lang" compare="contains"><string>fi</string></test>
    <edit name="lang" mode="assign" binding="same"><string>fi</string></edit>
  </match>
  <!-- fil* -> fil -->
  <match>
    <test name="lang" compare="contains"><string>fil</string></test>
    <edit name="lang" mode="assign" binding="same"><string>fil</string></edit>
  </match>
  <!-- fj* -> fj -->
  <match>
    <test name="lang" compare="contains"><string>fj</string></test>
    <edit name="lang" mode="assign" binding="same"><string>fj</string></edit>
  </match>
  <!-- fo* -> fo -->
  <match>
    <test name="lang" compare="contains"><string>fo</string></test>
    <edit name="lang" mode="assign" binding="same"><string>fo</string></edit>
  </match>
  <!-- fr* -> fr -->
  <match>
    <test name="lang" compare="contains"><string>fr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>fr</string></edit>
  </match>
  <!-- fur* -> fur -->
  <match>
    <test name="lang" compare="contains"><string>fur</string></test>
    <edit name="lang" mode="assign" binding="same"><string>fur</string></edit>
  </match>
  <!-- fy* -> fy -->
  <match>
    <test name="lang" compare="contains"><string>fy</string></test>
    <edit name="lang" mode="assign" binding="same"><string>fy</string></edit>
  </match>
  <!-- ga* -> ga -->
  <match>
    <test name="lang" compare="contains"><string>ga</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ga</string></edit>
  </match>
  <!-- gd* -> gd -->
  <match>
    <test name="lang" compare="contains"><string>gd</string></test>
    <edit name="lang" mode="assign" binding="same"><string>gd</string></edit>
  </match>
  <!-- gez* -> gez -->
  <match>
    <test name="lang" compare="contains"><string>gez</string></test>
    <edit name="lang" mode="assign" binding="same"><string>gez</string></edit>
  </match>
  <!-- gl* -> gl -->
  <match>
    <test name="lang" compare="contains"><string>gl</string></test>
    <edit name="lang" mode="assign" binding="same"><string>gl</string></edit>
  </match>
  <!-- gn* -> gn -->
  <match>
    <test name="lang" compare="contains"><string>gn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>gn</string></edit>
  </match>
  <!-- got* -> got -->
  <match>
    <test name="lang" compare="contains"><string>got</string></test>
    <edit name="lang" mode="assign" binding="same"><string>got</string></edit>
  </match>
  <!-- gu* -> gu -->
  <match>
    <test name="lang" compare="contains"><string>gu</string></test>
    <edit name="lang" mode="assign" binding="same"><string>gu</string></edit>
  </match>
  <!-- gv* -> gv -->
  <match>
    <test name="lang" compare="contains"><string>gv</string></test>
    <edit name="lang" mode="assign" binding="same"><string>gv</string></edit>
  </match>
  <!-- ha* -> ha -->
  <match>
    <test name="lang" compare="contains"><string>ha</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ha</string></edit>
  </match>
  <!-- hak* -> hak -->
  <match>
    <test name="lang" compare="contains"><string>hak</string></test>
    <edit name="lang" mode="assign" binding="same"><string>hak</string></edit>
  </match>
  <!-- haw* -> haw -->
  <match>
    <test name="lang" compare="contains"><string>haw</string></test>
    <edit name="lang" mode="assign" binding="same"><string>haw</string></edit>
  </match>
  <!-- he* -> he -->
  <match>
    <test name="lang" compare="contains"><string>he</string></test>
    <edit name="lang" mode="assign" binding="same"><string>he</string></edit>
  </match>
  <!-- hi* -> hi -->
  <match>
    <test name="lang" compare="contains"><string>hi</string></test>
    <edit name="lang" mode="assign" binding="same"><string>hi</string></edit>
  </match>
  <!-- hif* -> hif -->
  <match>
    <test name="lang" compare="contains"><string>hif</string></test>
    <edit name="lang" mode="assign" binding="same"><string>hif</string></edit>
  </match>
  <!-- hne* -> hne -->
  <match>
    <test name="lang" compare="contains"><string>hne</string></test>
    <edit name="lang" mode="assign" binding="same"><string>hne</string></edit>
  </match>
  <!-- ho* -> ho -->
  <match>
    <test name="lang" compare="contains"><string>ho</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ho</string></edit>
  </match>
  <!-- hr* -> hr -->
  <match>
    <test name="lang" compare="contains"><string>hr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>hr</string></edit>
  </match>
  <!-- hsb* -> hsb -->
  <match>
    <test name="lang" compare="contains"><string>hsb</string></test>
    <edit name="lang" mode="assign" binding="same"><string>hsb</string></edit>
  </match>
  <!-- ht* -> ht -->
  <match>
    <test name="lang" compare="contains"><string>ht</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ht</string></edit>
  </match>
  <!-- hu* -> hu -->
  <match>
    <test name="lang" compare="contains"><string>hu</string></test>
    <edit name="lang" mode="assign" binding="same"><string>hu</string></edit>
  </match>
  <!-- hy* -> hy -->
  <match>
    <test name="lang" compare="contains"><string>hy</string></test>
    <edit name="lang" mode="assign" binding="same"><string>hy</string></edit>
  </match>
  <!-- hz* -> hz -->
  <match>
    <test name="lang" compare="contains"><string>hz</string></test>
    <edit name="lang" mode="assign" binding="same"><string>hz</string></edit>
  </match>
  <!-- ia* -> ia -->
  <match>
    <test name="lang" compare="contains"><string>ia</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ia</string></edit>
  </match>
  <!-- id* -> id -->
  <match>
    <test name="lang" compare="contains"><string>id</string></test>
    <edit name="lang" mode="assign" binding="same"><string>id</string></edit>
  </match>
  <!-- ie* -> ie -->
  <match>
    <test name="lang" compare="contains"><string>ie</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ie</string></edit>
  </match>
  <!-- ig* -> ig -->
  <match>
    <test name="lang" compare="contains"><string>ig</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ig</string></edit>
  </match>
  <!-- ii* -> ii -->
  <match>
    <test name="lang" compare="contains"><string>ii</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ii</string></edit>
  </match>
  <!-- ik* -> ik -->
  <match>
    <test name="lang" compare="contains"><string>ik</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ik</string></edit>
  </match>
  <!-- io* -> io -->
  <match>
    <test name="lang" compare="contains"><string>io</string></test>
    <edit name="lang" mode="assign" binding="same"><string>io</string></edit>
  </match>
  <!-- is* -> is -->
  <match>
    <test name="lang" compare="contains"><string>is</string></test>
    <edit name="lang" mode="assign" binding="same"><string>is</string></edit>
  </match>
  <!-- it* -> it -->
  <match>
    <test name="lang" compare="contains"><string>it</string></test>
    <edit name="lang" mode="assign" binding="same"><string>it</string></edit>
  </match>
  <!-- iu* -> iu -->
  <match>
    <test name="lang" compare="contains"><string>iu</string></test>
    <edit name="lang" mode="assign" binding="same"><string>iu</string></edit>
  </match>
  <!-- ja* -> ja -->
  <match>
    <test name="lang" compare="contains"><string>ja</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ja</string></edit>
  </match>
  <!-- jv* -> jv -->
  <match>
    <test name="lang" compare="contains"><string>jv</string></test>
    <edit name="lang" mode="assign" binding="same"><string>jv</string></edit>
  </match>
  <!-- ka* -> ka -->
  <match>
    <test name="lang" compare="contains"><string>ka</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ka</string></edit>
  </match>
  <!-- kaa* -> kaa -->
  <match>
    <test name="lang" compare="contains"><string>kaa</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kaa</string></edit>
  </match>
  <!-- kab* -> kab -->
  <match>
    <test name="lang" compare="contains"><string>kab</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kab</string></edit>
  </match>
  <!-- ki* -> ki -->
  <match>
    <test name="lang" compare="contains"><string>ki</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ki</string></edit>
  </match>
  <!-- kj* -> kj -->
  <match>
    <test name="lang" compare="contains"><string>kj</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kj</string></edit>
  </match>
  <!-- kk* -> kk -->
  <match>
    <test name="lang" compare="contains"><string>kk</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kk</string></edit>
  </match>
  <!-- kl* -> kl -->
  <match>
    <test name="lang" compare="contains"><string>kl</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kl</string></edit>
  </match>
  <!-- km* -> km -->
  <match>
    <test name="lang" compare="contains"><string>km</string></test>
    <edit name="lang" mode="assign" binding="same"><string>km</string></edit>
  </match>
  <!-- kn* -> kn -->
  <match>
    <test name="lang" compare="contains"><string>kn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kn</string></edit>
  </match>
  <!-- ko* -> ko -->
  <match>
    <test name="lang" compare="contains"><string>ko</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ko</string></edit>
  </match>
  <!-- kok* -> kok -->
  <match>
    <test name="lang" compare="contains"><string>kok</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kok</string></edit>
  </match>
  <!-- kr* -> kr -->
  <match>
    <test name="lang" compare="contains"><string>kr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kr</string></edit>
  </match>
  <!-- ks* -> ks -->
  <match>
    <test name="lang" compare="contains"><string>ks</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ks</string></edit>
  </match>
  <!-- kum* -> kum -->
  <match>
    <test name="lang" compare="contains"><string>kum</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kum</string></edit>
  </match>
  <!-- kv* -> kv -->
  <match>
    <test name="lang" compare="contains"><string>kv</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kv</string></edit>
  </match>
  <!-- kw* -> kw -->
  <match>
    <test name="lang" compare="contains"><string>kw</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kw</string></edit>
  </match>
  <!-- kwm* -> kwm -->
  <match>
    <test name="lang" compare="contains"><string>kwm</string></test>
    <edit name="lang" mode="assign" binding="same"><string>kwm</string></edit>
  </match>
  <!-- ky* -> ky -->
  <match>
    <test name="lang" compare="contains"><string>ky</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ky</string></edit>
  </match>
  <!-- la* -> la -->
  <match>
    <test name="lang" compare="contains"><string>la</string></test>
    <edit name="lang" mode="assign" binding="same"><string>la</string></edit>
  </match>
  <!-- lah* -> lah -->
  <match>
    <test name="lang" compare="contains"><string>lah</string></test>
    <edit name="lang" mode="assign" binding="same"><string>lah</string></edit>
  </match>
  <!-- lb* -> lb -->
  <match>
    <test name="lang" compare="contains"><string>lb</string></test>
    <edit name="lang" mode="assign" binding="same"><string>lb</string></edit>
  </match>
  <!-- lez* -> lez -->
  <match>
    <test name="lang" compare="contains"><string>lez</string></test>
    <edit name="lang" mode="assign" binding="same"><string>lez</string></edit>
  </match>
  <!-- lg* -> lg -->
  <match>
    <test name="lang" compare="contains"><string>lg</string></test>
    <edit name="lang" mode="assign" binding="same"><string>lg</string></edit>
  </match>
  <!-- li* -> li -->
  <match>
    <test name="lang" compare="contains"><string>li</string></test>
    <edit name="lang" mode="assign" binding="same"><string>li</string></edit>
  </match>
  <!-- lij* -> lij -->
  <match>
    <test name="lang" compare="contains"><string>lij</string></test>
    <edit name="lang" mode="assign" binding="same"><string>lij</string></edit>
  </match>
  <!-- ln* -> ln -->
  <match>
    <test name="lang" compare="contains"><string>ln</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ln</string></edit>
  </match>
  <!-- lo* -> lo -->
  <match>
    <test name="lang" compare="contains"><string>lo</string></test>
    <edit name="lang" mode="assign" binding="same"><string>lo</string></edit>
  </match>
  <!-- lt* -> lt -->
  <match>
    <test name="lang" compare="contains"><string>lt</string></test>
    <edit name="lang" mode="assign" binding="same"><string>lt</string></edit>
  </match>
  <!-- lv* -> lv -->
  <match>
    <test name="lang" compare="contains"><string>lv</string></test>
    <edit name="lang" mode="assign" binding="same"><string>lv</string></edit>
  </match>
  <!-- lzh* -> lzh -->
  <match>
    <test name="lang" compare="contains"><string>lzh</string></test>
    <edit name="lang" mode="assign" binding="same"><string>lzh</string></edit>
  </match>
  <!-- mag* -> mag -->
  <match>
    <test name="lang" compare="contains"><string>mag</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mag</string></edit>
  </match>
  <!-- mai* -> mai -->
  <match>
    <test name="lang" compare="contains"><string>mai</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mai</string></edit>
  </match>
  <!-- mfe* -> mfe -->
  <match>
    <test name="lang" compare="contains"><string>mfe</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mfe</string></edit>
  </match>
  <!-- mg* -> mg -->
  <match>
    <test name="lang" compare="contains"><string>mg</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mg</string></edit>
  </match>
  <!-- mh* -> mh -->
  <match>
    <test name="lang" compare="contains"><string>mh</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mh</string></edit>
  </match>
  <!-- mhr* -> mhr -->
  <match>
    <test name="lang" compare="contains"><string>mhr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mhr</string></edit>
  </match>
  <!-- mi* -> mi -->
  <match>
    <test name="lang" compare="contains"><string>mi</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mi</string></edit>
  </match>
  <!-- miq* -> miq -->
  <match>
    <test name="lang" compare="contains"><string>miq</string></test>
    <edit name="lang" mode="assign" binding="same"><string>miq</string></edit>
  </match>
  <!-- mjw* -> mjw -->
  <match>
    <test name="lang" compare="contains"><string>mjw</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mjw</string></edit>
  </match>
  <!-- mk* -> mk -->
  <match>
    <test name="lang" compare="contains"><string>mk</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mk</string></edit>
  </match>
  <!-- ml* -> ml -->
  <match>
    <test name="lang" compare="contains"><string>ml</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ml</string></edit>
  </match>
  <!-- mni* -> mni -->
  <match>
    <test name="lang" compare="contains"><string>mni</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mni</string></edit>
  </match>
  <!-- mnw* -> mnw -->
  <match>
    <test name="lang" compare="contains"><string>mnw</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mnw</string></edit>
  </match>
  <!-- mo* -> mo -->
  <match>
    <test name="lang" compare="contains"><string>mo</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mo</string></edit>
  </match>
  <!-- mr* -> mr -->
  <match>
    <test name="lang" compare="contains"><string>mr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mr</string></edit>
  </match>
  <!-- ms* -> ms -->
  <match>
    <test name="lang" compare="contains"><string>ms</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ms</string></edit>
  </match>
  <!-- mt* -> mt -->
  <match>
    <test name="lang" compare="contains"><string>mt</string></test>
    <edit name="lang" mode="assign" binding="same"><string>mt</string></edit>
  </match>
  <!-- my* -> my -->
  <match>
    <test name="lang" compare="contains"><string>my</string></test>
    <edit name="lang" mode="assign" binding="same"><string>my</string></edit>
  </match>
  <!-- na* -> na -->
  <match>
    <test name="lang" compare="contains"><string>na</string></test>
    <edit name="lang" mode="assign" binding="same"><string>na</string></edit>
  </match>
  <!-- nan* -> nan -->
  <match>
    <test name="lang" compare="contains"><string>nan</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nan</string></edit>
  </match>
  <!-- nb* -> nb -->
  <match>
    <test name="lang" compare="contains"><string>nb</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nb</string></edit>
  </match>
  <!-- nds* -> nds -->
  <match>
    <test name="lang" compare="contains"><string>nds</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nds</string></edit>
  </match>
  <!-- ne* -> ne -->
  <match>
    <test name="lang" compare="contains"><string>ne</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ne</string></edit>
  </match>
  <!-- ng* -> ng -->
  <match>
    <test name="lang" compare="contains"><string>ng</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ng</string></edit>
  </match>
  <!-- nhn* -> nhn -->
  <match>
    <test name="lang" compare="contains"><string>nhn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nhn</string></edit>
  </match>
  <!-- niu* -> niu -->
  <match>
    <test name="lang" compare="contains"><string>niu</string></test>
    <edit name="lang" mode="assign" binding="same"><string>niu</string></edit>
  </match>
  <!-- nl* -> nl -->
  <match>
    <test name="lang" compare="contains"><string>nl</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nl</string></edit>
  </match>
  <!-- nn* -> nn -->
  <match>
    <test name="lang" compare="contains"><string>nn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nn</string></edit>
  </match>
  <!-- no* -> no -->
  <match>
    <test name="lang" compare="contains"><string>no</string></test>
    <edit name="lang" mode="assign" binding="same"><string>no</string></edit>
  </match>
  <!-- nqo* -> nqo -->
  <match>
    <test name="lang" compare="contains"><string>nqo</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nqo</string></edit>
  </match>
  <!-- nr* -> nr -->
  <match>
    <test name="lang" compare="contains"><string>nr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nr</string></edit>
  </match>
  <!-- nso* -> nso -->
  <match>
    <test name="lang" compare="contains"><string>nso</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nso</string></edit>
  </match>
  <!-- nv* -> nv -->
  <match>
    <test name="lang" compare="contains"><string>nv</string></test>
    <edit name="lang" mode="assign" binding="same"><string>nv</string></edit>
  </match>
  <!-- ny* -> ny -->
  <match>
    <test name="lang" compare="contains"><string>ny</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ny</string></edit>
  </match>
  <!-- oc* -> oc -->
  <match>
    <test name="lang" compare="contains"><string>oc</string></test>
    <edit name="lang" mode="assign" binding="same"><string>oc</string></edit>
  </match>
  <!-- om* -> om -->
  <match>
    <test name="lang" compare="contains"><string>om</string></test>
    <edit name="lang" mode="assign" binding="same"><string>om</string></edit>
  </match>
  <!-- or* -> or -->
  <match>
    <test name="lang" compare="contains"><string>or</string></test>
    <edit name="lang" mode="assign" binding="same"><string>or</string></edit>
  </match>
  <!-- os* -> os -->
  <match>
    <test name="lang" compare="contains"><string>os</string></test>
    <edit name="lang" mode="assign" binding="same"><string>os</string></edit>
  </match>
  <!-- ota* -> ota -->
  <match>
    <test name="lang" compare="contains"><string>ota</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ota</string></edit>
  </match>
  <!-- pa* -> pa -->
  <match>
    <test name="lang" compare="contains"><string>pa</string></test>
    <edit name="lang" mode="assign" binding="same"><string>pa</string></edit>
  </match>
  <!-- pl* -> pl -->
  <match>
    <test name="lang" compare="contains"><string>pl</string></test>
    <edit name="lang" mode="assign" binding="same"><string>pl</string></edit>
  </match>
  <!-- pt* -> pt -->
  <match>
    <test name="lang" compare="contains"><string>pt</string></test>
    <edit name="lang" mode="assign" binding="same"><string>pt</string></edit>
  </match>
  <!-- qu* -> qu -->
  <match>
    <test name="lang" compare="contains"><string>qu</string></test>
    <edit name="lang" mode="assign" binding="same"><string>qu</string></edit>
  </match>
  <!-- quz* -> quz -->
  <match>
    <test name="lang" compare="contains"><string>quz</string></test>
    <edit name="lang" mode="assign" binding="same"><string>quz</string></edit>
  </match>
  <!-- raj* -> raj -->
  <match>
    <test name="lang" compare="contains"><string>raj</string></test>
    <edit name="lang" mode="assign" binding="same"><string>raj</string></edit>
  </match>
  <!-- rif* -> rif -->
  <match>
    <test name="lang" compare="contains"><string>rif</string></test>
    <edit name="lang" mode="assign" binding="same"><string>rif</string></edit>
  </match>
  <!-- rm* -> rm -->
  <match>
    <test name="lang" compare="contains"><string>rm</string></test>
    <edit name="lang" mode="assign" binding="same"><string>rm</string></edit>
  </match>
  <!-- rn* -> rn -->
  <match>
    <test name="lang" compare="contains"><string>rn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>rn</string></edit>
  </match>
  <!-- ro* -> ro -->
  <match>
    <test name="lang" compare="contains"><string>ro</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ro</string></edit>
  </match>
  <!-- ru* -> ru -->
  <match>
    <test name="lang" compare="contains"><string>ru</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ru</string></edit>
  </match>
  <!-- rw* -> rw -->
  <match>
    <test name="lang" compare="contains"><string>rw</string></test>
    <edit name="lang" mode="assign" binding="same"><string>rw</string></edit>
  </match>
  <!-- sa* -> sa -->
  <match>
    <test name="lang" compare="contains"><string>sa</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sa</string></edit>
  </match>
  <!-- sah* -> sah -->
  <match>
    <test name="lang" compare="contains"><string>sah</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sah</string></edit>
  </match>
  <!-- sat* -> sat -->
  <match>
    <test name="lang" compare="contains"><string>sat</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sat</string></edit>
  </match>
  <!-- sc* -> sc -->
  <match>
    <test name="lang" compare="contains"><string>sc</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sc</string></edit>
  </match>
  <!-- sco* -> sco -->
  <match>
    <test name="lang" compare="contains"><string>sco</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sco</string></edit>
  </match>
  <!-- sd* -> sd -->
  <match>
    <test name="lang" compare="contains"><string>sd</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sd</string></edit>
  </match>
  <!-- se* -> se -->
  <match>
    <test name="lang" compare="contains"><string>se</string></test>
    <edit name="lang" mode="assign" binding="same"><string>se</string></edit>
  </match>
  <!-- sel* -> sel -->
  <match>
    <test name="lang" compare="contains"><string>sel</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sel</string></edit>
  </match>
  <!-- sg* -> sg -->
  <match>
    <test name="lang" compare="contains"><string>sg</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sg</string></edit>
  </match>
  <!-- sgs* -> sgs -->
  <match>
    <test name="lang" compare="contains"><string>sgs</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sgs</string></edit>
  </match>
  <!-- sh* -> sh -->
  <match>
    <test name="lang" compare="contains"><string>sh</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sh</string></edit>
  </match>
  <!-- shn* -> shn -->
  <match>
    <test name="lang" compare="contains"><string>shn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>shn</string></edit>
  </match>
  <!-- shs* -> shs -->
  <match>
    <test name="lang" compare="contains"><string>shs</string></test>
    <edit name="lang" mode="assign" binding="same"><string>shs</string></edit>
  </match>
  <!-- si* -> si -->
  <match>
    <test name="lang" compare="contains"><string>si</string></test>
    <edit name="lang" mode="assign" binding="same"><string>si</string></edit>
  </match>
  <!-- sid* -> sid -->
  <match>
    <test name="lang" compare="contains"><string>sid</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sid</string></edit>
  </match>
  <!-- sk* -> sk -->
  <match>
    <test name="lang" compare="contains"><string>sk</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sk</string></edit>
  </match>
  <!-- sl* -> sl -->
  <match>
    <test name="lang" compare="contains"><string>sl</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sl</string></edit>
  </match>
  <!-- sm* -> sm -->
  <match>
    <test name="lang" compare="contains"><string>sm</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sm</string></edit>
  </match>
  <!-- sma* -> sma -->
  <match>
    <test name="lang" compare="contains"><string>sma</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sma</string></edit>
  </match>
  <!-- smj* -> smj -->
  <match>
    <test name="lang" compare="contains"><string>smj</string></test>
    <edit name="lang" mode="assign" binding="same"><string>smj</string></edit>
  </match>
  <!-- smn* -> smn -->
  <match>
    <test name="lang" compare="contains"><string>smn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>smn</string></edit>
  </match>
  <!-- sms* -> sms -->
  <match>
    <test name="lang" compare="contains"><string>sms</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sms</string></edit>
  </match>
  <!-- sn* -> sn -->
  <match>
    <test name="lang" compare="contains"><string>sn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sn</string></edit>
  </match>
  <!-- so* -> so -->
  <match>
    <test name="lang" compare="contains"><string>so</string></test>
    <edit name="lang" mode="assign" binding="same"><string>so</string></edit>
  </match>
  <!-- sq* -> sq -->
  <match>
    <test name="lang" compare="contains"><string>sq</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sq</string></edit>
  </match>
  <!-- sr* -> sr -->
  <match>
    <test name="lang" compare="contains"><string>sr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sr</string></edit>
  </match>
  <!-- ss* -> ss -->
  <match>
    <test name="lang" compare="contains"><string>ss</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ss</string></edit>
  </match>
  <!-- st* -> st -->
  <match>
    <test name="lang" compare="contains"><string>st</string></test>
    <edit name="lang" mode="assign" binding="same"><string>st</string></edit>
  </match>
  <!-- su* -> su -->
  <match>
    <test name="lang" compare="contains"><string>su</string></test>
    <edit name="lang" mode="assign" binding="same"><string>su</string></edit>
  </match>
  <!-- sv* -> sv -->
  <match>
    <test name="lang" compare="contains"><string>sv</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sv</string></edit>
  </match>
  <!-- sw* -> sw -->
  <match>
    <test name="lang" compare="contains"><string>sw</string></test>
    <edit name="lang" mode="assign" binding="same"><string>sw</string></edit>
  </match>
  <!-- syr* -> syr -->
  <match>
    <test name="lang" compare="contains"><string>syr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>syr</string></edit>
  </match>
  <!-- szl* -> szl -->
  <match>
    <test name="lang" compare="contains"><string>szl</string></test>
    <edit name="lang" mode="assign" binding="same"><string>szl</string></edit>
  </match>
  <!-- ta* -> ta -->
  <match>
    <test name="lang" compare="contains"><string>ta</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ta</string></edit>
  </match>
  <!-- tcy* -> tcy -->
  <match>
    <test name="lang" compare="contains"><string>tcy</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tcy</string></edit>
  </match>
  <!-- te* -> te -->
  <match>
    <test name="lang" compare="contains"><string>te</string></test>
    <edit name="lang" mode="assign" binding="same"><string>te</string></edit>
  </match>
  <!-- tg* -> tg -->
  <match>
    <test name="lang" compare="contains"><string>tg</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tg</string></edit>
  </match>
  <!-- th* -> th -->
  <match>
    <test name="lang" compare="contains"><string>th</string></test>
    <edit name="lang" mode="assign" binding="same"><string>th</string></edit>
  </match>
  <!-- the* -> the -->
  <match>
    <test name="lang" compare="contains"><string>the</string></test>
    <edit name="lang" mode="assign" binding="same"><string>the</string></edit>
  </match>
  <!-- tig* -> tig -->
  <match>
    <test name="lang" compare="contains"><string>tig</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tig</string></edit>
  </match>
  <!-- tk* -> tk -->
  <match>
    <test name="lang" compare="contains"><string>tk</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tk</string></edit>
  </match>
  <!-- tl* -> tl -->
  <match>
    <test name="lang" compare="contains"><string>tl</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tl</string></edit>
  </match>
  <!-- tn* -> tn -->
  <match>
    <test name="lang" compare="contains"><string>tn</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tn</string></edit>
  </match>
  <!-- to* -> to -->
  <match>
    <test name="lang" compare="contains"><string>to</string></test>
    <edit name="lang" mode="assign" binding="same"><string>to</string></edit>
  </match>
  <!-- tpi* -> tpi -->
  <match>
    <test name="lang" compare="contains"><string>tpi</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tpi</string></edit>
  </match>
  <!-- tr* -> tr -->
  <match>
    <test name="lang" compare="contains"><string>tr</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tr</string></edit>
  </match>
  <!-- ts* -> ts -->
  <match>
    <test name="lang" compare="contains"><string>ts</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ts</string></edit>
  </match>
  <!-- tt* -> tt -->
  <match>
    <test name="lang" compare="contains"><string>tt</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tt</string></edit>
  </match>
  <!-- tw* -> tw -->
  <match>
    <test name="lang" compare="contains"><string>tw</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tw</string></edit>
  </match>
  <!-- ty* -> ty -->
  <match>
    <test name="lang" compare="contains"><string>ty</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ty</string></edit>
  </match>
  <!-- tyv* -> tyv -->
  <match>
    <test name="lang" compare="contains"><string>tyv</string></test>
    <edit name="lang" mode="assign" binding="same"><string>tyv</string></edit>
  </match>
  <!-- ug* -> ug -->
  <match>
    <test name="lang" compare="contains"><string>ug</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ug</string></edit>
  </match>
  <!-- uk* -> uk -->
  <match>
    <test name="lang" compare="contains"><string>uk</string></test>
    <edit name="lang" mode="assign" binding="same"><string>uk</string></edit>
  </match>
  <!-- unm* -> unm -->
  <match>
    <test name="lang" compare="contains"><string>unm</string></test>
    <edit name="lang" mode="assign" binding="same"><string>unm</string></edit>
  </match>
  <!-- ur* -> ur -->
  <match>
    <test name="lang" compare="contains"><string>ur</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ur</string></edit>
  </match>
  <!-- uz* -> uz -->
  <match>
    <test name="lang" compare="contains"><string>uz</string></test>
    <edit name="lang" mode="assign" binding="same"><string>uz</string></edit>
  </match>
  <!-- ve* -> ve -->
  <match>
    <test name="lang" compare="contains"><string>ve</string></test>
    <edit name="lang" mode="assign" binding="same"><string>ve</string></edit>
  </match>
  <!-- vi* -> vi -->
  <match>
    <test name="lang" compare="contains"><string>vi</string></test>
    <edit name="lang" mode="assign" binding="same"><string>vi</string></edit>
  </match>
  <!-- vo* -> vo -->
  <match>
    <test name="lang" compare="contains"><string>vo</string></test>
    <edit name="lang" mode="assign" binding="same"><string>vo</string></edit>
  </match>
  <!-- vot* -> vot -->
  <match>
    <test name="lang" compare="contains"><string>vot</string></test>
    <edit name="lang" mode="assign" binding="same"><string>vot</string></edit>
  </match>
  <!-- wa* -> wa -->
  <match>
    <test name="lang" compare="contains"><string>wa</string></test>
    <edit name="lang" mode="assign" binding="same"><string>wa</string></edit>
  </match>
  <!-- wae* -> wae -->
  <match>
    <test name="lang" compare="contains"><string>wae</string></test>
    <edit name="lang" mode="assign" binding="same"><string>wae</string></edit>
  </match>
  <!-- wal* -> wal -->
  <match>
    <test name="lang" compare="contains"><string>wal</string></test>
    <edit name="lang" mode="assign" binding="same"><string>wal</string></edit>
  </match>
  <!-- wen* -> wen -->
  <match>
    <test name="lang" compare="contains"><string>wen</string></test>
    <edit name="lang" mode="assign" binding="same"><string>wen</string></edit>
  </match>
  <!-- wo* -> wo -->
  <match>
    <test name="lang" compare="contains"><string>wo</string></test>
    <edit name="lang" mode="assign" binding="same"><string>wo</string></edit>
  </match>
  <!-- xh* -> xh -->
  <match>
    <test name="lang" compare="contains"><string>xh</string></test>
    <edit name="lang" mode="assign" binding="same"><string>xh</string></edit>
  </match>
  <!-- yap* -> yap -->
  <match>
    <test name="lang" compare="contains"><string>yap</string></test>
    <edit name="lang" mode="assign" binding="same"><string>yap</string></edit>
  </match>
  <!-- yi* -> yi -->
  <match>
    <test name="lang" compare="contains"><string>yi</string></test>
    <edit name="lang" mode="assign" binding="same"><string>yi</string></edit>
  </match>
  <!-- yo* -> yo -->
  <match>
    <test name="lang" compare="contains"><string>yo</string></test>
    <edit name="lang" mode="assign" binding="same"><string>yo</string></edit>
  </match>
  <!-- yue* -> yue -->
  <match>
    <test name="lang" compare="contains"><string>yue</string></test>
    <edit name="lang" mode="assign" binding="same"><string>yue</string></edit>
  </match>
  <!-- yuw* -> yuw -->
  <match>
    <test name="lang" compare="contains"><string>yuw</string></test>
    <edit name="lang" mode="assign" binding="same"><string>yuw</string></edit>
  </match>
  <!-- za* -> za -->
  <match>
    <test name="lang" compare="contains"><string>za</string></test>
    <edit name="lang" mode="assign" binding="same"><string>za</string></edit>
  </match>
  <!-- zu* -> zu -->
  <match>
    <test name="lang" compare="contains"><string>zu</string></test>
    <edit name="lang" mode="assign" binding="same"><string>zu</string></edit>
  </match>
</fontconfig>
