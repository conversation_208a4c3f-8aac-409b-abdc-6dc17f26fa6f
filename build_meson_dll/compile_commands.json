[{"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Isrc/libpatternlib_internal.a.p -Isrc -I../src -I. -I.. -Ifc-lang -I../fc-lang -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ src/libpatternlib_internal.a.p/fcpat.c.o -MF src/libpatternlib_internal.a.p/fcpat.c.o.d -o src/libpatternlib_internal.a.p/fcpat.c.o -c ../src/fcpat.c", "file": "../src/fcpat.c", "output": "src/libpatternlib_internal.a.p/fcpat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcatomic.c.o -MF libfontconfig.1.dylib.p/src_fcatomic.c.o.d -o libfontconfig.1.dylib.p/src_fcatomic.c.o -c ../src/fcatomic.c", "file": "../src/fcatomic.c", "output": "libfontconfig.1.dylib.p/src_fcatomic.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fccache.c.o -MF libfontconfig.1.dylib.p/src_fccache.c.o.d -o libfontconfig.1.dylib.p/src_fccache.c.o -c ../src/fccache.c", "file": "../src/fccache.c", "output": "libfontconfig.1.dylib.p/src_fccache.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fccfg.c.o -MF libfontconfig.1.dylib.p/src_fccfg.c.o.d -o libfontconfig.1.dylib.p/src_fccfg.c.o -c ../src/fccfg.c", "file": "../src/fccfg.c", "output": "libfontconfig.1.dylib.p/src_fccfg.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fccharset.c.o -MF libfontconfig.1.dylib.p/src_fccharset.c.o.d -o libfontconfig.1.dylib.p/src_fccharset.c.o -c ../src/fccharset.c", "file": "../src/fccharset.c", "output": "libfontconfig.1.dylib.p/src_fccharset.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fccompat.c.o -MF libfontconfig.1.dylib.p/src_fccompat.c.o.d -o libfontconfig.1.dylib.p/src_fccompat.c.o -c ../src/fccompat.c", "file": "../src/fccompat.c", "output": "libfontconfig.1.dylib.p/src_fccompat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcdbg.c.o -MF libfontconfig.1.dylib.p/src_fcdbg.c.o.d -o libfontconfig.1.dylib.p/src_fcdbg.c.o -c ../src/fcdbg.c", "file": "../src/fcdbg.c", "output": "libfontconfig.1.dylib.p/src_fcdbg.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcdefault.c.o -MF libfontconfig.1.dylib.p/src_fcdefault.c.o.d -o libfontconfig.1.dylib.p/src_fcdefault.c.o -c ../src/fcdefault.c", "file": "../src/fcdefault.c", "output": "libfontconfig.1.dylib.p/src_fcdefault.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcdir.c.o -MF libfontconfig.1.dylib.p/src_fcdir.c.o.d -o libfontconfig.1.dylib.p/src_fcdir.c.o -c ../src/fcdir.c", "file": "../src/fcdir.c", "output": "libfontconfig.1.dylib.p/src_fcdir.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcformat.c.o -MF libfontconfig.1.dylib.p/src_fcformat.c.o.d -o libfontconfig.1.dylib.p/src_fcformat.c.o -c ../src/fcformat.c", "file": "../src/fcformat.c", "output": "libfontconfig.1.dylib.p/src_fcformat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcfreetype.c.o -MF libfontconfig.1.dylib.p/src_fcfreetype.c.o.d -o libfontconfig.1.dylib.p/src_fcfreetype.c.o -c ../src/fcfreetype.c", "file": "../src/fcfreetype.c", "output": "libfontconfig.1.dylib.p/src_fcfreetype.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcfs.c.o -MF libfontconfig.1.dylib.p/src_fcfs.c.o.d -o libfontconfig.1.dylib.p/src_fcfs.c.o -c ../src/fcfs.c", "file": "../src/fcfs.c", "output": "libfontconfig.1.dylib.p/src_fcfs.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcptrlist.c.o -MF libfontconfig.1.dylib.p/src_fcptrlist.c.o.d -o libfontconfig.1.dylib.p/src_fcptrlist.c.o -c ../src/fcptrlist.c", "file": "../src/fcptrlist.c", "output": "libfontconfig.1.dylib.p/src_fcptrlist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fchash.c.o -MF libfontconfig.1.dylib.p/src_fchash.c.o.d -o libfontconfig.1.dylib.p/src_fchash.c.o -c ../src/fchash.c", "file": "../src/fchash.c", "output": "libfontconfig.1.dylib.p/src_fchash.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcinit.c.o -MF libfontconfig.1.dylib.p/src_fcinit.c.o.d -o libfontconfig.1.dylib.p/src_fcinit.c.o -c ../src/fcinit.c", "file": "../src/fcinit.c", "output": "libfontconfig.1.dylib.p/src_fcinit.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fclang.c.o -MF libfontconfig.1.dylib.p/src_fclang.c.o.d -o libfontconfig.1.dylib.p/src_fclang.c.o -c ../src/fclang.c", "file": "../src/fclang.c", "output": "libfontconfig.1.dylib.p/src_fclang.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fclist.c.o -MF libfontconfig.1.dylib.p/src_fclist.c.o.d -o libfontconfig.1.dylib.p/src_fclist.c.o -c ../src/fclist.c", "file": "../src/fclist.c", "output": "libfontconfig.1.dylib.p/src_fclist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcmatch.c.o -MF libfontconfig.1.dylib.p/src_fcmatch.c.o.d -o libfontconfig.1.dylib.p/src_fcmatch.c.o -c ../src/fcmatch.c", "file": "../src/fcmatch.c", "output": "libfontconfig.1.dylib.p/src_fcmatch.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcmatrix.c.o -MF libfontconfig.1.dylib.p/src_fcmatrix.c.o.d -o libfontconfig.1.dylib.p/src_fcmatrix.c.o -c ../src/fcmatrix.c", "file": "../src/fcmatrix.c", "output": "libfontconfig.1.dylib.p/src_fcmatrix.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcname.c.o -MF libfontconfig.1.dylib.p/src_fcname.c.o.d -o libfontconfig.1.dylib.p/src_fcname.c.o -c ../src/fcname.c", "file": "../src/fcname.c", "output": "libfontconfig.1.dylib.p/src_fcname.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcobjs.c.o -MF libfontconfig.1.dylib.p/src_fcobjs.c.o.d -o libfontconfig.1.dylib.p/src_fcobjs.c.o -c ../src/fcobjs.c", "file": "../src/fcobjs.c", "output": "libfontconfig.1.dylib.p/src_fcobjs.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcrange.c.o -MF libfontconfig.1.dylib.p/src_fcrange.c.o.d -o libfontconfig.1.dylib.p/src_fcrange.c.o -c ../src/fcrange.c", "file": "../src/fcrange.c", "output": "libfontconfig.1.dylib.p/src_fcrange.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcserialize.c.o -MF libfontconfig.1.dylib.p/src_fcserialize.c.o.d -o libfontconfig.1.dylib.p/src_fcserialize.c.o -c ../src/fcserialize.c", "file": "../src/fcserialize.c", "output": "libfontconfig.1.dylib.p/src_fcserialize.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcstat.c.o -MF libfontconfig.1.dylib.p/src_fcstat.c.o.d -o libfontconfig.1.dylib.p/src_fcstat.c.o -c ../src/fcstat.c", "file": "../src/fcstat.c", "output": "libfontconfig.1.dylib.p/src_fcstat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcstr.c.o -MF libfontconfig.1.dylib.p/src_fcstr.c.o.d -o libfontconfig.1.dylib.p/src_fcstr.c.o -c ../src/fcstr.c", "file": "../src/fcstr.c", "output": "libfontconfig.1.dylib.p/src_fcstr.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcweight.c.o -MF libfontconfig.1.dylib.p/src_fcweight.c.o.d -o libfontconfig.1.dylib.p/src_fcweight.c.o -c ../src/fcweight.c", "file": "../src/fcweight.c", "output": "libfontconfig.1.dylib.p/src_fcweight.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_fcxml.c.o -MF libfontconfig.1.dylib.p/src_fcxml.c.o.d -o libfontconfig.1.dylib.p/src_fcxml.c.o -c ../src/fcxml.c", "file": "../src/fcxml.c", "output": "libfontconfig.1.dylib.p/src_fcxml.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.1.dylib.p/src_ftglue.c.o -MF libfontconfig.1.dylib.p/src_ftglue.c.o.d -o libfontconfig.1.dylib.p/src_ftglue.c.o -c ../src/ftglue.c", "file": "../src/ftglue.c", "output": "libfontconfig.1.dylib.p/src_ftglue.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcatomic.c.o -MF libfontconfig.a.p/src_fcatomic.c.o.d -o libfontconfig.a.p/src_fcatomic.c.o -c ../src/fcatomic.c", "file": "../src/fcatomic.c", "output": "libfontconfig.a.p/src_fcatomic.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fccache.c.o -MF libfontconfig.a.p/src_fccache.c.o.d -o libfontconfig.a.p/src_fccache.c.o -c ../src/fccache.c", "file": "../src/fccache.c", "output": "libfontconfig.a.p/src_fccache.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fccfg.c.o -MF libfontconfig.a.p/src_fccfg.c.o.d -o libfontconfig.a.p/src_fccfg.c.o -c ../src/fccfg.c", "file": "../src/fccfg.c", "output": "libfontconfig.a.p/src_fccfg.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fccharset.c.o -MF libfontconfig.a.p/src_fccharset.c.o.d -o libfontconfig.a.p/src_fccharset.c.o -c ../src/fccharset.c", "file": "../src/fccharset.c", "output": "libfontconfig.a.p/src_fccharset.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fccompat.c.o -MF libfontconfig.a.p/src_fccompat.c.o.d -o libfontconfig.a.p/src_fccompat.c.o -c ../src/fccompat.c", "file": "../src/fccompat.c", "output": "libfontconfig.a.p/src_fccompat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcdbg.c.o -MF libfontconfig.a.p/src_fcdbg.c.o.d -o libfontconfig.a.p/src_fcdbg.c.o -c ../src/fcdbg.c", "file": "../src/fcdbg.c", "output": "libfontconfig.a.p/src_fcdbg.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcdefault.c.o -MF libfontconfig.a.p/src_fcdefault.c.o.d -o libfontconfig.a.p/src_fcdefault.c.o -c ../src/fcdefault.c", "file": "../src/fcdefault.c", "output": "libfontconfig.a.p/src_fcdefault.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcdir.c.o -MF libfontconfig.a.p/src_fcdir.c.o.d -o libfontconfig.a.p/src_fcdir.c.o -c ../src/fcdir.c", "file": "../src/fcdir.c", "output": "libfontconfig.a.p/src_fcdir.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcformat.c.o -MF libfontconfig.a.p/src_fcformat.c.o.d -o libfontconfig.a.p/src_fcformat.c.o -c ../src/fcformat.c", "file": "../src/fcformat.c", "output": "libfontconfig.a.p/src_fcformat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcfreetype.c.o -MF libfontconfig.a.p/src_fcfreetype.c.o.d -o libfontconfig.a.p/src_fcfreetype.c.o -c ../src/fcfreetype.c", "file": "../src/fcfreetype.c", "output": "libfontconfig.a.p/src_fcfreetype.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcfs.c.o -MF libfontconfig.a.p/src_fcfs.c.o.d -o libfontconfig.a.p/src_fcfs.c.o -c ../src/fcfs.c", "file": "../src/fcfs.c", "output": "libfontconfig.a.p/src_fcfs.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcptrlist.c.o -MF libfontconfig.a.p/src_fcptrlist.c.o.d -o libfontconfig.a.p/src_fcptrlist.c.o -c ../src/fcptrlist.c", "file": "../src/fcptrlist.c", "output": "libfontconfig.a.p/src_fcptrlist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fchash.c.o -MF libfontconfig.a.p/src_fchash.c.o.d -o libfontconfig.a.p/src_fchash.c.o -c ../src/fchash.c", "file": "../src/fchash.c", "output": "libfontconfig.a.p/src_fchash.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcinit.c.o -MF libfontconfig.a.p/src_fcinit.c.o.d -o libfontconfig.a.p/src_fcinit.c.o -c ../src/fcinit.c", "file": "../src/fcinit.c", "output": "libfontconfig.a.p/src_fcinit.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fclang.c.o -MF libfontconfig.a.p/src_fclang.c.o.d -o libfontconfig.a.p/src_fclang.c.o -c ../src/fclang.c", "file": "../src/fclang.c", "output": "libfontconfig.a.p/src_fclang.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fclist.c.o -MF libfontconfig.a.p/src_fclist.c.o.d -o libfontconfig.a.p/src_fclist.c.o -c ../src/fclist.c", "file": "../src/fclist.c", "output": "libfontconfig.a.p/src_fclist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcmatch.c.o -MF libfontconfig.a.p/src_fcmatch.c.o.d -o libfontconfig.a.p/src_fcmatch.c.o -c ../src/fcmatch.c", "file": "../src/fcmatch.c", "output": "libfontconfig.a.p/src_fcmatch.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcmatrix.c.o -MF libfontconfig.a.p/src_fcmatrix.c.o.d -o libfontconfig.a.p/src_fcmatrix.c.o -c ../src/fcmatrix.c", "file": "../src/fcmatrix.c", "output": "libfontconfig.a.p/src_fcmatrix.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcname.c.o -MF libfontconfig.a.p/src_fcname.c.o.d -o libfontconfig.a.p/src_fcname.c.o -c ../src/fcname.c", "file": "../src/fcname.c", "output": "libfontconfig.a.p/src_fcname.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcobjs.c.o -MF libfontconfig.a.p/src_fcobjs.c.o.d -o libfontconfig.a.p/src_fcobjs.c.o -c ../src/fcobjs.c", "file": "../src/fcobjs.c", "output": "libfontconfig.a.p/src_fcobjs.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcrange.c.o -MF libfontconfig.a.p/src_fcrange.c.o.d -o libfontconfig.a.p/src_fcrange.c.o -c ../src/fcrange.c", "file": "../src/fcrange.c", "output": "libfontconfig.a.p/src_fcrange.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcserialize.c.o -MF libfontconfig.a.p/src_fcserialize.c.o.d -o libfontconfig.a.p/src_fcserialize.c.o -c ../src/fcserialize.c", "file": "../src/fcserialize.c", "output": "libfontconfig.a.p/src_fcserialize.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcstat.c.o -MF libfontconfig.a.p/src_fcstat.c.o.d -o libfontconfig.a.p/src_fcstat.c.o -c ../src/fcstat.c", "file": "../src/fcstat.c", "output": "libfontconfig.a.p/src_fcstat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcstr.c.o -MF libfontconfig.a.p/src_fcstr.c.o.d -o libfontconfig.a.p/src_fcstr.c.o -c ../src/fcstr.c", "file": "../src/fcstr.c", "output": "libfontconfig.a.p/src_fcstr.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcweight.c.o -MF libfontconfig.a.p/src_fcweight.c.o.d -o libfontconfig.a.p/src_fcweight.c.o -c ../src/fcweight.c", "file": "../src/fcweight.c", "output": "libfontconfig.a.p/src_fcweight.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_fcxml.c.o -MF libfontconfig.a.p/src_fcxml.c.o.d -o libfontconfig.a.p/src_fcxml.c.o -c ../src/fcxml.c", "file": "../src/fcxml.c", "output": "libfontconfig.a.p/src_fcxml.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ libfontconfig.a.p/src_ftglue.c.o -MF libfontconfig.a.p/src_ftglue.c.o.d -o libfontconfig.a.p/src_ftglue.c.o -c ../src/ftglue.c", "file": "../src/ftglue.c", "output": "libfontconfig.a.p/src_ftglue.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ifc-cache/fc-cache.p -Ifc-cache -I../fc-cache -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ fc-cache/fc-cache.p/fc-cache.c.o -MF fc-cache/fc-cache.p/fc-cache.c.o.d -o fc-cache/fc-cache.p/fc-cache.c.o -c ../fc-cache/fc-cache.c", "file": "../fc-cache/fc-cache.c", "output": "fc-cache/fc-cache.p/fc-cache.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ifc-cat/fc-cat.p -Ifc-cat -I../fc-cat -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ fc-cat/fc-cat.p/fc-cat.c.o -MF fc-cat/fc-cat.p/fc-cat.c.o.d -o fc-cat/fc-cat.p/fc-cat.c.o -c ../fc-cat/fc-cat.c", "file": "../fc-cat/fc-cat.c", "output": "fc-cat/fc-cat.p/fc-cat.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ifc-conflist/fc-conflist.p -Ifc-conflist -I../fc-conflist -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ fc-conflist/fc-conflist.p/fc-conflist.c.o -MF fc-conflist/fc-conflist.p/fc-conflist.c.o.d -o fc-conflist/fc-conflist.p/fc-conflist.c.o -c ../fc-conflist/fc-conflist.c", "file": "../fc-conflist/fc-conflist.c", "output": "fc-conflist/fc-conflist.p/fc-conflist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ifc-list/fc-list.p -Ifc-list -I../fc-list -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ fc-list/fc-list.p/fc-list.c.o -MF fc-list/fc-list.p/fc-list.c.o.d -o fc-list/fc-list.p/fc-list.c.o -c ../fc-list/fc-list.c", "file": "../fc-list/fc-list.c", "output": "fc-list/fc-list.p/fc-list.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ifc-match/fc-match.p -Ifc-match -I../fc-match -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ fc-match/fc-match.p/fc-match.c.o -MF fc-match/fc-match.p/fc-match.c.o.d -o fc-match/fc-match.p/fc-match.c.o -c ../fc-match/fc-match.c", "file": "../fc-match/fc-match.c", "output": "fc-match/fc-match.p/fc-match.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ifc-pattern/fc-pattern.p -Ifc-pattern -I../fc-pattern -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ fc-pattern/fc-pattern.p/fc-pattern.c.o -MF fc-pattern/fc-pattern.p/fc-pattern.c.o.d -o fc-pattern/fc-pattern.p/fc-pattern.c.o -c ../fc-pattern/fc-pattern.c", "file": "../fc-pattern/fc-pattern.c", "output": "fc-pattern/fc-pattern.p/fc-pattern.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ifc-query/fc-query.p -Ifc-query -I../fc-query -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ fc-query/fc-query.p/fc-query.c.o -MF fc-query/fc-query.p/fc-query.c.o.d -o fc-query/fc-query.p/fc-query.c.o -c ../fc-query/fc-query.c", "file": "../fc-query/fc-query.c", "output": "fc-query/fc-query.p/fc-query.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ifc-scan/fc-scan.p -Ifc-scan -I../fc-scan -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ fc-scan/fc-scan.p/fc-scan.c.o -MF fc-scan/fc-scan.p/fc-scan.c.o.d -o fc-scan/fc-scan.p/fc-scan.c.o -c ../fc-scan/fc-scan.c", "file": "../fc-scan/fc-scan.c", "output": "fc-scan/fc-scan.p/fc-scan.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Ifc-validate/fc-validate.p -Ifc-validate -I../fc-validate -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ fc-validate/fc-validate.p/fc-validate.c.o -MF fc-validate/fc-validate.p/fc-validate.c.o.d -o fc-validate/fc-validate.p/fc-validate.c.o -c ../fc-validate/fc-validate.c", "file": "../fc-validate/fc-validate.c", "output": "fc-validate/fc-validate.p/fc-validate.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_bz89617.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H '-DSRCDIR=\"/Users/<USER>/work/fontconfig/test\"' -MD -MQ test/test_bz89617.p/test-bz89617.c.o -MF test/test_bz89617.p/test-bz89617.c.o.d -o test/test_bz89617.p/test-bz89617.c.o -c ../test/test-bz89617.c", "file": "../test/test-bz89617.c", "output": "test/test_bz89617.p/test-bz89617.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_bz131804.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_bz131804.p/test-bz131804.c.o -MF test/test_bz131804.p/test-bz131804.c.o.d -o test/test_bz131804.p/test-bz131804.c.o -c ../test/test-bz131804.c", "file": "../test/test-bz131804.c", "output": "test/test_bz131804.p/test-bz131804.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_bz96676.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_bz96676.p/test-bz96676.c.o -MF test/test_bz96676.p/test-bz96676.c.o.d -o test/test_bz96676.p/test-bz96676.c.o -c ../test/test-bz96676.c", "file": "../test/test-bz96676.c", "output": "test/test_bz96676.p/test-bz96676.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_name_parse.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_name_parse.p/test-name-parse.c.o -MF test/test_name_parse.p/test-name-parse.c.o.d -o test/test_name_parse.p/test-name-parse.c.o -c ../test/test-name-parse.c", "file": "../test/test-name-parse.c", "output": "test/test_name_parse.p/test-name-parse.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_bz106618.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_bz106618.p/test-bz106618.c.o -MF test/test_bz106618.p/test-bz106618.c.o.d -o test/test_bz106618.p/test-bz106618.c.o -c ../test/test-bz106618.c", "file": "../test/test-bz106618.c", "output": "test/test_bz106618.p/test-bz106618.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_bz1744377.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_bz1744377.p/test-bz1744377.c.o -MF test/test_bz1744377.p/test-bz1744377.c.o.d -o test/test_bz1744377.p/test-bz1744377.c.o -c ../test/test-bz1744377.c", "file": "../test/test-bz1744377.c", "output": "test/test_bz1744377.p/test-bz1744377.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_issue180.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_issue180.p/test-issue180.c.o -MF test/test_issue180.p/test-issue180.c.o.d -o test/test_issue180.p/test-issue180.c.o -c ../test/test-issue180.c", "file": "../test/test-issue180.c", "output": "test/test_issue180.p/test-issue180.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_family_matching.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_family_matching.p/test-family-matching.c.o -MF test/test_family_matching.p/test-family-matching.c.o.d -o test/test_family_matching.p/test-family-matching.c.o -c ../test/test-family-matching.c", "file": "../test/test-family-matching.c", "output": "test/test_family_matching.p/test-family-matching.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_ptrlist.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_ptrlist.p/test-ptrlist.c.o -MF test/test_ptrlist.p/test-ptrlist.c.o.d -o test/test_ptrlist.p/test-ptrlist.c.o -c ../test/test-ptrlist.c", "file": "../test/test-ptrlist.c", "output": "test/test_ptrlist.p/test-ptrlist.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_bz106632.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H '-DFONTFILE=\"/Users/<USER>/work/fontconfig/test/4x6.pcf\"' -MD -MQ test/test_bz106632.p/test-bz106632.c.o -MF test/test_bz106632.p/test-bz106632.c.o.d -o test/test_bz106632.p/test-bz106632.c.o -c ../test/test-bz106632.c", "file": "../test/test-bz106632.c", "output": "test/test_bz106632.p/test-bz106632.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_issue107.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_issue107.p/test-issue107.c.o -MF test/test_issue107.p/test-issue107.c.o.d -o test/test_issue107.p/test-issue107.c.o -c ../test/test-issue107.c", "file": "../test/test-issue107.c", "output": "test/test_issue107.p/test-issue107.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_crbug1004254.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_crbug1004254.p/test-crbug1004254.c.o -MF test/test_crbug1004254.p/test-crbug1004254.c.o.d -o test/test_crbug1004254.p/test-crbug1004254.c.o -c ../test/test-crbug1004254.c", "file": "../test/test-crbug1004254.c", "output": "test/test_crbug1004254.p/test-crbug1004254.c.o"}, {"directory": "/Users/<USER>/work/fontconfig/build_meson_dll", "command": "cc -Itest/test_mt_fccfg.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H -MD -MQ test/test_mt_fccfg.p/test-mt-fccfg.c.o -MF test/test_mt_fccfg.p/test-mt-fccfg.c.o.d -o test/test_mt_fccfg.p/test-mt-fccfg.c.o -c ../test/test-mt-fccfg.c", "file": "../test/test-mt-fccfg.c", "output": "test/test_mt_fccfg.p/test-mt-fccfg.c.o"}]