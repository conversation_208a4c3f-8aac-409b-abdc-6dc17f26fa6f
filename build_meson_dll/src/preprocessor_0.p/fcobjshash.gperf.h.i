%{
CUT_OUT_BEGIN
typedef __builtin_va_list va_list;
typedef __builtin_va_list __gnuc_va_list;
typedef signed char __int8_t;
typedef unsigned char __uint8_t;
typedef short __int16_t;
typedef unsigned short __uint16_t;
typedef int __int32_t;
typedef unsigned int __uint32_t;
typedef long long __int64_t;
typedef unsigned long long __uint64_t;
typedef long __darwin_intptr_t;
typedef unsigned int __darwin_natural_t;
typedef int __darwin_ct_rune_t;
typedef union {
 char __mbstate8[128];
 long long _mbstateL;
} __mbstate_t;
typedef __mbstate_t __darwin_mbstate_t;
typedef long int __darwin_ptrdiff_t;
typedef long unsigned int __darwin_size_t;
typedef __builtin_va_list __darwin_va_list;
typedef int __darwin_wchar_t;
typedef __darwin_wchar_t __darwin_rune_t;
typedef int __darwin_wint_t;
typedef unsigned long __darwin_clock_t;
typedef __uint32_t __darwin_socklen_t;
typedef long __darwin_ssize_t;
typedef long __darwin_time_t;
typedef __int64_t __darwin_blkcnt_t;
typedef __int32_t __darwin_blksize_t;
typedef __int32_t __darwin_dev_t;
typedef unsigned int __darwin_fsblkcnt_t;
typedef unsigned int __darwin_fsfilcnt_t;
typedef __uint32_t __darwin_gid_t;
typedef __uint32_t __darwin_id_t;
typedef __uint64_t __darwin_ino64_t;
typedef __darwin_ino64_t __darwin_ino_t;
typedef __darwin_natural_t __darwin_mach_port_name_t;
typedef __darwin_mach_port_name_t __darwin_mach_port_t;
typedef __uint16_t __darwin_mode_t;
typedef __int64_t __darwin_off_t;
typedef __int32_t __darwin_pid_t;
typedef __uint32_t __darwin_sigset_t;
typedef __int32_t __darwin_suseconds_t;
typedef __uint32_t __darwin_uid_t;
typedef __uint32_t __darwin_useconds_t;
typedef unsigned char __darwin_uuid_t[16];
typedef char __darwin_uuid_string_t[37];
struct __darwin_pthread_handler_rec {
 void (*__routine)(void *);
 void *__arg;
 struct __darwin_pthread_handler_rec *__next;
};
struct _opaque_pthread_attr_t {
 long __sig;
 char __opaque[56];
};
struct _opaque_pthread_cond_t {
 long __sig;
 char __opaque[40];
};
struct _opaque_pthread_condattr_t {
 long __sig;
 char __opaque[8];
};
struct _opaque_pthread_mutex_t {
 long __sig;
 char __opaque[56];
};
struct _opaque_pthread_mutexattr_t {
 long __sig;
 char __opaque[8];
};
struct _opaque_pthread_once_t {
 long __sig;
 char __opaque[8];
};
struct _opaque_pthread_rwlock_t {
 long __sig;
 char __opaque[192];
};
struct _opaque_pthread_rwlockattr_t {
 long __sig;
 char __opaque[16];
};
struct _opaque_pthread_t {
 long __sig;
 struct __darwin_pthread_handler_rec *__cleanup_stack;
 char __opaque[8176];
};
typedef struct _opaque_pthread_attr_t __darwin_pthread_attr_t;
typedef struct _opaque_pthread_cond_t __darwin_pthread_cond_t;
typedef struct _opaque_pthread_condattr_t __darwin_pthread_condattr_t;
typedef unsigned long __darwin_pthread_key_t;
typedef struct _opaque_pthread_mutex_t __darwin_pthread_mutex_t;
typedef struct _opaque_pthread_mutexattr_t __darwin_pthread_mutexattr_t;
typedef struct _opaque_pthread_once_t __darwin_pthread_once_t;
typedef struct _opaque_pthread_rwlock_t __darwin_pthread_rwlock_t;
typedef struct _opaque_pthread_rwlockattr_t __darwin_pthread_rwlockattr_t;
typedef struct _opaque_pthread_t *__darwin_pthread_t;

typedef signed char int8_t;
typedef short int16_t;
typedef int int32_t;
typedef long long int64_t;

typedef unsigned char u_int8_t;
typedef unsigned short u_int16_t;
typedef unsigned int u_int32_t;
typedef unsigned long long u_int64_t;
typedef int64_t register_t;

typedef __darwin_intptr_t intptr_t;
typedef unsigned long uintptr_t;
typedef u_int64_t user_addr_t;
typedef u_int64_t user_size_t;
typedef int64_t user_ssize_t;
typedef int64_t user_long_t;
typedef u_int64_t user_ulong_t;
typedef int64_t user_time_t;
typedef int64_t user_off_t;
typedef u_int64_t syscall_arg_t;

struct timespec
{
 __darwin_time_t tv_sec;
 long tv_nsec;
};
typedef __darwin_blkcnt_t blkcnt_t;
typedef __darwin_blksize_t blksize_t;
typedef __darwin_dev_t dev_t;
typedef __darwin_ino_t ino_t;
typedef __darwin_ino64_t ino64_t;
typedef __darwin_mode_t mode_t;
typedef __uint16_t nlink_t;
typedef __darwin_uid_t uid_t;
typedef __darwin_gid_t gid_t;
typedef __darwin_off_t off_t;
typedef __darwin_time_t time_t;
struct ostat {
 __uint16_t st_dev;
 ino_t st_ino;
 mode_t st_mode;
 nlink_t st_nlink;
 __uint16_t st_uid;
 __uint16_t st_gid;
 __uint16_t st_rdev;
 __int32_t st_size;
 struct timespec st_atimespec;
 struct timespec st_mtimespec;
 struct timespec st_ctimespec;
 __int32_t st_blksize;
 __int32_t st_blocks;
 __uint32_t st_flags;
 __uint32_t st_gen;
};
struct stat { dev_t st_dev; mode_t st_mode; nlink_t st_nlink; __darwin_ino64_t st_ino; uid_t st_uid; gid_t st_gid; dev_t st_rdev; struct timespec st_atimespec; struct timespec st_mtimespec; struct timespec st_ctimespec; struct timespec st_birthtimespec; off_t st_size; blkcnt_t st_blocks; blksize_t st_blksize; __uint32_t st_flags; __uint32_t st_gen; __int32_t st_lspare; __int64_t st_qspare[2]; };
struct stat64 { dev_t st_dev; mode_t st_mode; nlink_t st_nlink; __darwin_ino64_t st_ino; uid_t st_uid; gid_t st_gid; dev_t st_rdev; struct timespec st_atimespec; struct timespec st_mtimespec; struct timespec st_ctimespec; struct timespec st_birthtimespec; off_t st_size; blkcnt_t st_blocks; blksize_t st_blksize; __uint32_t st_flags; __uint32_t st_gen; __int32_t st_lspare; __int64_t st_qspare[2]; };
int chmod(const char *, mode_t) __asm("_" "chmod" );
int fchmod(int, mode_t) __asm("_" "fchmod" );
int fstat(int, struct stat *) __asm("_" "fstat" "$INODE64");
int lstat(const char *, struct stat *) __asm("_" "lstat" "$INODE64");
int mkdir(const char *, mode_t);
int mkfifo(const char *, mode_t);
int stat(const char *, struct stat *) __asm("_" "stat" "$INODE64");
int mknod(const char *, mode_t, dev_t);
mode_t umask(mode_t);
int fchmodat(int, const char *, mode_t, int) __attribute__((availability(macosx,introduced=10.10)));
int fstatat(int, const char *, struct stat *, int) __asm("_" "fstatat" "$INODE64") __attribute__((availability(macosx,introduced=10.10)));
int mkdirat(int, const char *, mode_t) __attribute__((availability(macosx,introduced=10.10)));
int mkfifoat(int, const char *, mode_t) __attribute__((availability(macos,introduced=13.0))) __attribute__((availability(ios,introduced=16.0))) __attribute__((availability(tvos,introduced=16.0))) __attribute__((availability(watchos,introduced=9.0)));
int mknodat(int, const char *, mode_t, dev_t) __attribute__((availability(macos,introduced=13.0))) __attribute__((availability(ios,introduced=16.0))) __attribute__((availability(tvos,introduced=16.0))) __attribute__((availability(watchos,introduced=9.0)));
int futimens(int __fd, const struct timespec __times[2]) __attribute__((availability(macosx,introduced=10.13))) __attribute__((availability(ios,introduced=11.0))) __attribute__((availability(tvos,introduced=11.0))) __attribute__((availability(watchos,introduced=4.0)));
int utimensat(int __fd, const char *__path, const struct timespec __times[2],
    int __flag) __attribute__((availability(macosx,introduced=10.13))) __attribute__((availability(ios,introduced=11.0))) __attribute__((availability(tvos,introduced=11.0))) __attribute__((availability(watchos,introduced=4.0)));
struct _filesec;
typedef struct _filesec *filesec_t;

int chflags(const char *, __uint32_t);
int chmodx_np(const char *, filesec_t);
int fchflags(int, __uint32_t);
int fchmodx_np(int, filesec_t);
int fstatx_np(int, struct stat *, filesec_t) __asm("_" "fstatx_np" "$INODE64");
int lchflags(const char *, __uint32_t) __attribute__((availability(macosx,introduced=10.5)));
int lchmod(const char *, mode_t) __attribute__((availability(macosx,introduced=10.5)));
int lstatx_np(const char *, struct stat *, filesec_t) __asm("_" "lstatx_np" "$INODE64");
int mkdirx_np(const char *, filesec_t);
int mkfifox_np(const char *, filesec_t);
int statx_np(const char *, struct stat *, filesec_t) __asm("_" "statx_np" "$INODE64");
int umaskx_np(filesec_t) __attribute__((availability(macosx,introduced=10.4,deprecated=10.6)));
int fstatx64_np(int, struct stat64 *, filesec_t) __attribute__((availability(macosx,introduced=10.5,deprecated=10.6)));
int lstatx64_np(const char *, struct stat64 *, filesec_t) __attribute__((availability(macosx,introduced=10.5,deprecated=10.6)));
int statx64_np(const char *, struct stat64 *, filesec_t) __attribute__((availability(macosx,introduced=10.5,deprecated=10.6)));
int fstat64(int, struct stat64 *) __attribute__((availability(macosx,introduced=10.5,deprecated=10.6)));
int lstat64(const char *, struct stat64 *) __attribute__((availability(macosx,introduced=10.5,deprecated=10.6)));
int stat64(const char *, struct stat64 *) __attribute__((availability(macosx,introduced=10.5,deprecated=10.6)));
static inline
__uint16_t
_OSSwapInt16(
 __uint16_t _data
 )
{
 return (__uint16_t)((_data << 8) | (_data >> 8));
}
static inline
__uint32_t
_OSSwapInt32(
 __uint32_t _data
 )
{
 return __builtin_bswap32(_data);
}
static inline
__uint64_t
_OSSwapInt64(
 __uint64_t _data
 )
{
 return __builtin_bswap64(_data);
}
typedef unsigned char u_char;
typedef unsigned short u_short;
typedef unsigned int u_int;

typedef unsigned long u_long;
typedef unsigned short ushort;
typedef unsigned int uint;
typedef u_int64_t u_quad_t;
typedef int64_t quad_t;
typedef quad_t * qaddr_t;
typedef char * caddr_t;

typedef int32_t daddr_t;
typedef u_int32_t fixpt_t;
typedef __uint32_t in_addr_t;
typedef __uint16_t in_port_t;
typedef __int32_t key_t;
typedef __darwin_id_t id_t;
typedef __darwin_pid_t pid_t;
typedef int32_t segsz_t;
typedef int32_t swblk_t;
typedef __darwin_clock_t clock_t;
typedef __darwin_size_t size_t;
typedef __darwin_ssize_t ssize_t;
typedef __darwin_useconds_t useconds_t;
typedef __darwin_suseconds_t suseconds_t;
typedef __darwin_size_t rsize_t;
typedef int errno_t;
typedef struct fd_set {
 __int32_t fds_bits[((((1024) % ((sizeof(__int32_t) * 8))) == 0) ? ((1024) / ((sizeof(__int32_t) * 8))) : (((1024) / ((sizeof(__int32_t) * 8))) + 1))];
} fd_set;
int __darwin_check_fd_set_overflow(int, const void *, int) __attribute__((availability(macosx,introduced=11.0))) __attribute__((availability(ios,introduced=14.0))) __attribute__((availability(tvos,introduced=14.0))) __attribute__((availability(watchos,introduced=7.0)));
inline __attribute__ ((__always_inline__)) int
__darwin_check_fd_set(int _a, const void *_b)
{
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunguarded-availability-new"
 if ((uintptr_t)&__darwin_check_fd_set_overflow != (uintptr_t) 0) {
  return __darwin_check_fd_set_overflow(_a, _b, 0);
 } else {
  return 1;
 }
#pragma clang diagnostic pop
}
inline __attribute__ ((__always_inline__)) int
__darwin_fd_isset(int _fd, const struct fd_set *_p)
{
 if (__darwin_check_fd_set(_fd, (const void *) _p)) {
  return _p->fds_bits[(unsigned long)_fd / (sizeof(__int32_t) * 8)] & ((__int32_t)(((unsigned long)1) << ((unsigned long)_fd % (sizeof(__int32_t) * 8))));
 }
 return 0;
}
inline __attribute__ ((__always_inline__)) void
__darwin_fd_set(int _fd, struct fd_set *const _p)
{
 if (__darwin_check_fd_set(_fd, (const void *) _p)) {
  (_p->fds_bits[(unsigned long)_fd / (sizeof(__int32_t) * 8)] |= ((__int32_t)(((unsigned long)1) << ((unsigned long)_fd % (sizeof(__int32_t) * 8)))));
 }
}
inline __attribute__ ((__always_inline__)) void
__darwin_fd_clr(int _fd, struct fd_set *const _p)
{
 if (__darwin_check_fd_set(_fd, (const void *) _p)) {
  (_p->fds_bits[(unsigned long)_fd / (sizeof(__int32_t) * 8)] &= ~((__int32_t)(((unsigned long)1) << ((unsigned long)_fd % (sizeof(__int32_t) * 8)))));
 }
}
typedef __int32_t fd_mask;
typedef __darwin_pthread_attr_t pthread_attr_t;
typedef __darwin_pthread_cond_t pthread_cond_t;
typedef __darwin_pthread_condattr_t pthread_condattr_t;
typedef __darwin_pthread_mutex_t pthread_mutex_t;
typedef __darwin_pthread_mutexattr_t pthread_mutexattr_t;
typedef __darwin_pthread_once_t pthread_once_t;
typedef __darwin_pthread_rwlock_t pthread_rwlock_t;
typedef __darwin_pthread_rwlockattr_t pthread_rwlockattr_t;
typedef __darwin_pthread_t pthread_t;
typedef __darwin_pthread_key_t pthread_key_t;
typedef __darwin_fsblkcnt_t fsblkcnt_t;
typedef __darwin_fsfilcnt_t fsfilcnt_t;
typedef unsigned char FcChar8;
typedef unsigned short FcChar16;
typedef unsigned int FcChar32;
typedef int FcBool;
typedef enum _FcType {
    FcTypeUnknown = -1,
    FcTypeVoid,
    FcTypeInteger,
    FcTypeDouble,
    FcTypeString,
    FcTypeBool,
    FcTypeMatrix,
    FcTypeCharSet,
    FcTypeFTFace,
    FcTypeLangSet,
    FcTypeRange
} FcType;
typedef struct _FcMatrix {
    double xx, xy, yx, yy;
} FcMatrix;
typedef struct _FcCharSet FcCharSet;
typedef struct _FcObjectType {
    char *object;
    FcType type;
} FcObjectType;
typedef struct _FcConstant {
    const FcChar8 *name;
    const char *object;
    int value;
} FcConstant;
typedef enum _FcResult {
    FcResultMatch,
    FcResultNoMatch,
    FcResultTypeMismatch,
    FcResultNoId,
    FcResultOutOfMemory
} FcResult;
typedef enum _FcValueBinding {
    FcValueBindingWeak,
    FcValueBindingStrong,
    FcValueBindingSame,
    FcValueBindingEnd = 2147483647
} FcValueBinding;
typedef struct _FcPattern FcPattern;
typedef struct __attribute__ ((may_alias)) _FcPatternIter {
    void *dummy1;
    void *dummy2;
} FcPatternIter;
typedef struct _FcLangSet FcLangSet;
typedef struct _FcRange FcRange;
typedef struct _FcValue {
    FcType type;
    union {
 const FcChar8 *s;
 int i;
 FcBool b;
 double d;
 const FcMatrix *m;
 const FcCharSet *c;
 void *f;
 const FcLangSet *l;
 const FcRange *r;
    } u;
} FcValue;
typedef struct _FcFontSet {
    int nfont;
    int sfont;
    FcPattern **fonts;
} FcFontSet;
typedef struct _FcObjectSet {
    int nobject;
    int sobject;
    const char **objects;
} FcObjectSet;
typedef enum _FcMatchKind {
    FcMatchPattern,
    FcMatchFont,
    FcMatchScan,
    FcMatchKindEnd,
    FcMatchKindBegin = FcMatchPattern
} FcMatchKind;
typedef enum _FcLangResult {
    FcLangEqual = 0,
    FcLangDifferentCountry = 1,
    FcLangDifferentTerritory = 1,
    FcLangDifferentLang = 2
} FcLangResult;
typedef enum _FcSetName {
    FcSetSystem = 0,
    FcSetApplication = 1
} FcSetName;
typedef struct _FcConfigFileInfoIter {
    void *dummy1;
    void *dummy2;
    void *dummy3;
} FcConfigFileInfoIter;
typedef struct _FcAtomic FcAtomic;
typedef enum { FcEndianBig,
               FcEndianLittle } FcEndian;
typedef struct _FcConfig FcConfig;
typedef struct _FcGlobalCache FcFileCache;
typedef struct _FcBlanks FcBlanks;
typedef struct _FcStrList FcStrList;
typedef struct _FcStrSet FcStrSet;
typedef struct _FcCache FcCache;
typedef void (*FcDestroyFunc) (void *data);
typedef FcBool (*FcFilterFontSetFunc) (const FcPattern *font, void *user_data);
         FcBlanks *
FcBlanksCreate (void);
         void
FcBlanksDestroy (FcBlanks *b);
         FcBool
FcBlanksAdd (FcBlanks *b, FcChar32 ucs4);
         FcBool
FcBlanksIsMember (FcBlanks *b, FcChar32 ucs4);
         const FcChar8 *
FcCacheDir (const FcCache *c);
         FcFontSet *
FcCacheCopySet (const FcCache *c);
         const FcChar8 *
FcCacheSubdir (const FcCache *c, int i);
         int
FcCacheNumSubdir (const FcCache *c);
         int
FcCacheNumFont (const FcCache *c);
         FcBool
FcDirCacheUnlink (const FcChar8 *dir, FcConfig *config);
         FcBool
FcDirCacheValid (const FcChar8 *cache_file);
         FcBool
FcDirCacheClean (const FcChar8 *cache_dir, FcBool verbose);
         void
FcCacheCreateTagFile (FcConfig *config);
         FcBool
FcDirCacheCreateUUID (FcChar8 *dir,
                      FcBool force,
                      FcConfig *config);
         FcBool
FcDirCacheDeleteUUID (const FcChar8 *dir,
                      FcConfig *config);
         FcChar8 *
FcConfigHome (void);
         FcBool
FcConfigEnableHome (FcBool enable);
         FcChar8 *
FcConfigGetFilename (FcConfig *config,
                     const FcChar8 *url);
         FcChar8 *
FcConfigFilename (const FcChar8 *url);
         FcConfig *
FcConfigCreate (void);
         FcConfig *
FcConfigReference (FcConfig *config);
         void
FcConfigDestroy (FcConfig *config);
         FcBool
FcConfigSetCurrent (FcConfig *config);
         FcConfig *
FcConfigGetCurrent (void);
         FcBool
FcConfigUptoDate (FcConfig *config);
         FcBool
FcConfigBuildFonts (FcConfig *config);
         FcStrList *
FcConfigGetFontDirs (FcConfig *config);
         FcStrList *
FcConfigGetConfigDirs (FcConfig *config);
         FcStrList *
FcConfigGetConfigFiles (FcConfig *config);
         FcChar8 *
FcConfigGetCache (FcConfig *config);
         FcBlanks *
FcConfigGetBlanks (FcConfig *config);
         FcStrList *
FcConfigGetCacheDirs (FcConfig *config);
         int
FcConfigGetRescanInterval (FcConfig *config);
         FcBool
FcConfigSetRescanInterval (FcConfig *config, int rescanInterval);
         FcFontSet *
FcConfigGetFonts (FcConfig *config,
                  FcSetName set);
         FcBool
FcConfigAcceptFont (FcConfig *config,
                    const FcPattern *font);
         FcBool
FcConfigAcceptFilter (FcConfig *config,
                      const FcPattern *font);
         FcBool
FcConfigAppFontAddFile (FcConfig *config,
                        const FcChar8 *file);
         FcBool
FcConfigAppFontAddDir (FcConfig *config,
                       const FcChar8 *dir);
         void
FcConfigAppFontClear (FcConfig *config);
         void
FcConfigPreferAppFont (FcConfig *config, FcBool flag);
         FcBool
FcConfigSubstituteWithPat (FcConfig *config,
                           FcPattern *p,
                           FcPattern *p_pat,
                           FcMatchKind kind);
         FcBool
FcConfigSubstitute (FcConfig *config,
                    FcPattern *p,
                    FcMatchKind kind);
         const FcChar8 *
FcConfigGetSysRoot (const FcConfig *config);
         void
FcConfigSetSysRoot (FcConfig *config,
                    const FcChar8 *sysroot);
         FcConfig *
FcConfigSetFontSetFilter (FcConfig *config,
                          FcFilterFontSetFunc filter_func,
                          FcDestroyFunc destroy_data_func,
                          void *user_data);
         void
FcConfigFileInfoIterInit (FcConfig *config,
                          FcConfigFileInfoIter *iter);
         FcBool
FcConfigFileInfoIterNext (FcConfig *config,
                          FcConfigFileInfoIter *iter);
         FcBool
FcConfigFileInfoIterGet (FcConfig *config,
                         FcConfigFileInfoIter *iter,
                         FcChar8 **name,
                         FcChar8 **description,
                         FcBool *enabled);
         FcCharSet *
FcCharSetCreate (void);
         FcCharSet *
FcCharSetNew (void);
         void
FcCharSetDestroy (FcCharSet *fcs);
         FcBool
FcCharSetAddChar (FcCharSet *fcs, FcChar32 ucs4);
         FcBool
FcCharSetDelChar (FcCharSet *fcs, FcChar32 ucs4);
         FcCharSet *
FcCharSetCopy (FcCharSet *src);
         FcBool
FcCharSetEqual (const FcCharSet *a, const FcCharSet *b);
         FcCharSet *
FcCharSetIntersect (const FcCharSet *a, const FcCharSet *b);
         FcCharSet *
FcCharSetUnion (const FcCharSet *a, const FcCharSet *b);
         FcCharSet *
FcCharSetSubtract (const FcCharSet *a, const FcCharSet *b);
         FcBool
FcCharSetMerge (FcCharSet *a, const FcCharSet *b, FcBool *changed);
         FcBool
FcCharSetHasChar (const FcCharSet *fcs, FcChar32 ucs4);
         FcChar32
FcCharSetCount (const FcCharSet *a);
         FcChar32
FcCharSetIntersectCount (const FcCharSet *a, const FcCharSet *b);
         FcChar32
FcCharSetSubtractCount (const FcCharSet *a, const FcCharSet *b);
         FcBool
FcCharSetIsSubset (const FcCharSet *a, const FcCharSet *b);
         FcChar32
FcCharSetFirstPage (const FcCharSet *a,
                    FcChar32 map[(256 / 32)],
                    FcChar32 *next);
         FcChar32
FcCharSetNextPage (const FcCharSet *a,
                   FcChar32 map[(256 / 32)],
                   FcChar32 *next);
         FcChar32
FcCharSetCoverage (const FcCharSet *a, FcChar32 page, FcChar32 *result);
         void
FcValuePrint (const FcValue v);
         void
FcPatternPrint (const FcPattern *p);
         void
FcFontSetPrint (const FcFontSet *s);
         FcStrSet *
FcConfigGetDefaultLangs (FcConfig *config);
         FcStrSet *
FcGetDefaultLangs (void);
         void
FcConfigSetDefaultSubstitute (FcConfig *config,
                              FcPattern *pattern);
         void
FcDefaultSubstitute (FcPattern *pattern);
         FcBool
FcFileIsDir (const FcChar8 *file);
         FcBool
FcFileScan (FcFontSet *set,
            FcStrSet *dirs,
            FcFileCache *cache,
            FcBlanks *blanks,
            const FcChar8 *file,
            FcBool force);
         FcBool
FcDirScan (FcFontSet *set,
           FcStrSet *dirs,
           FcFileCache *cache,
           FcBlanks *blanks,
           const FcChar8 *dir,
           FcBool force);
         FcBool
FcDirSave (FcFontSet *set, FcStrSet *dirs, const FcChar8 *dir);
         FcCache *
FcDirCacheLoad (const FcChar8 *dir, FcConfig *config, FcChar8 **cache_file);
         FcCache *
FcDirCacheRescan (const FcChar8 *dir, FcConfig *config);
         FcCache *
FcDirCacheRead (const FcChar8 *dir, FcBool force, FcConfig *config);
         FcCache *
FcDirCacheLoadFile (const FcChar8 *cache_file, struct stat *file_stat);
         void
FcDirCacheUnload (FcCache *cache);
         FcPattern *
FcFreeTypeQuery (const FcChar8 *file, unsigned int id, FcBlanks *blanks, int *count);
         unsigned int
FcFreeTypeQueryAll (const FcChar8 *file, unsigned int id, FcBlanks *blanks, int *count, FcFontSet *set);
         FcFontSet *
FcFontSetCreate (void);
         void
FcFontSetDestroy (FcFontSet *s);
         FcBool
FcFontSetAdd (FcFontSet *s, FcPattern *font);
         FcConfig *
FcInitLoadConfig (void);
         FcConfig *
FcInitLoadConfigAndFonts (void);
         FcBool
FcInit (void);
         void
FcFini (void);
         int
FcGetVersion (void);
         FcBool
FcInitReinitialize (void);
         FcBool
FcInitBringUptoDate (void);
         FcStrSet *
FcGetLangs (void);
         FcChar8 *
FcLangNormalize (const FcChar8 *lang);
         const FcCharSet *
FcLangGetCharSet (const FcChar8 *lang);
         FcLangSet *
FcLangSetCreate (void);
         void
FcLangSetDestroy (FcLangSet *ls);
         FcLangSet *
FcLangSetCopy (const FcLangSet *ls);
         FcBool
FcLangSetAdd (FcLangSet *ls, const FcChar8 *lang);
         FcBool
FcLangSetDel (FcLangSet *ls, const FcChar8 *lang);
         FcLangResult
FcLangSetHasLang (const FcLangSet *ls, const FcChar8 *lang);
         FcLangResult
FcLangSetCompare (const FcLangSet *lsa, const FcLangSet *lsb);
         FcBool
FcLangSetContains (const FcLangSet *lsa, const FcLangSet *lsb);
         FcBool
FcLangSetEqual (const FcLangSet *lsa, const FcLangSet *lsb);
         FcChar32
FcLangSetHash (const FcLangSet *ls);
         FcStrSet *
FcLangSetGetLangs (const FcLangSet *ls);
         FcLangSet *
FcLangSetUnion (const FcLangSet *a, const FcLangSet *b);
         FcLangSet *
FcLangSetSubtract (const FcLangSet *a, const FcLangSet *b);
         FcObjectSet *
FcObjectSetCreate (void);
         FcBool
FcObjectSetAdd (FcObjectSet *os, const char *object);
         void
FcObjectSetDestroy (FcObjectSet *os);
         FcObjectSet *
FcObjectSetVaBuild (const char *first, va_list va);
         FcObjectSet *
FcObjectSetBuild (const char *first, ...) __attribute__ ((__sentinel__ (0)));
         FcFontSet *
FcFontSetList (FcConfig *config,
               FcFontSet **sets,
               int nsets,
               FcPattern *p,
               FcObjectSet *os);
         FcFontSet *
FcFontList (FcConfig *config,
            FcPattern *p,
            FcObjectSet *os);
         FcAtomic *
FcAtomicCreate (const FcChar8 *file);
         FcBool
FcAtomicLock (FcAtomic *atomic);
         FcChar8 *
FcAtomicNewFile (FcAtomic *atomic);
         FcChar8 *
FcAtomicOrigFile (FcAtomic *atomic);
         FcBool
FcAtomicReplaceOrig (FcAtomic *atomic);
         void
FcAtomicDeleteNew (FcAtomic *atomic);
         void
FcAtomicUnlock (FcAtomic *atomic);
         void
FcAtomicDestroy (FcAtomic *atomic);
         FcPattern *
FcFontSetMatch (FcConfig *config,
                FcFontSet **sets,
                int nsets,
                FcPattern *p,
                FcResult *result);
         FcPattern *
FcFontMatch (FcConfig *config,
             FcPattern *p,
             FcResult *result);
         FcPattern *
FcFontRenderPrepare (FcConfig *config,
                     FcPattern *pat,
                     FcPattern *font);
         FcFontSet *
FcFontSetSort (FcConfig *config,
               FcFontSet **sets,
               int nsets,
               FcPattern *p,
               FcBool trim,
               FcCharSet **csp,
               FcResult *result);
         FcFontSet *
FcFontSort (FcConfig *config,
            FcPattern *p,
            FcBool trim,
            FcCharSet **csp,
            FcResult *result);
         void
FcFontSetSortDestroy (FcFontSet *fs);
         FcMatrix *
FcMatrixCopy (const FcMatrix *mat);
         FcBool
FcMatrixEqual (const FcMatrix *mat1, const FcMatrix *mat2);
         void
FcMatrixMultiply (FcMatrix *result, const FcMatrix *a, const FcMatrix *b);
         void
FcMatrixRotate (FcMatrix *m, double c, double s);
         void
FcMatrixScale (FcMatrix *m, double sx, double sy);
         void
FcMatrixShear (FcMatrix *m, double sh, double sv);
         FcBool
FcNameRegisterObjectTypes (const FcObjectType *types, int ntype);
         FcBool
FcNameUnregisterObjectTypes (const FcObjectType *types, int ntype);
         const FcObjectType *
FcNameGetObjectType (const char *object);
         FcBool
FcNameRegisterConstants (const FcConstant *consts, int nconsts);
         FcBool
FcNameUnregisterConstants (const FcConstant *consts, int nconsts);
         const FcConstant *
FcNameGetConstant (const FcChar8 *string);
         const FcConstant *
FcNameGetConstantFor (const FcChar8 *string, const char *object);
         FcBool
FcNameConstant (const FcChar8 *string, int *result);
         FcPattern *
FcNameParse (const FcChar8 *name);
         FcChar8 *
FcNameUnparse (FcPattern *pat);
         FcPattern *
FcPatternCreate (void);
         FcPattern *
FcPatternDuplicate (const FcPattern *p);
         void
FcPatternReference (FcPattern *p);
         FcPattern *
FcPatternFilter (FcPattern *p, const FcObjectSet *os);
         void
FcValueDestroy (FcValue v);
         FcBool
FcValueEqual (FcValue va, FcValue vb);
         FcValue
FcValueSave (FcValue v);
         void
FcPatternDestroy (FcPattern *p);
int
FcPatternObjectCount (const FcPattern *pat);
         FcBool
FcPatternEqual (const FcPattern *pa, const FcPattern *pb);
         FcBool
FcPatternEqualSubset (const FcPattern *pa, const FcPattern *pb, const FcObjectSet *os);
         FcChar32
FcPatternHash (const FcPattern *p);
         FcBool
FcPatternAdd (FcPattern *p, const char *object, FcValue value, FcBool append);
         FcBool
FcPatternAddWeak (FcPattern *p, const char *object, FcValue value, FcBool append);
         FcResult
FcPatternGet (const FcPattern *p, const char *object, int id, FcValue *v);
         FcResult
FcPatternGetWithBinding (const FcPattern *p, const char *object, int id, FcValue *v, FcValueBinding *b);
         FcBool
FcPatternDel (FcPattern *p, const char *object);
         FcBool
FcPatternRemove (FcPattern *p, const char *object, int id);
         FcBool
FcPatternAddInteger (FcPattern *p, const char *object, int i);
         FcBool
FcPatternAddDouble (FcPattern *p, const char *object, double d);
         FcBool
FcPatternAddString (FcPattern *p, const char *object, const FcChar8 *s);
         FcBool
FcPatternAddMatrix (FcPattern *p, const char *object, const FcMatrix *s);
         FcBool
FcPatternAddCharSet (FcPattern *p, const char *object, const FcCharSet *c);
         FcBool
FcPatternAddBool (FcPattern *p, const char *object, FcBool b);
         FcBool
FcPatternAddLangSet (FcPattern *p, const char *object, const FcLangSet *ls);
         FcBool
FcPatternAddRange (FcPattern *p, const char *object, const FcRange *r);
         FcResult
FcPatternGetInteger (const FcPattern *p, const char *object, int n, int *i);
         FcResult
FcPatternGetDouble (const FcPattern *p, const char *object, int n, double *d);
         FcResult
FcPatternGetString (const FcPattern *p, const char *object, int n, FcChar8 **s);
         FcResult
FcPatternGetMatrix (const FcPattern *p, const char *object, int n, FcMatrix **s);
         FcResult
FcPatternGetCharSet (const FcPattern *p, const char *object, int n, FcCharSet **c);
         FcResult
FcPatternGetBool (const FcPattern *p, const char *object, int n, FcBool *b);
         FcResult
FcPatternGetLangSet (const FcPattern *p, const char *object, int n, FcLangSet **ls);
         FcResult
FcPatternGetRange (const FcPattern *p, const char *object, int id, FcRange **r);
         FcPattern *
FcPatternVaBuild (FcPattern *p, va_list va);
         FcPattern *
FcPatternBuild (FcPattern *p, ...) __attribute__ ((__sentinel__ (0)));
         FcChar8 *
FcPatternFormat (FcPattern *pat, const FcChar8 *format);
         FcRange *
FcRangeCreateDouble (double begin, double end);
         FcRange *
FcRangeCreateInteger (FcChar32 begin, FcChar32 end);
         void
FcRangeDestroy (FcRange *range);
         FcRange *
FcRangeCopy (const FcRange *r);
         FcBool
FcRangeGetDouble (const FcRange *range, double *begin, double *end);
         void
FcPatternIterStart (const FcPattern *pat, FcPatternIter *iter);
         FcBool
FcPatternIterNext (const FcPattern *pat, FcPatternIter *iter);
         FcBool
FcPatternIterEqual (const FcPattern *p1, FcPatternIter *i1,
                    const FcPattern *p2, FcPatternIter *i2);
         FcBool
FcPatternFindIter (const FcPattern *pat, FcPatternIter *iter, const char *object);
         FcBool
FcPatternIterIsValid (const FcPattern *pat, FcPatternIter *iter);
         const char *
FcPatternIterGetObject (const FcPattern *pat, FcPatternIter *iter);
         int
FcPatternIterValueCount (const FcPattern *pat, FcPatternIter *iter);
         FcResult
FcPatternIterGetValue (const FcPattern *pat, FcPatternIter *iter, int id, FcValue *v, FcValueBinding *b);
         int
FcWeightFromOpenType (int ot_weight);
         double
FcWeightFromOpenTypeDouble (double ot_weight);
         int
FcWeightToOpenType (int fc_weight);
         double
FcWeightToOpenTypeDouble (double fc_weight);
         FcChar8 *
FcStrCopy (const FcChar8 *s);
         FcChar8 *
FcStrCopyFilename (const FcChar8 *s);
         FcChar8 *
FcStrPlus (const FcChar8 *s1, const FcChar8 *s2);
         void
FcStrFree (FcChar8 *s);
         FcChar8 *
FcStrDowncase (const FcChar8 *s);
         int
FcStrCmpIgnoreCase (const FcChar8 *s1, const FcChar8 *s2);
         int
FcStrCmp (const FcChar8 *s1, const FcChar8 *s2);
         const FcChar8 *
FcStrStrIgnoreCase (const FcChar8 *s1, const FcChar8 *s2);
         const FcChar8 *
FcStrStr (const FcChar8 *s1, const FcChar8 *s2);
         int
FcUtf8ToUcs4 (const FcChar8 *src_orig,
              FcChar32 *dst,
              int len);
         FcBool
FcUtf8Len (const FcChar8 *string,
           int len,
           int *nchar,
           int *wchar);
         int
FcUcs4ToUtf8 (FcChar32 ucs4,
              FcChar8 dest[6]);
         int
FcUtf16ToUcs4 (const FcChar8 *src_orig,
               FcEndian endian,
               FcChar32 *dst,
               int len);
         FcBool
FcUtf16Len (const FcChar8 *string,
            FcEndian endian,
            int len,
            int *nchar,
            int *wchar);
         FcChar8 *
FcStrBuildFilename (const FcChar8 *path,
                    ...);
         FcChar8 *
FcStrDirname (const FcChar8 *file);
         FcChar8 *
FcStrBasename (const FcChar8 *file);
         FcStrSet *
FcStrSetCreate (void);
         FcBool
FcStrSetMember (FcStrSet *set, const FcChar8 *s);
         FcBool
FcStrSetEqual (FcStrSet *sa, FcStrSet *sb);
         FcBool
FcStrSetAdd (FcStrSet *set, const FcChar8 *s);
         FcBool
FcStrSetAddFilename (FcStrSet *set, const FcChar8 *s);
         FcBool
FcStrSetDel (FcStrSet *set, const FcChar8 *s);
         void
FcStrSetDestroy (FcStrSet *set);
         FcStrList *
FcStrListCreate (FcStrSet *set);
         void
FcStrListFirst (FcStrList *list);
         FcChar8 *
FcStrListNext (FcStrList *list);
         void
FcStrListDone (FcStrList *list);
         FcBool
FcConfigParseAndLoad (FcConfig *config, const FcChar8 *file, FcBool complain);
         FcBool
FcConfigParseAndLoadFromMemory (FcConfig *config,
                                const FcChar8 *buffer,
                                FcBool complain);
CUT_OUT_END
%}
%struct-type
%language=ANSI-C
%includes
%enum
%readonly-tables
%define slot-name name
%define hash-function-name FcObjectTypeHash
%define lookup-function-name FcObjectTypeLookup
%pic
%define string-pool-name FcObjectTypeNamePool
struct FcObjectTypeInfo {
 int name;
 int id;
};
%%
"family", FC_FAMILY_OBJECT
"familylang", FC_FAMILYLANG_OBJECT
"style", FC_STYLE_OBJECT
"stylelang", FC_STYLELANG_OBJECT
"fullname", FC_FULLNAME_OBJECT
"fullnamelang", FC_FULLNAMELANG_OBJECT
"slant", FC_SLANT_OBJECT
"weight", FC_WEIGHT_OBJECT
"width", FC_WIDTH_OBJECT
"size", FC_SIZE_OBJECT
"aspect", FC_ASPECT_OBJECT
"pixelsize", FC_PIXEL_SIZE_OBJECT
"spacing", FC_SPACING_OBJECT
"foundry", FC_FOUNDRY_OBJECT
"antialias", FC_ANTIALIAS_OBJECT
"hintstyle", FC_HINT_STYLE_OBJECT
"hinting", FC_HINTING_OBJECT
"verticallayout", FC_VERTICAL_LAYOUT_OBJECT
"autohint", FC_AUTOHINT_OBJECT
"globaladvance", FC_GLOBAL_ADVANCE_OBJECT
"file", FC_FILE_OBJECT
"index", FC_INDEX_OBJECT
"rasterizer", FC_RASTERIZER_OBJECT
"outline", FC_OUTLINE_OBJECT
"scalable", FC_SCALABLE_OBJECT
"dpi", FC_DPI_OBJECT
"rgba", FC_RGBA_OBJECT
"scale", FC_SCALE_OBJECT
"minspace", FC_MINSPACE_OBJECT
"charwidth", FC_CHARWIDTH_OBJECT
"charheight", FC_CHAR_HEIGHT_OBJECT
"matrix", FC_MATRIX_OBJECT
"charset", FC_CHARSET_OBJECT
"lang", FC_LANG_OBJECT
"fontversion", FC_FONTVERSION_OBJECT
"capability", FC_CAPABILITY_OBJECT
"fontformat", FC_FONTFORMAT_OBJECT
"embolden", FC_EMBOLDEN_OBJECT
"embeddedbitmap", FC_EMBEDDED_BITMAP_OBJECT
"decorative", FC_DECORATIVE_OBJECT
"lcdfilter", FC_LCD_FILTER_OBJECT
"namelang", FC_NAMELANG_OBJECT
"fontfeatures", FC_FONT_FEATURES_OBJECT
"prgname", FC_PRGNAME_OBJECT
"hash", FC_HASH_OBJECT
"postscriptname", FC_POSTSCRIPT_NAME_OBJECT
"color", FC_COLOR_OBJECT
"symbol", FC_SYMBOL_OBJECT
"fontvariations", FC_FONT_VARIATIONS_OBJECT
"variable", FC_VARIABLE_OBJECT
"fonthashint", FC_FONT_HAS_HINT_OBJECT
"order", FC_ORDER_OBJECT
"desktop", FC_DESKTOP_NAME_OBJECT
"namedinstance", FC_NAMED_INSTANCE_OBJECT
"fontwrapper", FC_FONT_WRAPPER_OBJECT

