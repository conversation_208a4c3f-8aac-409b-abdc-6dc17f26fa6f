["/Users/<USER>/work/fontconfig/meson.build", "/Users/<USER>/work/fontconfig/meson.options", "/Users/<USER>/work/fontconfig/meson-cc-tests/freetype-pcf-long-family-names.c", "/Users/<USER>/work/fontconfig/subprojects/libintl/meson.build", "/Users/<USER>/work/fontconfig/meson-cc-tests/flexible-array-member-test.c", "/Users/<USER>/work/fontconfig/meson-cc-tests/pthread-prio-inherit-test.c", "/Users/<USER>/work/fontconfig/meson-cc-tests/stdatomic-primitives-test.c", "/Users/<USER>/work/fontconfig/meson-cc-tests/intel-atomic-primitives-test.c", "/Users/<USER>/work/fontconfig/meson-cc-tests/solaris-atomic-operations.c", "/usr/bin/gperf", "/Users/<USER>/work/fontconfig/meson-cc-tests/gperf.txt", "/Users/<USER>/work/fontconfig/fontconfig/meson.build", "/Users/<USER>/work/fontconfig/fontconfig/fontconfig.h.in", "/Users/<USER>/work/fontconfig/fc-case/meson.build", "/Users/<USER>/work/fontconfig/fc-lang/meson.build", "/Users/<USER>/work/fontconfig/src/meson.build", "/Users/<USER>/work/fontconfig/fc-cache/meson.build", "/Users/<USER>/work/fontconfig/fc-cat/meson.build", "/Users/<USER>/work/fontconfig/fc-conflist/meson.build", "/Users/<USER>/work/fontconfig/fc-list/meson.build", "/Users/<USER>/work/fontconfig/fc-match/meson.build", "/Users/<USER>/work/fontconfig/fc-pattern/meson.build", "/Users/<USER>/work/fontconfig/fc-query/meson.build", "/Users/<USER>/work/fontconfig/fc-scan/meson.build", "/Users/<USER>/work/fontconfig/fc-validate/meson.build", "/Users/<USER>/work/fontconfig/test/meson.build", "/Users/<USER>/work/fontconfig/conf.d/meson.build", "/Users/<USER>/work/fontconfig/conf.d/README.in", "/Users/<USER>/work/fontconfig/its/meson.build", "/Users/<USER>/work/fontconfig/doc/meson.build", "/Users/<USER>/work/fontconfig/doc/extract-man-list.py", "/Users/<USER>/work/fontconfig/doc/fcatomic.fncs", "/Users/<USER>/work/fontconfig/doc/fcblanks.fncs", "/Users/<USER>/work/fontconfig/doc/fccache.fncs", "/Users/<USER>/work/fontconfig/doc/fccharset.fncs", "/Users/<USER>/work/fontconfig/doc/fcconfig.fncs", "/Users/<USER>/work/fontconfig/doc/fcconstant.fncs", "/Users/<USER>/work/fontconfig/doc/fcdircache.fncs", "/Users/<USER>/work/fontconfig/doc/fcfile.fncs", "/Users/<USER>/work/fontconfig/doc/fcfontset.fncs", "/Users/<USER>/work/fontconfig/doc/fcfontations.fncs", "/Users/<USER>/work/fontconfig/doc/fcformat.fncs", "/Users/<USER>/work/fontconfig/doc/fcfreetype.fncs", "/Users/<USER>/work/fontconfig/doc/fcinit.fncs", "/Users/<USER>/work/fontconfig/doc/fclangset.fncs", "/Users/<USER>/work/fontconfig/doc/fcmatrix.fncs", "/Users/<USER>/work/fontconfig/doc/fcobjectset.fncs", "/Users/<USER>/work/fontconfig/doc/fcobjecttype.fncs", "/Users/<USER>/work/fontconfig/doc/fcpattern.fncs", "/Users/<USER>/work/fontconfig/doc/fcrange.fncs", "/Users/<USER>/work/fontconfig/doc/fcstring.fncs", "/Users/<USER>/work/fontconfig/doc/fcstrset.fncs", "/Users/<USER>/work/fontconfig/doc/fcvalue.fncs", "/Users/<USER>/work/fontconfig/doc/fcweight.fncs", "/Users/<USER>/work/fontconfig/doc/cache-version.sgml.in", "/Users/<USER>/work/fontconfig/doc/version.sgml.in", "/Users/<USER>/work/fontconfig/doc/confdir.sgml.in", "/Users/<USER>/work/fontconfig/meson-config.h.in", "/Users/<USER>/work/fontconfig/fonts.conf.in"]