[{"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz89617"], "env": {}, "name": "test_bz89617", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_bz89617@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz131804"], "env": {}, "name": "test_bz131804", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_bz131804@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz96676"], "env": {}, "name": "test_bz96676", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_bz96676@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_name_parse"], "env": {}, "name": "test_name_parse", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_name_parse@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz106618"], "env": {}, "name": "test_bz106618", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_bz106618@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz1744377"], "env": {}, "name": "test_bz1744377", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_bz1744377@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_issue180"], "env": {}, "name": "test_issue180", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_issue180@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_family_matching"], "env": {}, "name": "test_family_matching", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_family_matching@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_ptrlist"], "env": {}, "name": "test_ptrlist", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_ptrlist@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz106632"], "env": {}, "name": "test_bz106632", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_bz106632@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_issue107"], "env": {}, "name": "test_issue107", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_issue107@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_crbug1004254"], "env": {}, "name": "test_crbug1004254", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": false, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_crbug1004254@exe"], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_mt_fccfg"], "env": {}, "name": "test_mt_fccfg", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": false, "priority": 0, "protocol": "exitcode", "depends": ["9f86d08@@test_mt_fccfg@exe"], "extra_paths": []}, {"cmd": ["/bin/bash", "/Users/<USER>/work/fontconfig/test/run-test.sh"], "env": {"srcdir": "/Users/<USER>/work/fontconfig/test", "builddir": "/Users/<USER>/work/fontconfig/build_meson_dll/test", "EXEEXT": "", "VERBOSE": "1"}, "name": "run_test_sh", "workdir": null, "timeout": 600, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": [], "extra_paths": []}, {"cmd": ["/Users/<USER>/work/fontconfig/doc/check-missing-doc.py", "/Users/<USER>/work/fontconfig", "/Users/<USER>/work/fontconfig/build_meson_dll"], "env": {}, "name": "check-missing-doc.py", "workdir": null, "timeout": 30, "suite": ["fontconfig"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": [], "extra_paths": []}]