[{"name": "freetype2", "type": "pkgconfig", "version": "26.2.20", "compile_args": ["-I/usr/local/opt/freetype/include/freetype2", "-I/usr/local/opt/libpng/include/libpng16"], "link_args": ["/usr/local/opt/freetype/lib/libfreetype.dylib"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": ["freetype_dep"]}, {"name": "expat", "type": "pkgconfig", "version": "2.4.1", "compile_args": [], "link_args": ["-lexpat"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": ["xml_dep"]}, {"name": "threads", "type": "system", "version": "unknown", "compile_args": [], "link_args": [], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": ["extra_deps", "thread_dep"]}, {"name": "m", "type": "library", "version": "unknown", "compile_args": [], "link_args": ["-lm"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": ["math_dep"]}, {"name": "dep325234749404405092063314029888261101389", "type": "internal", "version": "2.17.1", "compile_args": [], "link_args": [], "include_directories": ["/Users/<USER>/work/fontconfig/.", "/Users/<USER>/work/fontconfig/build_meson_dll/.", "/Users/<USER>/work/fontconfig/fc-lang", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang"], "sources": [], "extra_files": [], "dependencies": ["freetype2", "expat", "", "threads"], "depends": ["fontconfig@sha"], "meson_variables": ["fontconfig_dep"]}]