{"targets": {"/Users/<USER>/work/fontconfig/build_meson_dll/libfontconfig.1.dylib": {"destination": "{libdir_shared}/libfontconfig.1.dylib", "tag": "runtime", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fc-cache/fc-cache": {"destination": "{bindir}/fc-cache", "tag": "tools", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fc-cat/fc-cat": {"destination": "{bindir}/fc-cat", "tag": "tools", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fc-conflist/fc-conflist": {"destination": "{bindir}/fc-conflist", "tag": "tools", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fc-list/fc-list": {"destination": "{bindir}/fc-list", "tag": "tools", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fc-match/fc-match": {"destination": "{bindir}/fc-match", "tag": "tools", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fc-pattern/fc-pattern": {"destination": "{bindir}/fc-pattern", "tag": "tools", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fc-query/fc-query": {"destination": "{bindir}/fc-query", "tag": "tools", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fc-scan/fc-scan": {"destination": "{bindir}/fc-scan", "tag": "tools", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fc-validate/fc-validate": {"destination": "{bindir}/fc-validate", "tag": "tools", "subproject": null, "install_rpath": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/conf.d/35-lang-normalize.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/35-lang-normalize.conf", "tag": "runtime", "subproject": null, "install_rpath": null}}, "data": {"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/fontconfig.pc": {"destination": "{libdir}/pkgconfig/fontconfig.pc", "tag": "devel", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/05-reset-dirs-sample.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/05-reset-dirs-sample.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/09-autohint-if-no-hinting.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/09-autohint-if-no-hinting.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-autohint.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-autohint.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-hinting-full.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-hinting-full.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-hinting-medium.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-hinting-medium.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-hinting-none.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-hinting-none.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-hinting-slight.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-hinting-slight.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-no-antialias.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-no-antialias.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-scale-bitmap-fonts.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-scale-bitmap-fonts.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-bgr.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-sub-pixel-bgr.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-none.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-sub-pixel-none.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-rgb.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-sub-pixel-rgb.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-vbgr.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-sub-pixel-vbgr.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-vrgb.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-sub-pixel-vrgb.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-unhinted.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-unhinted.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/10-yes-antialias.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/10-yes-antialias.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-default.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/11-lcdfilter-default.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-legacy.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/11-lcdfilter-legacy.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-light.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/11-lcdfilter-light.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-none.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/11-lcdfilter-none.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/20-unhint-small-vera.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/20-unhint-small-vera.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/25-unhint-nonlatin.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/25-unhint-nonlatin.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/30-metric-aliases.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/30-metric-aliases.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/40-nonlatin.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/40-nonlatin.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/45-generic.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/45-generic.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/45-latin.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/45-latin.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/48-guessfamily.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/48-guessfamily.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/48-spacing.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/48-spacing.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/49-sansserif.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/49-sansserif.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/50-user.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/50-user.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/51-local.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/51-local.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/60-generic.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/60-generic.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/60-latin.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/60-latin.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/65-fonts-persian.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/65-fonts-persian.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/65-khmer.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/65-khmer.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/65-nonlatin.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/65-nonlatin.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/69-unifont.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/69-unifont.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/70-no-bitmaps.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/70-no-bitmaps.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/70-no-bitmaps-and-emoji.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/70-no-bitmaps-and-emoji.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/70-no-bitmaps-except-emoji.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/70-no-bitmaps-except-emoji.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/70-yes-bitmaps.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/70-yes-bitmaps.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/80-delicious.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/80-delicious.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/conf.d/90-synthetic.conf": {"destination": "{prefix}/share/fontconfig/conf.avail/90-synthetic.conf", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/its/fontconfig.its": {"destination": "{datadir}/gettext/its/fontconfig.its", "tag": "devel", "subproject": null}, "/Users/<USER>/work/fontconfig/its/fontconfig.loc": {"destination": "{datadir}/gettext/its/fontconfig.loc", "tag": "devel", "subproject": null}, "/Users/<USER>/work/fontconfig/fonts.dtd": {"destination": "{prefix}/share/xml/fontconfig/fonts.dtd", "tag": "runtime", "subproject": null}}, "configure": {"/Users/<USER>/work/fontconfig/build_meson_dll/conf.d/README": {"destination": "{prefix}/etc/fonts/conf.d/README", "tag": "runtime", "subproject": null}, "/Users/<USER>/work/fontconfig/build_meson_dll/fonts.conf": {"destination": "{prefix}/etc/fonts/fonts.conf", "tag": "runtime", "subproject": null}}, "headers": {"/Users/<USER>/work/fontconfig/build_meson_dll/fontconfig/fontconfig.h": {"destination": "{includedir}/fontconfig/fontconfig.h", "tag": "devel", "subproject": null}, "/Users/<USER>/work/fontconfig/fontconfig/fcfreetype.h": {"destination": "{includedir}/fontconfig/fcfreetype.h", "tag": "devel", "subproject": null}, "/Users/<USER>/work/fontconfig/fontconfig/fcprivate.h": {"destination": "{includedir}/fontconfig/fcprivate.h", "tag": "devel", "subproject": null}}}