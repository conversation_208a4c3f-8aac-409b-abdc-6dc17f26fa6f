{"/Users/<USER>/work/fontconfig/build_meson_dll/libfontconfig.1.dylib": "/usr/local/lib/libfontconfig.1.dylib", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-cache/fc-cache": "/usr/local/bin/fc-cache", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-cat/fc-cat": "/usr/local/bin/fc-cat", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-conflist/fc-conflist": "/usr/local/bin/fc-conflist", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-list/fc-list": "/usr/local/bin/fc-list", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-match/fc-match": "/usr/local/bin/fc-match", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-pattern/fc-pattern": "/usr/local/bin/fc-pattern", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-query/fc-query": "/usr/local/bin/fc-query", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-scan/fc-scan": "/usr/local/bin/fc-scan", "/Users/<USER>/work/fontconfig/build_meson_dll/fc-validate/fc-validate": "/usr/local/bin/fc-validate", "/Users/<USER>/work/fontconfig/build_meson_dll/conf.d/35-lang-normalize.conf": "/usr/local/share/fontconfig/conf.avail/35-lang-normalize.conf", "/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/fontconfig.pc": "/usr/local/lib/pkgconfig/fontconfig.pc", "/Users/<USER>/work/fontconfig/conf.d/05-reset-dirs-sample.conf": "/usr/local/share/fontconfig/conf.avail/05-reset-dirs-sample.conf", "/Users/<USER>/work/fontconfig/conf.d/09-autohint-if-no-hinting.conf": "/usr/local/share/fontconfig/conf.avail/09-autohint-if-no-hinting.conf", "/Users/<USER>/work/fontconfig/conf.d/10-autohint.conf": "/usr/local/share/fontconfig/conf.avail/10-autohint.conf", "/Users/<USER>/work/fontconfig/conf.d/10-hinting-full.conf": "/usr/local/share/fontconfig/conf.avail/10-hinting-full.conf", "/Users/<USER>/work/fontconfig/conf.d/10-hinting-medium.conf": "/usr/local/share/fontconfig/conf.avail/10-hinting-medium.conf", "/Users/<USER>/work/fontconfig/conf.d/10-hinting-none.conf": "/usr/local/share/fontconfig/conf.avail/10-hinting-none.conf", "/Users/<USER>/work/fontconfig/conf.d/10-hinting-slight.conf": "/usr/local/share/fontconfig/conf.avail/10-hinting-slight.conf", "/Users/<USER>/work/fontconfig/conf.d/10-no-antialias.conf": "/usr/local/share/fontconfig/conf.avail/10-no-antialias.conf", "/Users/<USER>/work/fontconfig/conf.d/10-scale-bitmap-fonts.conf": "/usr/local/share/fontconfig/conf.avail/10-scale-bitmap-fonts.conf", "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-bgr.conf": "/usr/local/share/fontconfig/conf.avail/10-sub-pixel-bgr.conf", "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-none.conf": "/usr/local/share/fontconfig/conf.avail/10-sub-pixel-none.conf", "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-rgb.conf": "/usr/local/share/fontconfig/conf.avail/10-sub-pixel-rgb.conf", "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-vbgr.conf": "/usr/local/share/fontconfig/conf.avail/10-sub-pixel-vbgr.conf", "/Users/<USER>/work/fontconfig/conf.d/10-sub-pixel-vrgb.conf": "/usr/local/share/fontconfig/conf.avail/10-sub-pixel-vrgb.conf", "/Users/<USER>/work/fontconfig/conf.d/10-unhinted.conf": "/usr/local/share/fontconfig/conf.avail/10-unhinted.conf", "/Users/<USER>/work/fontconfig/conf.d/10-yes-antialias.conf": "/usr/local/share/fontconfig/conf.avail/10-yes-antialias.conf", "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-default.conf": "/usr/local/share/fontconfig/conf.avail/11-lcdfilter-default.conf", "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-legacy.conf": "/usr/local/share/fontconfig/conf.avail/11-lcdfilter-legacy.conf", "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-light.conf": "/usr/local/share/fontconfig/conf.avail/11-lcdfilter-light.conf", "/Users/<USER>/work/fontconfig/conf.d/11-lcdfilter-none.conf": "/usr/local/share/fontconfig/conf.avail/11-lcdfilter-none.conf", "/Users/<USER>/work/fontconfig/conf.d/20-unhint-small-vera.conf": "/usr/local/share/fontconfig/conf.avail/20-unhint-small-vera.conf", "/Users/<USER>/work/fontconfig/conf.d/25-unhint-nonlatin.conf": "/usr/local/share/fontconfig/conf.avail/25-unhint-nonlatin.conf", "/Users/<USER>/work/fontconfig/conf.d/30-metric-aliases.conf": "/usr/local/share/fontconfig/conf.avail/30-metric-aliases.conf", "/Users/<USER>/work/fontconfig/conf.d/40-nonlatin.conf": "/usr/local/share/fontconfig/conf.avail/40-nonlatin.conf", "/Users/<USER>/work/fontconfig/conf.d/45-generic.conf": "/usr/local/share/fontconfig/conf.avail/45-generic.conf", "/Users/<USER>/work/fontconfig/conf.d/45-latin.conf": "/usr/local/share/fontconfig/conf.avail/45-latin.conf", "/Users/<USER>/work/fontconfig/conf.d/48-guessfamily.conf": "/usr/local/share/fontconfig/conf.avail/48-guessfamily.conf", "/Users/<USER>/work/fontconfig/conf.d/48-spacing.conf": "/usr/local/share/fontconfig/conf.avail/48-spacing.conf", "/Users/<USER>/work/fontconfig/conf.d/49-sansserif.conf": "/usr/local/share/fontconfig/conf.avail/49-sansserif.conf", "/Users/<USER>/work/fontconfig/conf.d/50-user.conf": "/usr/local/share/fontconfig/conf.avail/50-user.conf", "/Users/<USER>/work/fontconfig/conf.d/51-local.conf": "/usr/local/share/fontconfig/conf.avail/51-local.conf", "/Users/<USER>/work/fontconfig/conf.d/60-generic.conf": "/usr/local/share/fontconfig/conf.avail/60-generic.conf", "/Users/<USER>/work/fontconfig/conf.d/60-latin.conf": "/usr/local/share/fontconfig/conf.avail/60-latin.conf", "/Users/<USER>/work/fontconfig/conf.d/65-fonts-persian.conf": "/usr/local/share/fontconfig/conf.avail/65-fonts-persian.conf", "/Users/<USER>/work/fontconfig/conf.d/65-khmer.conf": "/usr/local/share/fontconfig/conf.avail/65-khmer.conf", "/Users/<USER>/work/fontconfig/conf.d/65-nonlatin.conf": "/usr/local/share/fontconfig/conf.avail/65-nonlatin.conf", "/Users/<USER>/work/fontconfig/conf.d/69-unifont.conf": "/usr/local/share/fontconfig/conf.avail/69-unifont.conf", "/Users/<USER>/work/fontconfig/conf.d/70-no-bitmaps.conf": "/usr/local/share/fontconfig/conf.avail/70-no-bitmaps.conf", "/Users/<USER>/work/fontconfig/conf.d/70-no-bitmaps-and-emoji.conf": "/usr/local/share/fontconfig/conf.avail/70-no-bitmaps-and-emoji.conf", "/Users/<USER>/work/fontconfig/conf.d/70-no-bitmaps-except-emoji.conf": "/usr/local/share/fontconfig/conf.avail/70-no-bitmaps-except-emoji.conf", "/Users/<USER>/work/fontconfig/conf.d/70-yes-bitmaps.conf": "/usr/local/share/fontconfig/conf.avail/70-yes-bitmaps.conf", "/Users/<USER>/work/fontconfig/conf.d/80-delicious.conf": "/usr/local/share/fontconfig/conf.avail/80-delicious.conf", "/Users/<USER>/work/fontconfig/conf.d/90-synthetic.conf": "/usr/local/share/fontconfig/conf.avail/90-synthetic.conf", "/Users/<USER>/work/fontconfig/build_meson_dll/conf.d/README": "/usr/local/etc/fonts/conf.d/README", "/Users/<USER>/work/fontconfig/its/fontconfig.its": "/usr/local/share/gettext/its/fontconfig.its", "/Users/<USER>/work/fontconfig/its/fontconfig.loc": "/usr/local/share/gettext/its/fontconfig.loc", "/Users/<USER>/work/fontconfig/build_meson_dll/fonts.conf": "/usr/local/etc/fonts/fonts.conf", "/Users/<USER>/work/fontconfig/fonts.dtd": "/usr/local/share/xml/fontconfig/fonts.dtd", "/Users/<USER>/work/fontconfig/build_meson_dll/fontconfig/fontconfig.h": "/usr/local/include/fontconfig/fontconfig.h", "/Users/<USER>/work/fontconfig/fontconfig/fcfreetype.h": "/usr/local/include/fontconfig/fcfreetype.h", "/Users/<USER>/work/fontconfig/fontconfig/fcprivate.h": "/usr/local/include/fontconfig/fcprivate.h", "libfontconfig.dylib": "/usr/local/lib/libfontconfig.dylib"}