[{"name": "fcstdint.h", "id": "fcstdint.h@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fcstdint.h"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/usr/local/bin/meson", "--internal", "copy", "@INPUT@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/src/fcstdint.h.in"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "alias_headers", "id": "alias_headers@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fcalias.h", "/Users/<USER>/work/fontconfig/build_meson_dll/fcaliastail.h"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/usr/local/opt/python@3.13/bin/python3.13", "/Users/<USER>/work/fontconfig/src/makealias.py", "/Users/<USER>/work/fontconfig/src", "@OUTPUT@", "@INPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/build_meson_dll/fontconfig/fontconfig.h", "/Users/<USER>/work/fontconfig/src/fcdeprecate.h", "/Users/<USER>/work/fontconfig/fontconfig/fcprivate.h"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "ft_alias_headers", "id": "ft_alias_headers@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fcftalias.h", "/Users/<USER>/work/fontconfig/build_meson_dll/fcftaliastail.h"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/usr/local/opt/python@3.13/bin/python3.13", "/Users/<USER>/work/fontconfig/src/makealias.py", "/Users/<USER>/work/fontconfig/src", "@OUTPUT@", "@INPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/fontconfig/fcfreetype.h"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fccase.h", "id": "d517124@@fccase.h@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/fc-case/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-case/fccase.h"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/fc-case/fc-case.py", "@INPUT0@", "--template", "@INPUT1@", "--output", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/fc-case/CaseFolding.txt", "/Users/<USER>/work/fontconfig/fc-case/fccase.tmpl.h"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fclang.h", "id": "f362622@@fclang.h@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/fc-lang/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang/fclang.h"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/fc-lang/fc-lang.py", "aa.orth", "ab.orth", "af.orth", "am.orth", "ar.orth", "as.orth", "ast.orth", "av.orth", "ay.orth", "az_az.orth", "az_ir.orth", "ba.orth", "bm.orth", "be.orth", "bg.orth", "bh.orth", "bho.orth", "bi.orth", "bin.orth", "bn.orth", "bo.orth", "br.orth", "bs.orth", "bua.orth", "ca.orth", "ce.orth", "ch.orth", "chm.orth", "chr.orth", "co.orth", "cs.orth", "cu.orth", "cv.orth", "cy.orth", "da.orth", "de.orth", "dz.orth", "el.orth", "en.orth", "eo.orth", "es.orth", "et.orth", "eu.orth", "fa.orth", "fi.orth", "fj.orth", "fo.orth", "fr.orth", "ff.orth", "fur.orth", "fy.orth", "ga.orth", "gd.orth", "gez.orth", "gl.orth", "gn.orth", "gu.orth", "gv.orth", "ha.orth", "haw.orth", "he.orth", "hi.orth", "ho.orth", "hr.orth", "hu.orth", "hy.orth", "ia.orth", "ig.orth", "id.orth", "ie.orth", "ik.orth", "io.orth", "is.orth", "it.orth", "iu.orth", "ja.orth", "ka.orth", "kaa.orth", "ki.orth", "kk.orth", "kl.orth", "km.orth", "kn.orth", "ko.orth", "kok.orth", "ks.orth", "ku_am.orth", "ku_ir.orth", "kum.orth", "kv.orth", "kw.orth", "ky.orth", "la.orth", "lb.orth", "lez.orth", "ln.orth", "lo.orth", "lt.orth", "lv.orth", "mg.orth", "mh.orth", "mi.orth", "mk.orth", "ml.orth", "mn_cn.orth", "mo.orth", "mr.orth", "mt.orth", "my.orth", "nb.orth", "nds.orth", "ne.orth", "nl.orth", "nn.orth", "no.orth", "nr.orth", "nso.orth", "ny.orth", "oc.orth", "om.orth", "or.orth", "os.orth", "pa.orth", "pl.orth", "ps_af.orth", "ps_pk.orth", "pt.orth", "rm.orth", "ro.orth", "ru.orth", "sa.orth", "sah.orth", "sco.orth", "se.orth", "sel.orth", "sh.orth", "shs.orth", "si.orth", "sk.orth", "sl.orth", "sm.orth", "sma.orth", "smj.orth", "smn.orth", "sms.orth", "so.orth", "sq.orth", "sr.orth", "ss.orth", "st.orth", "sv.orth", "sw.orth", "syr.orth", "ta.orth", "te.orth", "tg.orth", "th.orth", "ti_er.orth", "ti_et.orth", "tig.orth", "tk.orth", "tl.orth", "tn.orth", "to.orth", "tr.orth", "ts.orth", "tt.orth", "tw.orth", "tyv.orth", "ug.orth", "uk.orth", "ur.orth", "uz.orth", "ve.orth", "vi.orth", "vo.orth", "vot.orth", "wa.orth", "wen.orth", "wo.orth", "xh.orth", "yap.orth", "yi.orth", "yo.orth", "zh_cn.orth", "zh_hk.orth", "zh_mo.orth", "zh_sg.orth", "zh_tw.orth", "zu.orth", "ak.orth", "an.orth", "ber_dz.orth", "ber_ma.orth", "byn.orth", "crh.orth", "csb.orth", "dv.orth", "ee.orth", "fat.orth", "fil.orth", "hne.orth", "hsb.orth", "ht.orth", "hz.orth", "ii.orth", "jv.orth", "kab.orth", "kj.orth", "kr.orth", "ku_iq.orth", "ku_tr.orth", "kwm.orth", "lg.orth", "li.orth", "mai.orth", "mn_mn.orth", "ms.orth", "na.orth", "ng.orth", "nv.orth", "ota.orth", "pa_pk.orth", "pap_an.orth", "pap_aw.orth", "qu.orth", "quz.orth", "rn.orth", "rw.orth", "sc.orth", "sd.orth", "sg.orth", "sid.orth", "sn.orth", "su.orth", "ty.orth", "wal.orth", "za.orth", "lah.orth", "nqo.orth", "brx.orth", "sat.orth", "doi.orth", "mni.orth", "und_zsye.orth", "und_zmth.orth", "anp.orth", "bhb.orth", "hif.orth", "mag.orth", "raj.orth", "the.orth", "agr.orth", "ayc.orth", "bem.orth", "ckb.orth", "cmn.orth", "dsb.orth", "hak.orth", "lij.orth", "lzh.orth", "mfe.orth", "mhr.orth", "miq.orth", "mjw.orth", "mnw.orth", "nan.orth", "nhn.orth", "niu.orth", "rif.orth", "sgs.orth", "shn.orth", "szl.orth", "tcy.orth", "tpi.orth", "unm.orth", "wae.orth", "yue.orth", "yuw.orth", "got.orth", "cop.orth", "--template", "/Users/<USER>/work/fontconfig/fc-lang/fclang.tmpl.h", "--output", "@OUTPUT@", "--directory", "/Users/<USER>/work/fontconfig/fc-lang"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/fc-lang/aa.orth", "/Users/<USER>/work/fontconfig/fc-lang/ab.orth", "/Users/<USER>/work/fontconfig/fc-lang/af.orth", "/Users/<USER>/work/fontconfig/fc-lang/am.orth", "/Users/<USER>/work/fontconfig/fc-lang/ar.orth", "/Users/<USER>/work/fontconfig/fc-lang/as.orth", "/Users/<USER>/work/fontconfig/fc-lang/ast.orth", "/Users/<USER>/work/fontconfig/fc-lang/av.orth", "/Users/<USER>/work/fontconfig/fc-lang/ay.orth", "/Users/<USER>/work/fontconfig/fc-lang/az_az.orth", "/Users/<USER>/work/fontconfig/fc-lang/az_ir.orth", "/Users/<USER>/work/fontconfig/fc-lang/ba.orth", "/Users/<USER>/work/fontconfig/fc-lang/bm.orth", "/Users/<USER>/work/fontconfig/fc-lang/be.orth", "/Users/<USER>/work/fontconfig/fc-lang/bg.orth", "/Users/<USER>/work/fontconfig/fc-lang/bh.orth", "/Users/<USER>/work/fontconfig/fc-lang/bho.orth", "/Users/<USER>/work/fontconfig/fc-lang/bi.orth", "/Users/<USER>/work/fontconfig/fc-lang/bin.orth", "/Users/<USER>/work/fontconfig/fc-lang/bn.orth", "/Users/<USER>/work/fontconfig/fc-lang/bo.orth", "/Users/<USER>/work/fontconfig/fc-lang/br.orth", "/Users/<USER>/work/fontconfig/fc-lang/bs.orth", "/Users/<USER>/work/fontconfig/fc-lang/bua.orth", "/Users/<USER>/work/fontconfig/fc-lang/ca.orth", "/Users/<USER>/work/fontconfig/fc-lang/ce.orth", "/Users/<USER>/work/fontconfig/fc-lang/ch.orth", "/Users/<USER>/work/fontconfig/fc-lang/chm.orth", "/Users/<USER>/work/fontconfig/fc-lang/chr.orth", "/Users/<USER>/work/fontconfig/fc-lang/co.orth", "/Users/<USER>/work/fontconfig/fc-lang/cs.orth", "/Users/<USER>/work/fontconfig/fc-lang/cu.orth", "/Users/<USER>/work/fontconfig/fc-lang/cv.orth", "/Users/<USER>/work/fontconfig/fc-lang/cy.orth", "/Users/<USER>/work/fontconfig/fc-lang/da.orth", "/Users/<USER>/work/fontconfig/fc-lang/de.orth", "/Users/<USER>/work/fontconfig/fc-lang/dz.orth", "/Users/<USER>/work/fontconfig/fc-lang/el.orth", "/Users/<USER>/work/fontconfig/fc-lang/en.orth", "/Users/<USER>/work/fontconfig/fc-lang/eo.orth", "/Users/<USER>/work/fontconfig/fc-lang/es.orth", "/Users/<USER>/work/fontconfig/fc-lang/et.orth", "/Users/<USER>/work/fontconfig/fc-lang/eu.orth", "/Users/<USER>/work/fontconfig/fc-lang/fa.orth", "/Users/<USER>/work/fontconfig/fc-lang/fi.orth", "/Users/<USER>/work/fontconfig/fc-lang/fj.orth", "/Users/<USER>/work/fontconfig/fc-lang/fo.orth", "/Users/<USER>/work/fontconfig/fc-lang/fr.orth", "/Users/<USER>/work/fontconfig/fc-lang/ff.orth", "/Users/<USER>/work/fontconfig/fc-lang/fur.orth", "/Users/<USER>/work/fontconfig/fc-lang/fy.orth", "/Users/<USER>/work/fontconfig/fc-lang/ga.orth", "/Users/<USER>/work/fontconfig/fc-lang/gd.orth", "/Users/<USER>/work/fontconfig/fc-lang/gez.orth", "/Users/<USER>/work/fontconfig/fc-lang/gl.orth", "/Users/<USER>/work/fontconfig/fc-lang/gn.orth", "/Users/<USER>/work/fontconfig/fc-lang/gu.orth", "/Users/<USER>/work/fontconfig/fc-lang/gv.orth", "/Users/<USER>/work/fontconfig/fc-lang/ha.orth", "/Users/<USER>/work/fontconfig/fc-lang/haw.orth", "/Users/<USER>/work/fontconfig/fc-lang/he.orth", "/Users/<USER>/work/fontconfig/fc-lang/hi.orth", "/Users/<USER>/work/fontconfig/fc-lang/ho.orth", "/Users/<USER>/work/fontconfig/fc-lang/hr.orth", "/Users/<USER>/work/fontconfig/fc-lang/hu.orth", "/Users/<USER>/work/fontconfig/fc-lang/hy.orth", "/Users/<USER>/work/fontconfig/fc-lang/ia.orth", "/Users/<USER>/work/fontconfig/fc-lang/ig.orth", "/Users/<USER>/work/fontconfig/fc-lang/id.orth", "/Users/<USER>/work/fontconfig/fc-lang/ie.orth", "/Users/<USER>/work/fontconfig/fc-lang/ik.orth", "/Users/<USER>/work/fontconfig/fc-lang/io.orth", "/Users/<USER>/work/fontconfig/fc-lang/is.orth", "/Users/<USER>/work/fontconfig/fc-lang/it.orth", "/Users/<USER>/work/fontconfig/fc-lang/iu.orth", "/Users/<USER>/work/fontconfig/fc-lang/ja.orth", "/Users/<USER>/work/fontconfig/fc-lang/ka.orth", "/Users/<USER>/work/fontconfig/fc-lang/kaa.orth", "/Users/<USER>/work/fontconfig/fc-lang/ki.orth", "/Users/<USER>/work/fontconfig/fc-lang/kk.orth", "/Users/<USER>/work/fontconfig/fc-lang/kl.orth", "/Users/<USER>/work/fontconfig/fc-lang/km.orth", "/Users/<USER>/work/fontconfig/fc-lang/kn.orth", "/Users/<USER>/work/fontconfig/fc-lang/ko.orth", "/Users/<USER>/work/fontconfig/fc-lang/kok.orth", "/Users/<USER>/work/fontconfig/fc-lang/ks.orth", "/Users/<USER>/work/fontconfig/fc-lang/ku_am.orth", "/Users/<USER>/work/fontconfig/fc-lang/ku_ir.orth", "/Users/<USER>/work/fontconfig/fc-lang/kum.orth", "/Users/<USER>/work/fontconfig/fc-lang/kv.orth", "/Users/<USER>/work/fontconfig/fc-lang/kw.orth", "/Users/<USER>/work/fontconfig/fc-lang/ky.orth", "/Users/<USER>/work/fontconfig/fc-lang/la.orth", "/Users/<USER>/work/fontconfig/fc-lang/lb.orth", "/Users/<USER>/work/fontconfig/fc-lang/lez.orth", "/Users/<USER>/work/fontconfig/fc-lang/ln.orth", "/Users/<USER>/work/fontconfig/fc-lang/lo.orth", "/Users/<USER>/work/fontconfig/fc-lang/lt.orth", "/Users/<USER>/work/fontconfig/fc-lang/lv.orth", "/Users/<USER>/work/fontconfig/fc-lang/mg.orth", "/Users/<USER>/work/fontconfig/fc-lang/mh.orth", "/Users/<USER>/work/fontconfig/fc-lang/mi.orth", "/Users/<USER>/work/fontconfig/fc-lang/mk.orth", "/Users/<USER>/work/fontconfig/fc-lang/ml.orth", "/Users/<USER>/work/fontconfig/fc-lang/mn_cn.orth", "/Users/<USER>/work/fontconfig/fc-lang/mo.orth", "/Users/<USER>/work/fontconfig/fc-lang/mr.orth", "/Users/<USER>/work/fontconfig/fc-lang/mt.orth", "/Users/<USER>/work/fontconfig/fc-lang/my.orth", "/Users/<USER>/work/fontconfig/fc-lang/nb.orth", "/Users/<USER>/work/fontconfig/fc-lang/nds.orth", "/Users/<USER>/work/fontconfig/fc-lang/ne.orth", "/Users/<USER>/work/fontconfig/fc-lang/nl.orth", "/Users/<USER>/work/fontconfig/fc-lang/nn.orth", "/Users/<USER>/work/fontconfig/fc-lang/no.orth", "/Users/<USER>/work/fontconfig/fc-lang/nr.orth", "/Users/<USER>/work/fontconfig/fc-lang/nso.orth", "/Users/<USER>/work/fontconfig/fc-lang/ny.orth", "/Users/<USER>/work/fontconfig/fc-lang/oc.orth", "/Users/<USER>/work/fontconfig/fc-lang/om.orth", "/Users/<USER>/work/fontconfig/fc-lang/or.orth", "/Users/<USER>/work/fontconfig/fc-lang/os.orth", "/Users/<USER>/work/fontconfig/fc-lang/pa.orth", "/Users/<USER>/work/fontconfig/fc-lang/pl.orth", "/Users/<USER>/work/fontconfig/fc-lang/ps_af.orth", "/Users/<USER>/work/fontconfig/fc-lang/ps_pk.orth", "/Users/<USER>/work/fontconfig/fc-lang/pt.orth", "/Users/<USER>/work/fontconfig/fc-lang/rm.orth", "/Users/<USER>/work/fontconfig/fc-lang/ro.orth", "/Users/<USER>/work/fontconfig/fc-lang/ru.orth", "/Users/<USER>/work/fontconfig/fc-lang/sa.orth", "/Users/<USER>/work/fontconfig/fc-lang/sah.orth", "/Users/<USER>/work/fontconfig/fc-lang/sco.orth", "/Users/<USER>/work/fontconfig/fc-lang/se.orth", "/Users/<USER>/work/fontconfig/fc-lang/sel.orth", "/Users/<USER>/work/fontconfig/fc-lang/sh.orth", "/Users/<USER>/work/fontconfig/fc-lang/shs.orth", "/Users/<USER>/work/fontconfig/fc-lang/si.orth", "/Users/<USER>/work/fontconfig/fc-lang/sk.orth", "/Users/<USER>/work/fontconfig/fc-lang/sl.orth", "/Users/<USER>/work/fontconfig/fc-lang/sm.orth", "/Users/<USER>/work/fontconfig/fc-lang/sma.orth", "/Users/<USER>/work/fontconfig/fc-lang/smj.orth", "/Users/<USER>/work/fontconfig/fc-lang/smn.orth", "/Users/<USER>/work/fontconfig/fc-lang/sms.orth", "/Users/<USER>/work/fontconfig/fc-lang/so.orth", "/Users/<USER>/work/fontconfig/fc-lang/sq.orth", "/Users/<USER>/work/fontconfig/fc-lang/sr.orth", "/Users/<USER>/work/fontconfig/fc-lang/ss.orth", "/Users/<USER>/work/fontconfig/fc-lang/st.orth", "/Users/<USER>/work/fontconfig/fc-lang/sv.orth", "/Users/<USER>/work/fontconfig/fc-lang/sw.orth", "/Users/<USER>/work/fontconfig/fc-lang/syr.orth", "/Users/<USER>/work/fontconfig/fc-lang/ta.orth", "/Users/<USER>/work/fontconfig/fc-lang/te.orth", "/Users/<USER>/work/fontconfig/fc-lang/tg.orth", "/Users/<USER>/work/fontconfig/fc-lang/th.orth", "/Users/<USER>/work/fontconfig/fc-lang/ti_er.orth", "/Users/<USER>/work/fontconfig/fc-lang/ti_et.orth", "/Users/<USER>/work/fontconfig/fc-lang/tig.orth", "/Users/<USER>/work/fontconfig/fc-lang/tk.orth", "/Users/<USER>/work/fontconfig/fc-lang/tl.orth", "/Users/<USER>/work/fontconfig/fc-lang/tn.orth", "/Users/<USER>/work/fontconfig/fc-lang/to.orth", "/Users/<USER>/work/fontconfig/fc-lang/tr.orth", "/Users/<USER>/work/fontconfig/fc-lang/ts.orth", "/Users/<USER>/work/fontconfig/fc-lang/tt.orth", "/Users/<USER>/work/fontconfig/fc-lang/tw.orth", "/Users/<USER>/work/fontconfig/fc-lang/tyv.orth", "/Users/<USER>/work/fontconfig/fc-lang/ug.orth", "/Users/<USER>/work/fontconfig/fc-lang/uk.orth", "/Users/<USER>/work/fontconfig/fc-lang/ur.orth", "/Users/<USER>/work/fontconfig/fc-lang/uz.orth", "/Users/<USER>/work/fontconfig/fc-lang/ve.orth", "/Users/<USER>/work/fontconfig/fc-lang/vi.orth", "/Users/<USER>/work/fontconfig/fc-lang/vo.orth", "/Users/<USER>/work/fontconfig/fc-lang/vot.orth", "/Users/<USER>/work/fontconfig/fc-lang/wa.orth", "/Users/<USER>/work/fontconfig/fc-lang/wen.orth", "/Users/<USER>/work/fontconfig/fc-lang/wo.orth", "/Users/<USER>/work/fontconfig/fc-lang/xh.orth", "/Users/<USER>/work/fontconfig/fc-lang/yap.orth", "/Users/<USER>/work/fontconfig/fc-lang/yi.orth", "/Users/<USER>/work/fontconfig/fc-lang/yo.orth", "/Users/<USER>/work/fontconfig/fc-lang/zh_cn.orth", "/Users/<USER>/work/fontconfig/fc-lang/zh_hk.orth", "/Users/<USER>/work/fontconfig/fc-lang/zh_mo.orth", "/Users/<USER>/work/fontconfig/fc-lang/zh_sg.orth", "/Users/<USER>/work/fontconfig/fc-lang/zh_tw.orth", "/Users/<USER>/work/fontconfig/fc-lang/zu.orth", "/Users/<USER>/work/fontconfig/fc-lang/ak.orth", "/Users/<USER>/work/fontconfig/fc-lang/an.orth", "/Users/<USER>/work/fontconfig/fc-lang/ber_dz.orth", "/Users/<USER>/work/fontconfig/fc-lang/ber_ma.orth", "/Users/<USER>/work/fontconfig/fc-lang/byn.orth", "/Users/<USER>/work/fontconfig/fc-lang/crh.orth", "/Users/<USER>/work/fontconfig/fc-lang/csb.orth", "/Users/<USER>/work/fontconfig/fc-lang/dv.orth", "/Users/<USER>/work/fontconfig/fc-lang/ee.orth", "/Users/<USER>/work/fontconfig/fc-lang/fat.orth", "/Users/<USER>/work/fontconfig/fc-lang/fil.orth", "/Users/<USER>/work/fontconfig/fc-lang/hne.orth", "/Users/<USER>/work/fontconfig/fc-lang/hsb.orth", "/Users/<USER>/work/fontconfig/fc-lang/ht.orth", "/Users/<USER>/work/fontconfig/fc-lang/hz.orth", "/Users/<USER>/work/fontconfig/fc-lang/ii.orth", "/Users/<USER>/work/fontconfig/fc-lang/jv.orth", "/Users/<USER>/work/fontconfig/fc-lang/kab.orth", "/Users/<USER>/work/fontconfig/fc-lang/kj.orth", "/Users/<USER>/work/fontconfig/fc-lang/kr.orth", "/Users/<USER>/work/fontconfig/fc-lang/ku_iq.orth", "/Users/<USER>/work/fontconfig/fc-lang/ku_tr.orth", "/Users/<USER>/work/fontconfig/fc-lang/kwm.orth", "/Users/<USER>/work/fontconfig/fc-lang/lg.orth", "/Users/<USER>/work/fontconfig/fc-lang/li.orth", "/Users/<USER>/work/fontconfig/fc-lang/mai.orth", "/Users/<USER>/work/fontconfig/fc-lang/mn_mn.orth", "/Users/<USER>/work/fontconfig/fc-lang/ms.orth", "/Users/<USER>/work/fontconfig/fc-lang/na.orth", "/Users/<USER>/work/fontconfig/fc-lang/ng.orth", "/Users/<USER>/work/fontconfig/fc-lang/nv.orth", "/Users/<USER>/work/fontconfig/fc-lang/ota.orth", "/Users/<USER>/work/fontconfig/fc-lang/pa_pk.orth", "/Users/<USER>/work/fontconfig/fc-lang/pap_an.orth", "/Users/<USER>/work/fontconfig/fc-lang/pap_aw.orth", "/Users/<USER>/work/fontconfig/fc-lang/qu.orth", "/Users/<USER>/work/fontconfig/fc-lang/quz.orth", "/Users/<USER>/work/fontconfig/fc-lang/rn.orth", "/Users/<USER>/work/fontconfig/fc-lang/rw.orth", "/Users/<USER>/work/fontconfig/fc-lang/sc.orth", "/Users/<USER>/work/fontconfig/fc-lang/sd.orth", "/Users/<USER>/work/fontconfig/fc-lang/sg.orth", "/Users/<USER>/work/fontconfig/fc-lang/sid.orth", "/Users/<USER>/work/fontconfig/fc-lang/sn.orth", "/Users/<USER>/work/fontconfig/fc-lang/su.orth", "/Users/<USER>/work/fontconfig/fc-lang/ty.orth", "/Users/<USER>/work/fontconfig/fc-lang/wal.orth", "/Users/<USER>/work/fontconfig/fc-lang/za.orth", "/Users/<USER>/work/fontconfig/fc-lang/lah.orth", "/Users/<USER>/work/fontconfig/fc-lang/nqo.orth", "/Users/<USER>/work/fontconfig/fc-lang/brx.orth", "/Users/<USER>/work/fontconfig/fc-lang/sat.orth", "/Users/<USER>/work/fontconfig/fc-lang/doi.orth", "/Users/<USER>/work/fontconfig/fc-lang/mni.orth", "/Users/<USER>/work/fontconfig/fc-lang/und_zsye.orth", "/Users/<USER>/work/fontconfig/fc-lang/und_zmth.orth", "/Users/<USER>/work/fontconfig/fc-lang/anp.orth", "/Users/<USER>/work/fontconfig/fc-lang/bhb.orth", "/Users/<USER>/work/fontconfig/fc-lang/hif.orth", "/Users/<USER>/work/fontconfig/fc-lang/mag.orth", "/Users/<USER>/work/fontconfig/fc-lang/raj.orth", "/Users/<USER>/work/fontconfig/fc-lang/the.orth", "/Users/<USER>/work/fontconfig/fc-lang/agr.orth", "/Users/<USER>/work/fontconfig/fc-lang/ayc.orth", "/Users/<USER>/work/fontconfig/fc-lang/bem.orth", "/Users/<USER>/work/fontconfig/fc-lang/ckb.orth", "/Users/<USER>/work/fontconfig/fc-lang/cmn.orth", "/Users/<USER>/work/fontconfig/fc-lang/dsb.orth", "/Users/<USER>/work/fontconfig/fc-lang/hak.orth", "/Users/<USER>/work/fontconfig/fc-lang/lij.orth", "/Users/<USER>/work/fontconfig/fc-lang/lzh.orth", "/Users/<USER>/work/fontconfig/fc-lang/mfe.orth", "/Users/<USER>/work/fontconfig/fc-lang/mhr.orth", "/Users/<USER>/work/fontconfig/fc-lang/miq.orth", "/Users/<USER>/work/fontconfig/fc-lang/mjw.orth", "/Users/<USER>/work/fontconfig/fc-lang/mnw.orth", "/Users/<USER>/work/fontconfig/fc-lang/nan.orth", "/Users/<USER>/work/fontconfig/fc-lang/nhn.orth", "/Users/<USER>/work/fontconfig/fc-lang/niu.orth", "/Users/<USER>/work/fontconfig/fc-lang/rif.orth", "/Users/<USER>/work/fontconfig/fc-lang/sgs.orth", "/Users/<USER>/work/fontconfig/fc-lang/shn.orth", "/Users/<USER>/work/fontconfig/fc-lang/szl.orth", "/Users/<USER>/work/fontconfig/fc-lang/tcy.orth", "/Users/<USER>/work/fontconfig/fc-lang/tpi.orth", "/Users/<USER>/work/fontconfig/fc-lang/unm.orth", "/Users/<USER>/work/fontconfig/fc-lang/wae.orth", "/Users/<USER>/work/fontconfig/fc-lang/yue.orth", "/Users/<USER>/work/fontconfig/fc-lang/yuw.orth", "/Users/<USER>/work/fontconfig/fc-lang/got.orth", "/Users/<USER>/work/fontconfig/fc-lang/cop.orth"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "patternlib_internal", "id": "25a6634@@patternlib_internal@sta", "type": "static library", "defined_in": "/Users/<USER>/work/fontconfig/src/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/src/libpatternlib_internal.a"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/src/libpatternlib_internal.a.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/usr/local/opt/freetype/include/freetype2", "-I/usr/local/opt/libpng/include/libpng16", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/src/fcpat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["ar"], "parameters": ["csr"]}], "extra_files": [], "subproject": null, "dependencies": ["freetype2", "expat", "", "threads"], "depends": [], "installed": false}, {"name": "preprocessor_0", "id": "25a6634@@preprocessor_0@compile", "type": "compile", "defined_in": "/Users/<USER>/work/fontconfig/src/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/src/fcobjshash.gperf.h.i"], "build_by_default": false, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc", "-E", "-P", "-xc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/src/preprocessor_0.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/src/fcobjshash.gperf.h"], "generated_sources": [], "unity_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcobjshash.gperf", "id": "25a6634@@fcobjshash.gperf@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/src/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/src/fcobjshash.gperf"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/src/cutout.py", "@INPUT@", "@OUTPUT@"], "parameters": [], "sources": [], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcobjshash.h", "id": "25a6634@@fcobjshash.h@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/src/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/src/fcobjshash.h"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/usr/bin/gperf", "--pic", "-m", "100", "@INPUT@", "--output-file", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/build_meson_dll/src/fcobjshash.gperf"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fontconfig", "id": "fontconfig@sha", "type": "shared library", "defined_in": "/Users/<USER>/work/fontconfig/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/libfontconfig.1.dylib"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/libfontconfig.1.dylib.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-case", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/usr/local/opt/freetype/include/freetype2", "-I/usr/local/opt/libpng/include/libpng16", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/src/fcatomic.c", "/Users/<USER>/work/fontconfig/src/fccache.c", "/Users/<USER>/work/fontconfig/src/fccfg.c", "/Users/<USER>/work/fontconfig/src/fccharset.c", "/Users/<USER>/work/fontconfig/src/fccompat.c", "/Users/<USER>/work/fontconfig/src/fcdbg.c", "/Users/<USER>/work/fontconfig/src/fcdefault.c", "/Users/<USER>/work/fontconfig/src/fcdir.c", "/Users/<USER>/work/fontconfig/src/fcformat.c", "/Users/<USER>/work/fontconfig/src/fcfreetype.c", "/Users/<USER>/work/fontconfig/src/fcfs.c", "/Users/<USER>/work/fontconfig/src/fcptrlist.c", "/Users/<USER>/work/fontconfig/src/fchash.c", "/Users/<USER>/work/fontconfig/src/fcinit.c", "/Users/<USER>/work/fontconfig/src/fclang.c", "/Users/<USER>/work/fontconfig/src/fclist.c", "/Users/<USER>/work/fontconfig/src/fcmatch.c", "/Users/<USER>/work/fontconfig/src/fcmatrix.c", "/Users/<USER>/work/fontconfig/src/fcname.c", "/Users/<USER>/work/fontconfig/src/fcobjs.c", "/Users/<USER>/work/fontconfig/src/fcrange.c", "/Users/<USER>/work/fontconfig/src/fcserialize.c", "/Users/<USER>/work/fontconfig/src/fcstat.c", "/Users/<USER>/work/fontconfig/src/fcstr.c", "/Users/<USER>/work/fontconfig/src/fcweight.c", "/Users/<USER>/work/fontconfig/src/fcxml.c", "/Users/<USER>/work/fontconfig/src/ftglue.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-shared", "-install_name", "@rpath/libfontconfig.1.dylib", "-compatibility_version", "17", "-current_version", "17", "-Wl,-rpath,/usr/local/opt/freetype/lib", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": ["freetype2", "expat", "", "threads", "m"], "depends": [], "installed": true, "install_filename": ["/usr/local/lib/libfontconfig.1.dylib", "/usr/local/lib/libfontconfig.dylib"]}, {"name": "fontconfig", "id": "fontconfig@sta", "type": "static library", "defined_in": "/Users/<USER>/work/fontconfig/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/libfontconfig.a"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/libfontconfig.a.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-case", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/usr/local/opt/freetype/include/freetype2", "-I/usr/local/opt/libpng/include/libpng16", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/src/fcatomic.c", "/Users/<USER>/work/fontconfig/src/fccache.c", "/Users/<USER>/work/fontconfig/src/fccfg.c", "/Users/<USER>/work/fontconfig/src/fccharset.c", "/Users/<USER>/work/fontconfig/src/fccompat.c", "/Users/<USER>/work/fontconfig/src/fcdbg.c", "/Users/<USER>/work/fontconfig/src/fcdefault.c", "/Users/<USER>/work/fontconfig/src/fcdir.c", "/Users/<USER>/work/fontconfig/src/fcformat.c", "/Users/<USER>/work/fontconfig/src/fcfreetype.c", "/Users/<USER>/work/fontconfig/src/fcfs.c", "/Users/<USER>/work/fontconfig/src/fcptrlist.c", "/Users/<USER>/work/fontconfig/src/fchash.c", "/Users/<USER>/work/fontconfig/src/fcinit.c", "/Users/<USER>/work/fontconfig/src/fclang.c", "/Users/<USER>/work/fontconfig/src/fclist.c", "/Users/<USER>/work/fontconfig/src/fcmatch.c", "/Users/<USER>/work/fontconfig/src/fcmatrix.c", "/Users/<USER>/work/fontconfig/src/fcname.c", "/Users/<USER>/work/fontconfig/src/fcobjs.c", "/Users/<USER>/work/fontconfig/src/fcrange.c", "/Users/<USER>/work/fontconfig/src/fcserialize.c", "/Users/<USER>/work/fontconfig/src/fcstat.c", "/Users/<USER>/work/fontconfig/src/fcstr.c", "/Users/<USER>/work/fontconfig/src/fcweight.c", "/Users/<USER>/work/fontconfig/src/fcxml.c", "/Users/<USER>/work/fontconfig/src/ftglue.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["ar"], "parameters": ["csr"]}], "extra_files": [], "subproject": null, "dependencies": ["freetype2", "expat", "", "threads", "m"], "depends": [], "installed": false}, {"name": "fc-cache", "id": "47e81e8@@fc-cache@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/fc-cache/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-cache/fc-cache"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-cache/fc-cache.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-cache", "-I/Users/<USER>/work/fontconfig/fc-cache", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/fc-cache/fc-cache.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,@loader_path/..", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.1.dylib"]}], "extra_files": [], "subproject": null, "dependencies": [""], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["/usr/local/bin/fc-cache"]}, {"name": "fc-cat", "id": "ad4d76b@@fc-cat@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/fc-cat/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-cat/fc-cat"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-cat/fc-cat.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-cat", "-I/Users/<USER>/work/fontconfig/fc-cat", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/fc-cat/fc-cat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,@loader_path/..", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.1.dylib"]}], "extra_files": [], "subproject": null, "dependencies": [""], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["/usr/local/bin/fc-cat"]}, {"name": "fc-conflist", "id": "d80630e@@fc-conflist@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/fc-conflist/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-conflist/fc-conflist"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-conflist/fc-conflist.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-conflist", "-I/Users/<USER>/work/fontconfig/fc-conflist", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/fc-conflist/fc-conflist.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,@loader_path/..", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.1.dylib"]}], "extra_files": [], "subproject": null, "dependencies": [""], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["/usr/local/bin/fc-conflist"]}, {"name": "fc-list", "id": "cd1ad0b@@fc-list@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/fc-list/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-list/fc-list"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-list/fc-list.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-list", "-I/Users/<USER>/work/fontconfig/fc-list", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/fc-list/fc-list.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,@loader_path/..", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.1.dylib"]}], "extra_files": [], "subproject": null, "dependencies": [""], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["/usr/local/bin/fc-list"]}, {"name": "fc-match", "id": "11b523d@@fc-match@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/fc-match/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-match/fc-match"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-match/fc-match.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-match", "-I/Users/<USER>/work/fontconfig/fc-match", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/fc-match/fc-match.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,@loader_path/..", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.1.dylib"]}], "extra_files": [], "subproject": null, "dependencies": [""], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["/usr/local/bin/fc-match"]}, {"name": "fc-pattern", "id": "d8415d1@@fc-pattern@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/fc-pattern/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-pattern/fc-pattern"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-pattern/fc-pattern.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-pattern", "-I/Users/<USER>/work/fontconfig/fc-pattern", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/fc-pattern/fc-pattern.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,@loader_path/..", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.1.dylib"]}], "extra_files": [], "subproject": null, "dependencies": [""], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["/usr/local/bin/fc-pattern"]}, {"name": "fc-query", "id": "dbbe49a@@fc-query@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/fc-query/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-query/fc-query"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-query/fc-query.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-query", "-I/Users/<USER>/work/fontconfig/fc-query", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-I/usr/local/opt/freetype/include/freetype2", "-I/usr/local/opt/libpng/include/libpng16", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/fc-query/fc-query.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,@loader_path/..", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.1.dylib", "/usr/local/opt/freetype/lib/libfreetype.dylib"]}], "extra_files": [], "subproject": null, "dependencies": ["freetype2", ""], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["/usr/local/bin/fc-query"]}, {"name": "fc-scan", "id": "dbc03db@@fc-scan@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/fc-scan/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-scan/fc-scan"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-scan/fc-scan.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-scan", "-I/Users/<USER>/work/fontconfig/fc-scan", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-I/usr/local/opt/freetype/include/freetype2", "-I/usr/local/opt/libpng/include/libpng16", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/fc-scan/fc-scan.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,@loader_path/..", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.1.dylib", "/usr/local/opt/freetype/lib/libfreetype.dylib"]}], "extra_files": [], "subproject": null, "dependencies": ["freetype2", ""], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["/usr/local/bin/fc-scan"]}, {"name": "fc-validate", "id": "6b7e39e@@fc-validate@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/fc-validate/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/fc-validate/fc-validate"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-validate/fc-validate.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-validate", "-I/Users/<USER>/work/fontconfig/fc-validate", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-I/usr/local/opt/freetype/include/freetype2", "-I/usr/local/opt/libpng/include/libpng16", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/fc-validate/fc-validate.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,@loader_path/..", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.1.dylib", "/usr/local/opt/freetype/lib/libfreetype.dylib"]}], "extra_files": [], "subproject": null, "dependencies": ["freetype2", ""], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["/usr/local/bin/fc-validate"]}, {"name": "fetch_test_fonts", "id": "9f86d08@@fetch_test_fonts@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/testfonts"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/usr/local/opt/python@3.13/bin/python3.13", "/Users/<USER>/work/fontconfig/build-aux/fetch-testfonts.py", "--target-dir", "@BUILD_ROOT@/testfonts", "--try-symlink"], "parameters": [], "sources": [], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "test_bz89617", "id": "9f86d08@@test_bz89617@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz89617"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz89617.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H", "-DSRCDIR=\"/Users/<USER>/work/fontconfig/test\""], "sources": ["/Users/<USER>/work/fontconfig/test/test-bz89617.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_bz131804", "id": "9f86d08@@test_bz131804@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz131804"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz131804.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-bz131804.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_bz96676", "id": "9f86d08@@test_bz96676@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz96676"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz96676.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-bz96676.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_name_parse", "id": "9f86d08@@test_name_parse@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_name_parse"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_name_parse.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-name-parse.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_bz106618", "id": "9f86d08@@test_bz106618@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz106618"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz106618.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-bz106618.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_bz1744377", "id": "9f86d08@@test_bz1744377@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz1744377"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz1744377.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-bz1744377.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_issue180", "id": "9f86d08@@test_issue180@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_issue180"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_issue180.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-issue180.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_family_matching", "id": "9f86d08@@test_family_matching@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_family_matching"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_family_matching.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-family-matching.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_ptrlist", "id": "9f86d08@@test_ptrlist@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_ptrlist"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_ptrlist.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-ptrlist.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [""], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_bz106632", "id": "9f86d08@@test_bz106632@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz106632"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_bz106632.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H", "-DFONTFILE=\"/Users/<USER>/work/fontconfig/test/4x6.pcf\""], "sources": ["/Users/<USER>/work/fontconfig/test/test-bz106632.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_issue107", "id": "9f86d08@@test_issue107@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_issue107"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_issue107.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-issue107.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_crbug1004254", "id": "9f86d08@@test_crbug1004254@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_crbug1004254"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_crbug1004254.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-crbug1004254.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": ["threads"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_mt_fccfg", "id": "9f86d08@@test_mt_fccfg@exe", "type": "executable", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/test_mt_fccfg"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-I/Users/<USER>/work/fontconfig/build_meson_dll/test/test_mt_fccfg.p", "-I/Users/<USER>/work/fontconfig/build_meson_dll/test", "-I/Users/<USER>/work/fontconfig/test", "-I/Users/<USER>/work/fontconfig/build_meson_dll", "-I/Users/<USER>/work/fontconfig", "-I/Users/<USER>/work/fontconfig/build_meson_dll/fc-lang", "-I/Users/<USER>/work/fontconfig/fc-lang", "-I/Users/<USER>/work/fontconfig/build_meson_dll/src", "-I/Users/<USER>/work/fontconfig/src", "-fdiagnostics-color=always", "-Wall", "-Winvalid-pch", "-std=c11", "-O0", "-g", "-DHAVE_CONFIG_H"], "sources": ["/Users/<USER>/work/fontconfig/test/test-mt-fccfg.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,-dead_strip_dylibs", "-Wl,-headerpad_max_install_names", "-Wl,-rpath,/usr/local/opt/freetype/lib", "libfontconfig.a", "src/libpatternlib_internal.a", "/usr/local/opt/freetype/lib/libfreetype.dylib", "-lexpat", "-lm", "-lexpat"]}], "extra_files": [], "subproject": null, "dependencies": ["threads"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "out.expected", "id": "9f86d08@@out.expected@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/test/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/test/out.expected"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/usr/local/bin/meson", "--internal", "copy", "@INPUT@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/test/out.expected-no-long-family-names"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "35-lang-normalize.conf", "id": "885d5e4@@35-lang-normalize.conf@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/conf.d/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/conf.d/35-lang-normalize.conf"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/conf.d/write-35-lang-normalize-conf.py", "aa,ab,af,am,ar,as,ast,av,ay,ba,bm,be,bg,bh,bho,bi,bin,bn,bo,br,bs,bua,ca,ce,ch,chm,chr,co,cs,cu,cv,cy,da,de,dz,el,en,eo,es,et,eu,fa,fi,fj,fo,fr,ff,fur,fy,ga,gd,gez,gl,gn,gu,gv,ha,haw,he,hi,ho,hr,hu,hy,ia,ig,id,ie,ik,io,is,it,iu,ja,ka,kaa,ki,kk,kl,km,kn,ko,kok,ks,kum,kv,kw,ky,la,lb,lez,ln,lo,lt,lv,mg,mh,mi,mk,ml,mo,mr,mt,my,nb,nds,ne,nl,nn,no,nr,nso,ny,oc,om,or,os,pa,pl,pt,rm,ro,ru,sa,sah,sco,se,sel,sh,shs,si,sk,sl,sm,sma,smj,smn,sms,so,sq,sr,ss,st,sv,sw,syr,ta,te,tg,th,tig,tk,tl,tn,to,tr,ts,tt,tw,tyv,ug,uk,ur,uz,ve,vi,vo,vot,wa,wen,wo,xh,yap,yi,yo,zu,ak,an,byn,crh,csb,dv,ee,fat,fil,hne,hsb,ht,hz,ii,jv,kab,kj,kr,kwm,lg,li,mai,ms,na,ng,nv,ota,qu,quz,rn,rw,sc,sd,sg,sid,sn,su,ty,wal,za,lah,nqo,brx,sat,doi,mni,anp,bhb,hif,mag,raj,the,agr,ayc,bem,ckb,cmn,dsb,hak,lij,lzh,mfe,mhr,miq,mjw,mnw,nan,nhn,niu,rif,sgs,shn,szl,tcy,tpi,unm,wae,yue,yuw,got,cop", "@OUTPUT@"], "parameters": [], "sources": [], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": true, "install_filename": ["/usr/local/share/fontconfig/conf.avail/35-lang-normalize.conf"]}, {"name": "fontconfig-devel.sgml", "id": "139d544@@fontconfig-devel.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fontconfig-devel.sgml"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/usr/local/bin/meson", "--internal", "copy", "@INPUT@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/fontconfig-devel.sgml"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fontconfig-user.sgml", "id": "139d544@@fontconfig-user.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fontconfig-user.sgml"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/usr/local/bin/meson", "--internal", "copy", "@INPUT@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/fontconfig-user.sgml"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcatomic.sgml", "id": "139d544@@fcatomic.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcatomic.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcatomic.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcblanks.sgml", "id": "139d544@@fcblanks.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcblanks.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcblanks.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fccache.sgml", "id": "139d544@@fccache.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fccache.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fccache.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fccharset.sgml", "id": "139d544@@fccharset.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fccharset.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fccharset.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcconfig.sgml", "id": "139d544@@fcconfig.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcconfig.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcconfig.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcconstant.sgml", "id": "139d544@@fcconstant.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcconstant.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcconstant.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcdircache.sgml", "id": "139d544@@fcdircache.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcdircache.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcdircache.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcfile.sgml", "id": "139d544@@fcfile.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcfile.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcfile.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcfontset.sgml", "id": "139d544@@fcfontset.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcfontset.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcfontset.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcfontations.sgml", "id": "139d544@@fcfontations.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcfontations.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcfontations.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcformat.sgml", "id": "139d544@@fcformat.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcformat.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcformat.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcfreetype.sgml", "id": "139d544@@fcfreetype.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcfreetype.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcfreetype.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcinit.sgml", "id": "139d544@@fcinit.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcinit.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcinit.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fclangset.sgml", "id": "139d544@@fclangset.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fclangset.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fclangset.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcmatrix.sgml", "id": "139d544@@fcmatrix.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcmatrix.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcmatrix.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcobjectset.sgml", "id": "139d544@@fcobjectset.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcobjectset.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcobjectset.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcobjecttype.sgml", "id": "139d544@@fcobjecttype.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcobjecttype.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcobjecttype.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcpattern.sgml", "id": "139d544@@fcpattern.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcpattern.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcpattern.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcrange.sgml", "id": "139d544@@fcrange.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcrange.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcrange.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcstring.sgml", "id": "139d544@@fcstring.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcstring.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcstring.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcstrset.sgml", "id": "139d544@@fcstrset.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcstrset.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcstrset.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcvalue.sgml", "id": "139d544@@fcvalue.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcvalue.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcvalue.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "fcweight.sgml", "id": "139d544@@fcweight.sgml@cus", "type": "custom", "defined_in": "/Users/<USER>/work/fontconfig/doc/meson.build", "filename": ["/Users/<USER>/work/fontconfig/build_meson_dll/doc/fcweight.sgml"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["/Users/<USER>/work/fontconfig/doc/edit-sgml.py", "@INPUT0@", "@INPUT1@", "@OUTPUT@"], "parameters": [], "sources": ["/Users/<USER>/work/fontconfig/doc/func.sgml", "/Users/<USER>/work/fontconfig/doc/fcweight.fncs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}]