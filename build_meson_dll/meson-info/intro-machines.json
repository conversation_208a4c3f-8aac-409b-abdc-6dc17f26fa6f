{"host": {"system": "darwin", "cpu_family": "x86_64", "cpu": "x86_64", "endian": "little", "kernel": "xnu", "subsystem": "macos", "is_64_bit": true, "exe_suffix": "", "object_suffix": "o"}, "build": {"system": "darwin", "cpu_family": "x86_64", "cpu": "x86_64", "endian": "little", "kernel": "xnu", "subsystem": "macos", "is_64_bit": true, "exe_suffix": "", "object_suffix": "o"}, "target": {"system": "darwin", "cpu_family": "x86_64", "cpu": "x86_64", "endian": "little", "kernel": "xnu", "subsystem": "macos", "is_64_bit": true, "exe_suffix": "", "object_suffix": "o"}}