######################################
###  AUTOMATICALLY GENERATED FILE  ###
######################################

# This file was generated from the configuration in the
# relevant meson machine file. See the meson documentation
# https://mesonbuild.com/Machine-files.html for more information

if(DEFINED MESON_PRELOAD_FILE)
    include("${MESON_PRELOAD_FILE}")
endif()

# CMake compiler state variables
# -- Variables for language c
set(CMAKE_C_COMPILER "/usr/bin/cc")
set(CMAKE_C_COMPILER_ID "AppleClang")
set(CMAKE_C_COMPILER_VERSION "14.0.0.14000029")
set(CMAKE_C_STANDARD_COMPUTED_DEFAULT "17")
set(CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT "ON")
set(CMAKE_C_PLATFORM_ID "Darwin")
set(CMAKE_C_COMPILER_FRONTEND_VARIANT "GNU")
set(CMAKE_C_COMPILER_APPLE_SYSROOT "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk")
set(CMAKE_AR "/usr/bin/ar")
set(CMAKE_RANLIB "/usr/bin/ranlib")
set(CMAKE_LINKER "/usr/bin/ld")
set(CMAKE_TAPI "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi")
set(CMAKE_C_COMPILER_LOADED "1")
set(CMAKE_C_COMPILER_ENV_VAR "CC")
set(CMAKE_C_COMPILER_ID_RUN "1")
set(CMAKE_C_SOURCE_FILE_EXTENSIONS "c" "m")
set(CMAKE_C_IGNORE_EXTENSIONS "h" "H" "o" "O" "obj" "OBJ" "def" "DEF" "rc" "RC")
set(CMAKE_C_LINKER_PREFERENCE "10")
set(CMAKE_C_STANDARD_LATEST "23")
set(CMAKE_C_COMPILE_FEATURES "c_std_90" "c_function_prototypes" "c_std_99" "c_restrict" "c_variadic_macros" "c_std_11" "c_static_assert" "c_std_17" "c_std_23")
set(CMAKE_C90_COMPILE_FEATURES "c_std_90" "c_function_prototypes")
set(CMAKE_C99_COMPILE_FEATURES "c_std_99" "c_restrict" "c_variadic_macros")
set(CMAKE_C11_COMPILE_FEATURES "c_std_11" "c_static_assert")
set(CMAKE_C17_COMPILE_FEATURES "c_std_17")
set(CMAKE_C23_COMPILE_FEATURES "c_std_23")
set(CMAKE_C_COMPILER_LINKER "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld")
set(CMAKE_C_COMPILER_LINKER_ID "AppleClang")
set(CMAKE_C_COMPILER_LINKER_VERSION "820.1")
set(CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT "GNU")
set(CMAKE_C_COMPILER_WORKS "TRUE")
set(CMAKE_C_ABI_COMPILED "TRUE")
set(CMAKE_C_SIZEOF_DATA_PTR "8")
set(CMAKE_C_BYTE_ORDER "LITTLE_ENDIAN")
set(CMAKE_SIZEOF_VOID_P "8")
set(CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES "/usr/local/include" "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include" "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include" "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include")
set(CMAKE_C_IMPLICIT_LINK_DIRECTORIES "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib")
set(CMAKE_C_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks")
set(CMAKE_C_COMPILER_FORCED "1")


# Variables from meson
set(CMAKE_SIZEOF_VOID_P "8")
set(CMAKE_C_COMPILER "/usr/bin/cc")

