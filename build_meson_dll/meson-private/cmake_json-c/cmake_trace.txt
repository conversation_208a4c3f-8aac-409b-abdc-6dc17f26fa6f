{"version":{"major":1,"minor":2}}
{"args":["VERSION","4.0.2"],"cmd":"cmake_minimum_required","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":2,"time":1753231094.334934}
{"args":["MesonTemp","LANGUAGES","C"],"cmd":"project","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":3,"time":1753231094.3349841}
{"args":["CMAKE_HOST_UNIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":11,"time":1753231094.3353131}
{"args":["CMAKE_UNAME","NAMES","uname","PATHS","/bin","/usr/bin","/usr/local/bin"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":12,"time":1753231094.3353479}
{"args":["CMAKE_UNAME"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":13,"time":1753231094.335706}
{"args":["CMAKE_HOST_SYSTEM_NAME","STREQUAL","AIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":14,"time":1753231094.335732}
{"args":["CMAKE_HOST_SYSTEM_NAME","STREQUAL","Android"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":26,"time":1753231094.3357561}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":48,"time":1753231094.3357711}
{"args":["COMMAND","/usr/bin/uname","-r","OUTPUT_VARIABLE","CMAKE_HOST_SYSTEM_VERSION","OUTPUT_STRIP_TRAILING_WHITESPACE","ERROR_QUIET"],"cmd":"execute_process","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":49,"line_end":52,"time":1753231094.3357899}
{"args":["CMAKE_HOST_SYSTEM_NAME","MATCHES","Linux|CYGWIN.*|MSYS.*|^GNU$|Android"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":54,"time":1753231094.3376241}
{"args":["CMAKE_HOST_SYSTEM_NAME","MATCHES","Darwin"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":60,"time":1753231094.3376551}
{"args":["DEFINED","CMAKE_APPLE_SILICON_PROCESSOR"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":62,"time":1753231094.3376729}
{"args":["DEFINED","ENV{CMAKE_APPLE_SILICON_PROCESSOR}"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":64,"time":1753231094.3376889}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":66,"time":1753231094.337702}
{"args":["_CMAKE_APPLE_SILICON_PROCESSOR",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":67,"time":1753231094.337714}
{"args":["_CMAKE_APPLE_SILICON_PROCESSOR"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":69,"time":1753231094.337728}
{"args":["_CMAKE_APPLE_SILICON_PROCESSOR"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":84,"time":1753231094.3377421}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":86,"time":1753231094.337754}
{"args":["COMMAND","/usr/bin/uname","-m","OUTPUT_VARIABLE","CMAKE_HOST_SYSTEM_PROCESSOR","RESULT_VARIABLE","val","OUTPUT_STRIP_TRAILING_WHITESPACE","ERROR_QUIET"],"cmd":"execute_process","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":87,"line_end":91,"time":1753231094.3377681}
{"args":["_CMAKE_APPLE_SILICON_PROCESSOR"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":93,"time":1753231094.3393519}
{"args":["CMAKE_HOST_SYSTEM_PROCESSOR","STREQUAL","Power Macintosh"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":94,"time":1753231094.339427}
{"args":["0","GREATER","0"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":119,"time":1753231094.339469}
{"args":["CMAKE_UNAME","/usr/bin/uname","CACHE","INTERNAL","uname command"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":122,"time":1753231094.3394909}
{"args":["REPLACE","\"","","CMAKE_HOST_SYSTEM_PROCESSOR","x86_64"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":124,"time":1753231094.3395121}
{"args":["REPLACE","/","_","CMAKE_HOST_SYSTEM_PROCESSOR","x86_64"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":125,"time":1753231094.3395431}
{"args":["CMAKE_TOOLCHAIN_FILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":141,"time":1753231094.3395629}
{"args":["/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c//Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","OPTIONAL","RESULT_VARIABLE","_INCLUDED_TOOLCHAIN_FILE"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":143,"time":1753231094.339587}
{"args":["NOT","_INCLUDED_TOOLCHAIN_FILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":144,"time":1753231094.339637}
{"args":["/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","OPTIONAL","RESULT_VARIABLE","_INCLUDED_TOOLCHAIN_FILE"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":146,"time":1753231094.3396599}
{"args":["DEFINED","MESON_PRELOAD_FILE"],"cmd":"if","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":9,"time":1753231094.339819}
{"args":["CMAKE_C_COMPILER","/usr/bin/cc"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":15,"time":1753231094.3398449}
{"args":["CMAKE_C_COMPILER_ID","AppleClang"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":16,"time":1753231094.3398609}
{"args":["CMAKE_C_COMPILER_VERSION","14.0.0.14000029"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":17,"time":1753231094.3398759}
{"args":["CMAKE_C_STANDARD_COMPUTED_DEFAULT","17"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":18,"time":1753231094.339891}
{"args":["CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT","ON"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":19,"time":1753231094.339911}
{"args":["CMAKE_C_PLATFORM_ID","Darwin"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":20,"time":1753231094.3399251}
{"args":["CMAKE_C_COMPILER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":21,"time":1753231094.3399401}
{"args":["CMAKE_C_COMPILER_APPLE_SYSROOT","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":22,"time":1753231094.3399551}
{"args":["CMAKE_AR","/usr/bin/ar"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":23,"time":1753231094.3399971}
{"args":["CMAKE_RANLIB","/usr/bin/ranlib"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":24,"time":1753231094.340013}
{"args":["CMAKE_LINKER","/usr/bin/ld"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":25,"time":1753231094.3400271}
{"args":["CMAKE_TAPI","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":26,"time":1753231094.3400409}
{"args":["CMAKE_C_COMPILER_LOADED","1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":27,"time":1753231094.3400569}
{"args":["CMAKE_C_COMPILER_ENV_VAR","CC"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":28,"time":1753231094.340071}
{"args":["CMAKE_C_COMPILER_ID_RUN","1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":29,"time":1753231094.340085}
{"args":["CMAKE_C_SOURCE_FILE_EXTENSIONS","c","m"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":30,"time":1753231094.3400991}
{"args":["CMAKE_C_IGNORE_EXTENSIONS","h","H","o","O","obj","OBJ","def","DEF","rc","RC"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":31,"time":1753231094.340117}
{"args":["CMAKE_C_LINKER_PREFERENCE","10"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":32,"time":1753231094.3401351}
{"args":["CMAKE_C_STANDARD_LATEST","23"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":33,"time":1753231094.3401489}
{"args":["CMAKE_C_COMPILE_FEATURES","c_std_90","c_function_prototypes","c_std_99","c_restrict","c_variadic_macros","c_std_11","c_static_assert","c_std_17","c_std_23"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":34,"time":1753231094.3401661}
{"args":["CMAKE_C90_COMPILE_FEATURES","c_std_90","c_function_prototypes"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":35,"time":1753231094.340184}
{"args":["CMAKE_C99_COMPILE_FEATURES","c_std_99","c_restrict","c_variadic_macros"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":36,"time":1753231094.3401999}
{"args":["CMAKE_C11_COMPILE_FEATURES","c_std_11","c_static_assert"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":37,"time":1753231094.340215}
{"args":["CMAKE_C17_COMPILE_FEATURES","c_std_17"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":38,"time":1753231094.3402491}
{"args":["CMAKE_C23_COMPILE_FEATURES","c_std_23"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":39,"time":1753231094.340265}
{"args":["CMAKE_C_COMPILER_LINKER","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":40,"time":1753231094.3402791}
{"args":["CMAKE_C_COMPILER_LINKER_ID","AppleClang"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":41,"time":1753231094.3403089}
{"args":["CMAKE_C_COMPILER_LINKER_VERSION","820.1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":42,"time":1753231094.3404679}
{"args":["CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":43,"time":1753231094.340497}
{"args":["CMAKE_C_COMPILER_WORKS","TRUE"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":44,"time":1753231094.3405149}
{"args":["CMAKE_C_ABI_COMPILED","TRUE"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":45,"time":1753231094.3405361}
{"args":["CMAKE_C_SIZEOF_DATA_PTR","8"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":46,"time":1753231094.3405521}
{"args":["CMAKE_C_BYTE_ORDER","LITTLE_ENDIAN"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":47,"time":1753231094.3405671}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":48,"time":1753231094.3405809}
{"args":["CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES","/usr/local/include","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":49,"time":1753231094.3405981}
{"args":["CMAKE_C_IMPLICIT_LINK_DIRECTORIES","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":50,"time":1753231094.3406191}
{"args":["CMAKE_C_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":51,"time":1753231094.340636}
{"args":["CMAKE_C_COMPILER_FORCED","1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":52,"time":1753231094.340682}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":56,"time":1753231094.340699}
{"args":["CMAKE_C_COMPILER","/usr/bin/cc"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":57,"time":1753231094.340713}
{"args":["_INCLUDED_TOOLCHAIN_FILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":149,"time":1753231094.3407431}
{"args":["CMAKE_TOOLCHAIN_FILE","/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","CACHE","FILEPATH","The CMake toolchain file","FORCE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":150,"time":1753231094.3407691}
{"args":["CMAKE_SYSTEM_NAME"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":156,"time":1753231094.3408329}
{"args":["CMAKE_VS_WINCE_VERSION"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":162,"time":1753231094.340852}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":167,"time":1753231094.3408639}
{"args":["CMAKE_SYSTEM_NAME","Darwin"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":169,"time":1753231094.3408771}
{"args":["NOT","DEFINED","CMAKE_SYSTEM_VERSION"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":170,"time":1753231094.3408921}
{"args":["CMAKE_SYSTEM_VERSION","21.6.0"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":171,"time":1753231094.340909}
{"args":["CMAKE_SYSTEM_PROCESSOR","x86_64"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":173,"time":1753231094.3409231}
{"args":["CMAKE_CROSSCOMPILING"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":174,"time":1753231094.3409369}
{"args":["CMAKE_CROSSCOMPILING","FALSE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":181,"time":1753231094.340951}
{"args":["Platform/Darwin-Determine","OPTIONAL"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":184,"time":1753231094.340966}
{"args":["CMAKE_SYSTEM","Darwin"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":186,"time":1753231094.341018}
{"args":["CMAKE_SYSTEM_VERSION"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":187,"time":1753231094.341033}
{"args":["APPEND","CMAKE_SYSTEM","-21.6.0"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":188,"time":1753231094.341049}
{"args":["CMAKE_HOST_SYSTEM","Darwin"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":190,"time":1753231094.3410671}
{"args":["CMAKE_HOST_SYSTEM_VERSION"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":191,"time":1753231094.3410809}
{"args":["APPEND","CMAKE_HOST_SYSTEM","-21.6.0"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":192,"time":1753231094.3410959}
{"args":["CMAKE_BINARY_DIR"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":197,"time":1753231094.341135}
{"args":["CMAKE_CROSSCOMPILING"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":199,"time":1753231094.341152}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":204,"time":1753231094.3411651}
{"args":["CONFIGURE_LOG","The system is: Darwin - 21.6.0 - x86_64\n"],"cmd":"message","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":205,"line_end":207,"time":1753231094.3411789}
{"args":["INCLUDE_CMAKE_TOOLCHAIN_FILE_IF_REQUIRED"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":212,"time":1753231094.3413849}
{"args":["CMAKE_TOOLCHAIN_FILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":213,"time":1753231094.341404}
{"args":["INCLUDE_CMAKE_TOOLCHAIN_FILE_IF_REQUIRED","include(\"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake\")"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":214,"time":1753231094.3414221}
{"args":["/usr/local/share/cmake/Modules/CMakeSystem.cmake.in","/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","@ONLY"],"cmd":"configure_file","file":"/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":218,"line_end":220,"time":1753231094.3414431}
{"args":["CMAKE_HOST_SYSTEM","Darwin-21.6.0"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":1,"time":1753231094.342082}
{"args":["CMAKE_HOST_SYSTEM_NAME","Darwin"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":2,"time":1753231094.342104}
{"args":["CMAKE_HOST_SYSTEM_VERSION","21.6.0"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":3,"time":1753231094.3421309}
{"args":["CMAKE_HOST_SYSTEM_PROCESSOR","x86_64"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":4,"time":1753231094.3421459}
{"args":["/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake"],"cmd":"include","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":6,"time":1753231094.342171}
{"args":["DEFINED","MESON_PRELOAD_FILE"],"cmd":"if","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":9,"time":1753231094.3423359}
{"args":["CMAKE_C_COMPILER","/usr/bin/cc"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":15,"time":1753231094.3423669}
{"args":["CMAKE_C_COMPILER_ID","AppleClang"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":16,"time":1753231094.342416}
{"args":["CMAKE_C_COMPILER_VERSION","14.0.0.14000029"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":17,"time":1753231094.3424549}
{"args":["CMAKE_C_STANDARD_COMPUTED_DEFAULT","17"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":18,"time":1753231094.3425341}
{"args":["CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT","ON"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":19,"time":1753231094.3425601}
{"args":["CMAKE_C_PLATFORM_ID","Darwin"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":20,"time":1753231094.3425829}
{"args":["CMAKE_C_COMPILER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":21,"time":1753231094.3425961}
{"args":["CMAKE_C_COMPILER_APPLE_SYSROOT","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":22,"time":1753231094.3426099}
{"args":["CMAKE_AR","/usr/bin/ar"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":23,"time":1753231094.3426249}
{"args":["CMAKE_RANLIB","/usr/bin/ranlib"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":24,"time":1753231094.342638}
{"args":["CMAKE_LINKER","/usr/bin/ld"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":25,"time":1753231094.3426499}
{"args":["CMAKE_TAPI","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":26,"time":1753231094.342663}
{"args":["CMAKE_C_COMPILER_LOADED","1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":27,"time":1753231094.3426771}
{"args":["CMAKE_C_COMPILER_ENV_VAR","CC"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":28,"time":1753231094.34269}
{"args":["CMAKE_C_COMPILER_ID_RUN","1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":29,"time":1753231094.3427031}
{"args":["CMAKE_C_SOURCE_FILE_EXTENSIONS","c","m"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":30,"time":1753231094.342716}
{"args":["CMAKE_C_IGNORE_EXTENSIONS","h","H","o","O","obj","OBJ","def","DEF","rc","RC"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":31,"time":1753231094.342732}
{"args":["CMAKE_C_LINKER_PREFERENCE","10"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":32,"time":1753231094.3427491}
{"args":["CMAKE_C_STANDARD_LATEST","23"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":33,"time":1753231094.342762}
{"args":["CMAKE_C_COMPILE_FEATURES","c_std_90","c_function_prototypes","c_std_99","c_restrict","c_variadic_macros","c_std_11","c_static_assert","c_std_17","c_std_23"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":34,"time":1753231094.3427949}
{"args":["CMAKE_C90_COMPILE_FEATURES","c_std_90","c_function_prototypes"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":35,"time":1753231094.342813}
{"args":["CMAKE_C99_COMPILE_FEATURES","c_std_99","c_restrict","c_variadic_macros"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":36,"time":1753231094.342828}
{"args":["CMAKE_C11_COMPILE_FEATURES","c_std_11","c_static_assert"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":37,"time":1753231094.3428421}
{"args":["CMAKE_C17_COMPILE_FEATURES","c_std_17"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":38,"time":1753231094.3428559}
{"args":["CMAKE_C23_COMPILE_FEATURES","c_std_23"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":39,"time":1753231094.3428681}
{"args":["CMAKE_C_COMPILER_LINKER","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":40,"time":1753231094.342881}
{"args":["CMAKE_C_COMPILER_LINKER_ID","AppleClang"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":41,"time":1753231094.342895}
{"args":["CMAKE_C_COMPILER_LINKER_VERSION","820.1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":42,"time":1753231094.3429079}
{"args":["CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":43,"time":1753231094.342921}
{"args":["CMAKE_C_COMPILER_WORKS","TRUE"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":44,"time":1753231094.3429339}
{"args":["CMAKE_C_ABI_COMPILED","TRUE"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":45,"time":1753231094.342947}
{"args":["CMAKE_C_SIZEOF_DATA_PTR","8"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":46,"time":1753231094.3429589}
{"args":["CMAKE_C_BYTE_ORDER","LITTLE_ENDIAN"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":47,"time":1753231094.342972}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":48,"time":1753231094.342984}
{"args":["CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES","/usr/local/include","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":49,"time":1753231094.342998}
{"args":["CMAKE_C_IMPLICIT_LINK_DIRECTORIES","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":50,"time":1753231094.3430331}
{"args":["CMAKE_C_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":51,"time":1753231094.343049}
{"args":["CMAKE_C_COMPILER_FORCED","1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":52,"time":1753231094.3430631}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":56,"time":1753231094.343076}
{"args":["CMAKE_C_COMPILER","/usr/bin/cc"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":57,"time":1753231094.3430879}
{"args":["CMAKE_SYSTEM","Darwin-21.6.0"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":8,"time":1753231094.3431101}
{"args":["CMAKE_SYSTEM_NAME","Darwin"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":9,"time":1753231094.343122}
{"args":["CMAKE_SYSTEM_VERSION","21.6.0"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":10,"time":1753231094.3431351}
{"args":["CMAKE_SYSTEM_PROCESSOR","x86_64"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":11,"time":1753231094.343147}
{"args":["CMAKE_CROSSCOMPILING","FALSE"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":13,"time":1753231094.343159}
{"args":["CMAKE_SYSTEM_LOADED","1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeSystem.cmake","frame":2,"global_frame":2,"line":15,"time":1753231094.3431709}
{"args":["CMAKE_MAKE_PROGRAM","NAMES","gmake","make","smake"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeUnixFindMake.cmake","frame":2,"global_frame":2,"line":5,"time":1753231094.3432789}
{"args":["CMAKE_MAKE_PROGRAM"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeUnixFindMake.cmake","frame":2,"global_frame":2,"line":6,"time":1753231094.34409}
{"args":["APPLE"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":13,"time":1753231094.344202}
{"args":["UNIX"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":14,"time":1753231094.344218}
{"args":["CYGWIN"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":15,"time":1753231094.3442299}
{"args":["MSYS"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":16,"time":1753231094.344264}
{"args":["WIN32"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":17,"time":1753231094.3442769}
{"args":["BSD"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":18,"time":1753231094.3442881}
{"args":["LINUX"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":19,"time":1753231094.3443029}
{"args":["AIX"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":20,"time":1753231094.3443151}
{"args":["CMAKE_EFFECTIVE_SYSTEM_NAME","Darwin"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":33,"time":1753231094.344327}
{"args":["Platform/Darwin-Initialize","OPTIONAL"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":35,"time":1753231094.344347}
{"args":["APPLE","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":1,"time":1753231094.34465}
{"args":["UNIX","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":2,"time":1753231094.3446679}
{"args":["COMMAND","xcode-select","-print-path","OUTPUT_VARIABLE","_stdout","OUTPUT_STRIP_TRAILING_WHITESPACE","ERROR_VARIABLE","_stderr","RESULT_VARIABLE","_failed"],"cmd":"execute_process","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":5,"line_end":9,"time":1753231094.3446851}
{"args":["NOT","_failed","AND","IS_DIRECTORY","/Applications/Xcode.app/Contents/Developer"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":10,"time":1753231094.347224}
{"args":["OSX_DEVELOPER_ROOT","/Applications/Xcode.app/Contents/Developer"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":11,"time":1753231094.3472641}
{"args":["COMMAND","sw_vers","-productVersion","OUTPUT_VARIABLE","CURRENT_OSX_VERSION","OUTPUT_STRIP_TRAILING_WHITESPACE"],"cmd":"execute_process","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":18,"line_end":20,"time":1753231094.347285}
{"args":["CMAKE_OSX_ARCHITECTURES","","CACHE","STRING","Build architectures for OSX"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":23,"line_end":24,"time":1753231094.3582971}
{"args":["NOT","CMAKE_CROSSCOMPILING","AND","CMAKE_SYSTEM_NAME","STREQUAL","Darwin","AND","CMAKE_HOST_SYSTEM_PROCESSOR","MATCHES","^(arm64|x86_64)$"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":26,"line_end":28,"time":1753231094.358341}
{"args":["COMMAND","sysctl","-q","hw.optional.arm64","OUTPUT_VARIABLE","_sysctl_stdout","ERROR_VARIABLE","_sysctl_stderr","RESULT_VARIABLE","_sysctl_result"],"cmd":"execute_process","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":29,"line_end":33,"time":1753231094.358407}
{"args":["_sysctl_result","EQUAL","0","AND","_sysctl_stdout","MATCHES","hw.optional.arm64: 1"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":38,"time":1753231094.3603899}
{"args":["_sysctl_result"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":41,"time":1753231094.36043}
{"args":["_sysctl_stderr"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":42,"time":1753231094.360487}
{"args":["_sysctl_stdout"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":43,"time":1753231094.3605011}
{"args":["CMAKE_EFFECTIVE_SYSTEM_NAME","Apple"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":48,"time":1753231094.3605161}
{"args":["REGEX","REPLACE","^([0-9]+\\.[0-9]+).*$","\\1","_CURRENT_OSX_VERSION","12.7.6"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":53,"line_end":54,"time":1753231094.3605349}
{"args":["CMAKE_SYSTEM_NAME","STREQUAL","Darwin","AND","_CURRENT_OSX_VERSION","VERSION_GREATER","10.3"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":60,"time":1753231094.3605621}
{"args":["CMAKE_OSX_DEPLOYMENT_TARGET","","CACHE","STRING","Minimum OS X version to target for deployment (at runtime); newer APIs weak linked. Set to empty string for default value."],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":61,"line_end":62,"time":1753231094.3605919}
{"args":["CMAKE_OSX_SYSROOT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":68,"time":1753231094.3606131}
{"args":["NOT","x","STREQUAL","x","AND","(","NOT","x","MATCHES","/","OR","IS_DIRECTORY","",")"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":71,"line_end":72,"time":1753231094.3606341}
{"args":["CMAKE_SYSTEM_NAME","STREQUAL","iOS"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":75,"time":1753231094.3606601}
{"args":["CMAKE_SYSTEM_NAME","STREQUAL","tvOS"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":77,"time":1753231094.3606739}
{"args":["CMAKE_SYSTEM_NAME","STREQUAL","visionOS"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":79,"time":1753231094.360687}
{"args":["CMAKE_SYSTEM_NAME","STREQUAL","watchOS"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":81,"time":1753231094.3606999}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":83,"time":1753231094.3607121}
{"args":["_CMAKE_OSX_SYSROOT_DEFAULT",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":84,"time":1753231094.360724}
{"args":["_CMAKE_OSX_SYSROOT_TYPE","STRING"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":89,"time":1753231094.360738}
{"args":["_v","CMAKE_OSX_SYSROOT","_CMAKE_OSX_SYSROOT_DEFAULT"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":90,"time":1753231094.3607521}
{"args":["x","MATCHES","/"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":91,"time":1753231094.360774}
{"args":["x","MATCHES","/"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":91,"time":1753231094.360791}
{"args":["CMAKE_OSX_SYSROOT","","CACHE","STRING","The product will be built against the headers and libraries located inside the indicated SDK."],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":96,"line_end":97,"time":1753231094.360811}
{"args":["_apple_resolve_sdk_path","sdk_name","ret"],"cmd":"function","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":100,"time":1753231094.360852}
{"args":["_apple_resolve_supported_archs_for_sdk_from_system_lib","sdk_path","ret","ret_failed"],"cmd":"function","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":111,"time":1753231094.3608761}
{"args":["_apple_resolve_multi_arch_sysroots"],"cmd":"function","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":195,"time":1753231094.360899}
{"args":[],"cmd":"_apple_resolve_multi_arch_sysroots","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":261,"time":1753231094.3609221}
{"args":["DEFINED","CMAKE_APPLE_ARCH_SYSROOTS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":4,"global_frame":4,"line":196,"time":1753231094.3609419}
{"args":["LENGTH","CMAKE_OSX_ARCHITECTURES","_num_archs"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":4,"global_frame":4,"line":200,"time":1753231094.3609591}
{"args":["NOT","(","_num_archs","GREATER","1",")"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":4,"global_frame":4,"line":201,"time":1753231094.3609791}
{"args":[],"cmd":"return","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":4,"global_frame":4,"line":202,"time":1753231094.360997}
{"args":["CMAKE_OSX_SYSROOT","MATCHES","/"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":263,"time":1753231094.3610129}
{"args":["CMAKE_OSX_SYSROOT"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":271,"time":1753231094.3610301}
{"args":["NOT","CMAKE_OSX_SYSROOT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":279,"time":1753231094.361042}
{"args":["CMAKE_GENERATOR","STREQUAL","Xcode"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":282,"time":1753231094.361057}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":284,"time":1753231094.3610699}
{"args":["_sdk_macosx"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":285,"time":1753231094.3610809}
{"args":["COMMAND","xcrun","","--show-sdk-path","OUTPUT_VARIABLE","_CMAKE_OSX_SYSROOT_PATH","OUTPUT_STRIP_TRAILING_WHITESPACE","ERROR_VARIABLE","_stderr","RESULT_VARIABLE","_result"],"cmd":"execute_process","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":287,"line_end":293,"time":1753231094.3610981}
{"args":["_sdk_macosx"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":294,"time":1753231094.3699329}
{"args":["APPEND","CMAKE_PLATFORM_IMPLICIT_LINK_DIRECTORIES_EXCLUDE","/usr/local/lib"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake","frame":3,"global_frame":3,"line":296,"line_end":301,"time":1753231094.3699601}
{"args":["CMAKE_SYSTEM_SPECIFIC_INITIALIZE_LOADED","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":37,"time":1753231094.3700149}
{"args":["/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":24,"time":1753231094.370266}
{"args":["_cmake_find_compiler","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":5,"time":1753231094.370461}
{"args":["_cmake_find_compiler_path","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":87,"time":1753231094.370533}
{"args":["_cmake_find_compiler_sysroot","lang"],"cmd":"function","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":131,"time":1753231094.3705571}
{"args":["Platform/Darwin-Determine-C","OPTIONAL"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":27,"time":1753231094.370584}
{"args":["Platform/Darwin-C","OPTIONAL"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":28,"time":1753231094.3706329}
{"args":["NOT","CMAKE_C_COMPILER_NAMES"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":29,"time":1753231094.370662}
{"args":["CMAKE_C_COMPILER_NAMES","cc"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":30,"time":1753231094.3706801}
{"args":["Unix Makefiles","MATCHES","Visual Studio"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":33,"time":1753231094.3706951}
{"args":["Unix Makefiles","MATCHES","Green Hills MULTI"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":34,"time":1753231094.3707161}
{"args":["Unix Makefiles","MATCHES","Xcode"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":35,"time":1753231094.3707359}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":38,"time":1753231094.37075}
{"args":["NOT","CMAKE_C_COMPILER"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":39,"time":1753231094.3707609}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":67,"time":1753231094.3707759}
{"args":["C"],"cmd":"_cmake_find_compiler_path","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":68,"time":1753231094.3707881}
{"args":["CMAKE_C_COMPILER"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":88,"time":1753231094.3708041}
{"args":["CONVERT","/usr/bin/cc","TO_CMAKE_PATH_LIST","CMAKE_C_COMPILER","NORMALIZE"],"cmd":"cmake_path","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":97,"time":1753231094.3708429}
{"args":["LENGTH","CMAKE_C_COMPILER","_CMAKE_C_COMPILER_LENGTH"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":99,"time":1753231094.3708761}
{"args":["_CMAKE_C_COMPILER_LENGTH","GREATER","1"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":100,"time":1753231094.370893}
{"args":["_CMAKE_C_COMPILER_LENGTH"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":105,"time":1753231094.370914}
{"args":["_CMAKE_USER_C_COMPILER_PATH","/usr/bin/cc","PATH"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":109,"time":1753231094.370928}
{"args":["NOT","_CMAKE_USER_C_COMPILER_PATH"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":110,"time":1753231094.3709431}
{"args":["EXISTS","/usr/bin/cc"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":121,"time":1753231094.370959}
{"args":["_CMAKE_C_COMPILER_CACHED","CACHE","CMAKE_C_COMPILER","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":122,"time":1753231094.3710301}
{"args":["_CMAKE_C_COMPILER_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":123,"time":1753231094.3710761}
{"args":["_CMAKE_C_COMPILER_CACHED"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":126,"time":1753231094.3710921}
{"args":["CMAKE_C_COMPILER"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":70,"time":1753231094.3711109}
{"args":["CMAKE_C_COMPILER_ID_TEST_FLAGS_FIRST"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":75,"time":1753231094.371124}
{"args":["CMAKE_C_COMPILER_ID_TEST_FLAGS","-c","-Aa","-D__CLASSIC_C__","--target=arm-arm-none-eabi -mcpu=cortex-m3","-c -I__does_not_exist__"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":76,"line_end":93,"time":1753231094.371139}
{"args":["CMAKE_C_COMPILER_TARGET"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":95,"time":1753231094.3711579}
{"args":["NOT","CMAKE_C_COMPILER_ID_RUN"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":99,"time":1753231094.371171}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":131,"time":1753231094.371186}
{"args":["NOT","DEFINED","CMAKE_C_COMPILER_FRONTEND_VARIANT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":132,"time":1753231094.3711979}
{"args":["NOT","_CMAKE_TOOLCHAIN_LOCATION"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":150,"time":1753231094.3712161}
{"args":["_CMAKE_TOOLCHAIN_LOCATION","/usr/bin/cc","PATH"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":151,"time":1753231094.3712299}
{"args":["NOT","_CMAKE_TOOLCHAIN_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":161,"time":1753231094.3712449}
{"args":["CMAKE_C_COMPILER_ID","MATCHES","GNU|Clang|QCC|LCC"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":163,"time":1753231094.3712609}
{"args":["COMPILER_BASENAME","/usr/bin/cc","NAME"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":164,"time":1753231094.3712809}
{"args":["COMPILER_BASENAME","MATCHES","^(.+-)?(clang|g?cc)(-cl)?(-?[0-9]+(\\.[0-9]+)*)?(-[^.]+)?(\\.exe)?$"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":165,"time":1753231094.371295}
{"args":["_CMAKE_TOOLCHAIN_PREFIX",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":166,"time":1753231094.37132}
{"args":["_CMAKE_TOOLCHAIN_SUFFIX",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":167,"time":1753231094.3713341}
{"args":["_CMAKE_COMPILER_SUFFIX",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":168,"time":1753231094.371347}
{"args":["","MATCHES","(.+-)?llvm-$"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":186,"time":1753231094.371362}
{"args":["_CMAKE_PROCESSING_LANGUAGE","C"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":200,"time":1753231094.3713801}
{"args":["CMakeFindBinUtils"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":201,"time":1753231094.371413}
{"args":["__resolve_tool_path","CMAKE_TOOL","SEARCH_PATH","DOCSTRING"],"cmd":"function","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":25,"time":1753231094.371701}
{"args":["CMAKE_LINKER","/usr/bin","Default Linker"],"cmd":"__resolve_tool_path","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":58,"time":1753231094.3717279}
{"args":["CMAKE_LINKER"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":4,"global_frame":4,"line":27,"time":1753231094.3717539}
{"args":["_CMAKE_USER_TOOL_PATH","/usr/bin/ld","DIRECTORY"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.3717721}
{"args":["NOT","_CMAKE_USER_TOOL_PATH"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":4,"global_frame":4,"line":34,"time":1753231094.3717871}
{"args":["CMAKE_MT","/usr/bin","Default Manifest Tool"],"cmd":"__resolve_tool_path","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":59,"time":1753231094.37181}
{"args":["CMAKE_MT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":4,"global_frame":4,"line":27,"time":1753231094.371829}
{"args":["__resolve_linker_path","__linker_type","__name","__search_path","__doc"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":61,"time":1753231094.3718469}
{"args":["_CMAKE_TOOL_VARS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":68,"time":1753231094.3718669}
{"args":["(","x","STREQUAL","xMSVC","AND","(","xGNU","STREQUAL","xMSVC","OR","NOT","xAppleClang","STREQUAL","xClang",")",")","OR","xAppleClang","STREQUAL","xMSVC","OR","(","CMAKE_HOST_WIN32","AND","xAppleClang","STREQUAL","xPGI",")","OR","(","CMAKE_HOST_WIN32","AND","xAppleClang","STREQUAL","xNVIDIA",")","OR","(","CMAKE_HOST_WIN32","AND","xC","STREQUAL","xISPC",")","OR","(","CMAKE_GENERATOR","MATCHES","Visual Studio","AND","NOT","CMAKE_VS_PLATFORM_NAME","STREQUAL","Tegra-Android",")"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":71,"line_end":79,"time":1753231094.3719029}
{"args":["xAppleClang","MATCHES","^x(Open)?Watcom$"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":106,"time":1753231094.3719721}
{"args":["xAppleClang","MATCHES","^xIAR$"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":111,"time":1753231094.37199}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":153,"time":1753231094.372004}
{"args":["CMAKE_C_COMPILER_EXTERNAL_TOOLCHAIN"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":154,"time":1753231094.372016}
{"args":["CMAKE_CXX_COMPILER_EXTERNAL_TOOLCHAIN"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":157,"time":1753231094.37203}
{"args":["_CMAKE_AR_NAMES","ar"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":162,"time":1753231094.3720441}
{"args":["_CMAKE_RANLIB_NAMES","ranlib"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":163,"time":1753231094.372056}
{"args":["_CMAKE_STRIP_NAMES","strip"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":164,"time":1753231094.3720689}
{"args":["_CMAKE_LINKER_NAMES","ld"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":165,"time":1753231094.3721039}
{"args":["_CMAKE_NM_NAMES","nm"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":166,"time":1753231094.372117}
{"args":["_CMAKE_OBJDUMP_NAMES","objdump"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":167,"time":1753231094.372129}
{"args":["_CMAKE_OBJCOPY_NAMES","objcopy"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":168,"time":1753231094.3721409}
{"args":["_CMAKE_READELF_NAMES","readelf"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":169,"time":1753231094.3721521}
{"args":["_CMAKE_DLLTOOL_NAMES","dlltool"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":170,"time":1753231094.372164}
{"args":["_CMAKE_ADDR2LINE_NAMES","addr2line"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":171,"time":1753231094.3721759}
{"args":["_CMAKE_TAPI_NAMES","tapi"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":172,"time":1753231094.3721881}
{"args":["AppleClang","STREQUAL","Clang"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":175,"time":1753231094.372201}
{"args":["AppleClang","STREQUAL","ARMClang"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":202,"time":1753231094.3722191}
{"args":["APPEND","_CMAKE_TOOL_VARS","AR","RANLIB","STRIP","LINKER","NM","OBJDUMP","OBJCOPY","READELF","DLLTOOL","ADDR2LINE","TAPI"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":207,"time":1753231094.372237}
{"args":["_CMAKE_TOOL","IN","LISTS","_CMAKE_TOOL_VARS"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":210,"time":1753231094.3722579}
{"args":["_CMAKE_AR_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.3722751}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_AR_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.3722889}
{"args":["APPEND","_CMAKE_AR_FIND_NAMES","ar","ar","ar","ar"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.372308}
{"args":["REMOVE_DUPLICATES","_CMAKE_AR_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.3723271}
{"args":["CMAKE_AR","NAMES","ar","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.37235}
{"args":["_CMAKE_AR_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.372416}
{"args":["_CMAKE_RANLIB_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.372432}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_RANLIB_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.3724461}
{"args":["APPEND","_CMAKE_RANLIB_FIND_NAMES","ranlib","ranlib","ranlib","ranlib"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.3724661}
{"args":["REMOVE_DUPLICATES","_CMAKE_RANLIB_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.372508}
{"args":["CMAKE_RANLIB","NAMES","ranlib","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.3725281}
{"args":["_CMAKE_RANLIB_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.372586}
{"args":["_CMAKE_STRIP_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.372602}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_STRIP_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.3726161}
{"args":["APPEND","_CMAKE_STRIP_FIND_NAMES","strip","strip","strip","strip"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.3726361}
{"args":["REMOVE_DUPLICATES","_CMAKE_STRIP_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.3726549}
{"args":["CMAKE_STRIP","NAMES","strip","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.372673}
{"args":["_CMAKE_STRIP_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.3730259}
{"args":["_CMAKE_LINKER_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.373044}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_LINKER_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.373059}
{"args":["APPEND","_CMAKE_LINKER_FIND_NAMES","ld","ld","ld","ld"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.37308}
{"args":["REMOVE_DUPLICATES","_CMAKE_LINKER_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.3731}
{"args":["CMAKE_LINKER","NAMES","ld","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.3731179}
{"args":["_CMAKE_LINKER_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.3731749}
{"args":["_CMAKE_NM_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.3731911}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_NM_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.3732049}
{"args":["APPEND","_CMAKE_NM_FIND_NAMES","nm","nm","nm","nm"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.373225}
{"args":["REMOVE_DUPLICATES","_CMAKE_NM_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.3732431}
{"args":["CMAKE_NM","NAMES","nm","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.373261}
{"args":["_CMAKE_NM_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.3739851}
{"args":["_CMAKE_OBJDUMP_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.3740029}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_OBJDUMP_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.3740189}
{"args":["APPEND","_CMAKE_OBJDUMP_FIND_NAMES","objdump","objdump","objdump","objdump"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.3740389}
{"args":["REMOVE_DUPLICATES","_CMAKE_OBJDUMP_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.374059}
{"args":["CMAKE_OBJDUMP","NAMES","objdump","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.374078}
{"args":["_CMAKE_OBJDUMP_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.374393}
{"args":["_CMAKE_OBJCOPY_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.3744111}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_OBJCOPY_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.3744259}
{"args":["APPEND","_CMAKE_OBJCOPY_FIND_NAMES","objcopy","objcopy","objcopy","objcopy"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.3744459}
{"args":["REMOVE_DUPLICATES","_CMAKE_OBJCOPY_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.3744659}
{"args":["CMAKE_OBJCOPY","NAMES","objcopy","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.374485}
{"args":["_CMAKE_OBJCOPY_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.374578}
{"args":["_CMAKE_READELF_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.374594}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_READELF_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.374608}
{"args":["APPEND","_CMAKE_READELF_FIND_NAMES","readelf","readelf","readelf","readelf"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.3746271}
{"args":["REMOVE_DUPLICATES","_CMAKE_READELF_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.3746469}
{"args":["CMAKE_READELF","NAMES","readelf","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.374665}
{"args":["_CMAKE_READELF_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.374753}
{"args":["_CMAKE_DLLTOOL_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.3747809}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_DLLTOOL_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.3747971}
{"args":["APPEND","_CMAKE_DLLTOOL_FIND_NAMES","dlltool","dlltool","dlltool","dlltool"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.3748159}
{"args":["REMOVE_DUPLICATES","_CMAKE_DLLTOOL_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.374836}
{"args":["CMAKE_DLLTOOL","NAMES","dlltool","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.3748541}
{"args":["_CMAKE_DLLTOOL_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.3749421}
{"args":["_CMAKE_ADDR2LINE_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.374958}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_ADDR2LINE_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.3749721}
{"args":["APPEND","_CMAKE_ADDR2LINE_FIND_NAMES","addr2line","addr2line","addr2line","addr2line"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.3749909}
{"args":["REMOVE_DUPLICATES","_CMAKE_ADDR2LINE_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.375011}
{"args":["CMAKE_ADDR2LINE","NAMES","addr2line","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.3750291}
{"args":["_CMAKE_ADDR2LINE_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.375114}
{"args":["_CMAKE_TAPI_FIND_NAMES",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.3751299}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_TAPI_NAMES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1753231094.3751431}
{"args":["APPEND","_CMAKE_TAPI_FIND_NAMES","tapi","tapi","tapi","tapi"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1753231094.3751631}
{"args":["REMOVE_DUPLICATES","_CMAKE_TAPI_FIND_NAMES"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1753231094.3751819}
{"args":["CMAKE_TAPI","NAMES","tapi","HINTS","/usr/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1753231094.3751991}
{"args":["_CMAKE_TAPI_FIND_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1753231094.375236}
{"args":["NOT","CMAKE_RANLIB"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":227,"time":1753231094.3752511}
{"args":["APPLE","AND","TAPI","IN_LIST","_CMAKE_TOOL_VARS","AND","NOT","CMAKE_TAPI"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":231,"time":1753231094.375268}
{"args":["CMAKE_PLATFORM_HAS_INSTALLNAME"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":246,"time":1753231094.3753021}
{"args":["_CMAKE_TOOL","IN","LISTS","_CMAKE_TOOL_VARS"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":257,"time":1753231094.375319}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_AR","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.3753359}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.375351}
{"args":["_CMAKE_AR_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.3753641}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_RANLIB","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.3753779}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.375391}
{"args":["_CMAKE_RANLIB_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.3754039}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_STRIP","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.375417}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.3754311}
{"args":["CMAKE_STRIP"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1753231094.375443}
{"args":["_CMAKE_STRIP_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.3754561}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_LINKER","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.375469}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.375483}
{"args":["_CMAKE_LINKER_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.375495}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_NM","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.3755081}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.3755209}
{"args":["CMAKE_NM"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1753231094.3755331}
{"args":["_CMAKE_NM_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.375546}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_OBJDUMP","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.3755591}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.375572}
{"args":["CMAKE_OBJDUMP"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1753231094.3755839}
{"args":["_CMAKE_OBJDUMP_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.375607}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_OBJCOPY","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.3756211}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.3756349}
{"args":["CMAKE_OBJCOPY"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1753231094.3756471}
{"args":["_CMAKE_OBJCOPY_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.3756599}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_READELF","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.3756731}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.3756859}
{"args":["CMAKE_READELF"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1753231094.3756981}
{"args":["_CMAKE_READELF_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.375711}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_DLLTOOL","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.3757229}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.375737}
{"args":["CMAKE_DLLTOOL"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1753231094.3757491}
{"args":["_CMAKE_DLLTOOL_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.375761}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_ADDR2LINE","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.3757739}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.375788}
{"args":["CMAKE_ADDR2LINE"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1753231094.3757999}
{"args":["_CMAKE_ADDR2LINE_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.3758121}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_TAPI","PROPERTY","TYPE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.3758249}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.375838}
{"args":["_CMAKE_TAPI_NAMES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1753231094.37585}
{"args":["_CMAKE_TOOL_VARS"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":264,"time":1753231094.3758631}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":265,"time":1753231094.375874}
{"args":["_CMAKE_TOOL_NAME"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":266,"time":1753231094.3758931}
{"args":["_CMAKE_TOOL"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":267,"time":1753231094.375905}
{"args":["xAppleClang","MATCHES","^xIAR$"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":269,"time":1753231094.3759179}
{"args":["Compiler/AppleClang-FindBinUtils","OPTIONAL"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":202,"time":1753231094.3759551}
{"args":["_CMAKE_PROCESSING_LANGUAGE"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":203,"time":1753231094.375998}
{"args":["CMAKE_C_COMPILER_SYSROOT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":205,"time":1753231094.3760121}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":209,"time":1753231094.376025}
{"args":["_SET_CMAKE_C_COMPILER_SYSROOT",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":210,"time":1753231094.3760369}
{"args":["CMAKE_C_COMPILER_ARCHITECTURE_ID"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":213,"time":1753231094.37605}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":216,"time":1753231094.3760619}
{"args":["_SET_CMAKE_C_COMPILER_ARCHITECTURE_ID",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":217,"time":1753231094.3760729}
{"args":["MSVC_C_ARCHITECTURE_ID"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":220,"time":1753231094.376087}
{"args":["CMAKE_C_XCODE_ARCHS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":225,"time":1753231094.3760991}
{"args":["/usr/local/share/cmake/Modules/CMakeCCompiler.cmake.in","/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","@ONLY"],"cmd":"configure_file","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":231,"line_end":234,"time":1753231094.3761151}
{"args":["CMAKE_C_COMPILER_ENV_VAR","CC"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":235,"time":1753231094.3765759}
{"args":["CMAKE_C_COMPILER","/usr/bin/cc"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":1,"time":1753231094.3767121}
{"args":["CMAKE_C_COMPILER_ARG1",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":2,"time":1753231094.3767309}
{"args":["CMAKE_C_COMPILER_ID","AppleClang"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":3,"time":1753231094.376745}
{"args":["CMAKE_C_COMPILER_VERSION","14.0.0.14000029"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":4,"time":1753231094.3767591}
{"args":["CMAKE_C_COMPILER_VERSION_INTERNAL",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":5,"time":1753231094.3767731}
{"args":["CMAKE_C_COMPILER_WRAPPER",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":6,"time":1753231094.3767991}
{"args":["CMAKE_C_STANDARD_COMPUTED_DEFAULT","17"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":7,"time":1753231094.3768129}
{"args":["CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT","ON"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":8,"time":1753231094.376827}
{"args":["CMAKE_C_STANDARD_LATEST","23"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":9,"time":1753231094.3768401}
{"args":["CMAKE_C_COMPILE_FEATURES","c_std_90;c_function_prototypes;c_std_99;c_restrict;c_variadic_macros;c_std_11;c_static_assert;c_std_17;c_std_23"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":10,"time":1753231094.376853}
{"args":["CMAKE_C90_COMPILE_FEATURES","c_std_90;c_function_prototypes"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":11,"time":1753231094.376868}
{"args":["CMAKE_C99_COMPILE_FEATURES","c_std_99;c_restrict;c_variadic_macros"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":12,"time":1753231094.3768821}
{"args":["CMAKE_C11_COMPILE_FEATURES","c_std_11;c_static_assert"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":13,"time":1753231094.3768959}
{"args":["CMAKE_C17_COMPILE_FEATURES","c_std_17"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":14,"time":1753231094.37691}
{"args":["CMAKE_C23_COMPILE_FEATURES","c_std_23"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":15,"time":1753231094.3769231}
{"args":["CMAKE_C_PLATFORM_ID","Darwin"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":17,"time":1753231094.376936}
{"args":["CMAKE_C_SIMULATE_ID",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":18,"time":1753231094.3769481}
{"args":["CMAKE_C_COMPILER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":19,"time":1753231094.376961}
{"args":["CMAKE_C_COMPILER_APPLE_SYSROOT","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":20,"time":1753231094.3769751}
{"args":["CMAKE_C_SIMULATE_VERSION",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":21,"time":1753231094.3769889}
{"args":["CMAKE_AR","/usr/bin/ar"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":26,"time":1753231094.3770101}
{"args":["CMAKE_C_COMPILER_AR",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":27,"time":1753231094.3770239}
{"args":["CMAKE_RANLIB","/usr/bin/ranlib"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":28,"time":1753231094.3770361}
{"args":["CMAKE_C_COMPILER_RANLIB",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":29,"time":1753231094.377049}
{"args":["CMAKE_LINKER","/usr/bin/ld"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":30,"time":1753231094.3770621}
{"args":["CMAKE_LINKER_LINK",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":31,"time":1753231094.377074}
{"args":["CMAKE_LINKER_LLD",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":32,"time":1753231094.3770859}
{"args":["CMAKE_C_COMPILER_LINKER","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":33,"time":1753231094.377099}
{"args":["CMAKE_C_COMPILER_LINKER_ID","AppleClang"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":34,"time":1753231094.3771131}
{"args":["CMAKE_C_COMPILER_LINKER_VERSION","820.1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":35,"time":1753231094.377126}
{"args":["CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":36,"time":1753231094.3771391}
{"args":["CMAKE_MT",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":37,"time":1753231094.377152}
{"args":["CMAKE_TAPI","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":38,"time":1753231094.3771651}
{"args":["CMAKE_COMPILER_IS_GNUCC"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":39,"time":1753231094.3771789}
{"args":["CMAKE_C_COMPILER_LOADED","1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":40,"time":1753231094.3771911}
{"args":["CMAKE_C_COMPILER_WORKS","TRUE"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":41,"time":1753231094.3772039}
{"args":["CMAKE_C_ABI_COMPILED","TRUE"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":42,"time":1753231094.3772249}
{"args":["CMAKE_C_COMPILER_ENV_VAR","CC"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":44,"time":1753231094.377238}
{"args":["CMAKE_C_COMPILER_ID_RUN","1"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":46,"time":1753231094.3772509}
{"args":["CMAKE_C_SOURCE_FILE_EXTENSIONS","c;m"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":47,"time":1753231094.377264}
{"args":["CMAKE_C_IGNORE_EXTENSIONS","h;H;o;O;obj;OBJ;def;DEF;rc;RC"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":48,"time":1753231094.3772781}
{"args":["CMAKE_C_LINKER_PREFERENCE","10"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":49,"time":1753231094.3772931}
{"args":["CMAKE_C_LINKER_DEPFILE_SUPPORTED"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":50,"time":1753231094.377306}
{"args":["CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":51,"time":1753231094.3773191}
{"args":["CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":52,"time":1753231094.377331}
{"args":["CMAKE_C_SIZEOF_DATA_PTR","8"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":55,"time":1753231094.3773429}
{"args":["CMAKE_C_COMPILER_ABI",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":56,"time":1753231094.3773561}
{"args":["CMAKE_C_BYTE_ORDER","LITTLE_ENDIAN"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":57,"time":1753231094.377368}
{"args":["CMAKE_C_LIBRARY_ARCHITECTURE",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":58,"time":1753231094.3773811}
{"args":["CMAKE_C_SIZEOF_DATA_PTR"],"cmd":"if","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":60,"time":1753231094.377393}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":61,"time":1753231094.377409}
{"args":["CMAKE_C_COMPILER_ABI"],"cmd":"if","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":64,"time":1753231094.377423}
{"args":["CMAKE_C_LIBRARY_ARCHITECTURE"],"cmd":"if","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":68,"time":1753231094.3774371}
{"args":["CMAKE_C_CL_SHOWINCLUDES_PREFIX",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":72,"time":1753231094.377459}
{"args":["CMAKE_C_CL_SHOWINCLUDES_PREFIX"],"cmd":"if","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":73,"time":1753231094.3774731}
{"args":["CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES","/usr/local/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":81,"time":1753231094.3774879}
{"args":["CMAKE_C_IMPLICIT_LINK_LIBRARIES",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":82,"time":1753231094.377506}
{"args":["CMAKE_C_IMPLICIT_LINK_DIRECTORIES","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":83,"time":1753231094.3775201}
{"args":["CMAKE_C_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":84,"time":1753231094.3775351}
{"args":["_cmake_record_install_prefix"],"cmd":"function","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":9,"time":1753231094.3776691}
{"args":["CMakeGenericSystem"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":27,"time":1753231094.377691}
{"args":["CMakeInitializeConfigs"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":4,"time":1753231094.377893}
{"args":["GLOBAL"],"cmd":"include_guard","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":4,"time":1753231094.3780029}
{"args":["cmake_initialize_per_config_variable","_PREFIX","_DOCSTRING"],"cmd":"function","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":8,"time":1753231094.3780291}
{"args":["CMAKE_SHARED_LIBRARY_C_FLAGS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":6,"time":1753231094.378058}
{"args":["CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS","-shared"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":7,"time":1753231094.378073}
{"args":["CMAKE_SHARED_LIBRARY_LINK_C_FLAGS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":8,"time":1753231094.3780861}
{"args":["CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":9,"time":1753231094.378099}
{"args":["CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG_SEP",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":10,"time":1753231094.3781109}
{"args":["CMAKE_INCLUDE_FLAG_C","-I"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":11,"time":1753231094.3781359}
{"args":["CMAKE_LIBRARY_PATH_FLAG","-L"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":12,"time":1753231094.37815}
{"args":["CMAKE_LIBRARY_PATH_TERMINATOR",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":13,"time":1753231094.3781631}
{"args":["CMAKE_LINK_LIBRARY_FLAG","-l"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":14,"time":1753231094.378175}
{"args":["CMAKE_LINK_LIBRARY_SUFFIX",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":16,"time":1753231094.3781869}
{"args":["CMAKE_STATIC_LIBRARY_PREFIX","lib"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":17,"time":1753231094.3782001}
{"args":["CMAKE_STATIC_LIBRARY_SUFFIX",".a"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":18,"time":1753231094.378212}
{"args":["CMAKE_SHARED_LIBRARY_PREFIX","lib"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":19,"time":1753231094.3782239}
{"args":["CMAKE_SHARED_LIBRARY_SUFFIX",".so"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":20,"time":1753231094.378237}
{"args":["CMAKE_EXECUTABLE_SUFFIX",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":21,"time":1753231094.3782489}
{"args":["CMAKE_DL_LIBS","dl"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":22,"time":1753231094.3782611}
{"args":["CMAKE_FIND_LIBRARY_PREFIXES","lib"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":24,"time":1753231094.378274}
{"args":["CMAKE_FIND_LIBRARY_SUFFIXES",".so",".a"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":25,"time":1753231094.3782859}
{"args":["CMAKE_LINK_LIBRARY_USING_DEFAULT_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":31,"time":1753231094.378299}
{"args":["NOT","DEFINED","CMAKE_AUTOGEN_ORIGIN_DEPENDS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":33,"time":1753231094.3783131}
{"args":["CMAKE_AUTOGEN_ORIGIN_DEPENDS","ON"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":34,"time":1753231094.37833}
{"args":["NOT","DEFINED","CMAKE_AUTOMOC_COMPILER_PREDEFINES"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":36,"time":1753231094.3783441}
{"args":["CMAKE_AUTOMOC_COMPILER_PREDEFINES","ON"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":37,"time":1753231094.3783591}
{"args":["NOT","DEFINED","CMAKE_AUTOMOC_PATH_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":39,"time":1753231094.378372}
{"args":["CMAKE_AUTOMOC_PATH_PREFIX","OFF"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":40,"time":1753231094.378387}
{"args":["NOT","DEFINED","CMAKE_AUTOMOC_MACRO_NAMES"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":42,"time":1753231094.3784001}
{"args":["CMAKE_AUTOMOC_MACRO_NAMES","Q_OBJECT","Q_GADGET","Q_NAMESPACE","Q_NAMESPACE_EXPORT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":43,"time":1753231094.3784151}
{"args":["GLOBAL","PROPERTY","TARGET_SUPPORTS_SHARED_LIBS","TRUE"],"cmd":"set_property","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":47,"time":1753231094.3784411}
{"args":["CMAKE_SKIP_RPATH","NO","CACHE","BOOL","If set, runtime paths are not added when using shared libraries."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":49,"line_end":50,"time":1753231094.3784621}
{"args":["CMAKE_SKIP_INSTALL_RPATH","NO","CACHE","BOOL","If set, runtime paths are not added when installing shared libraries, but are added when building."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":51,"line_end":52,"time":1753231094.3784809}
{"args":["CMAKE_VERBOSE_MAKEFILE","FALSE","CACHE","BOOL","If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":54,"time":1753231094.3785}
{"args":["DEFINED","ENV{CMAKE_COLOR_DIAGNOSTICS}","AND","NOT","DEFINED","CACHE{CMAKE_COLOR_DIAGNOSTICS}"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":56,"time":1753231094.3785181}
{"args":["CMAKE_GENERATOR","MATCHES","Make"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":60,"time":1753231094.3785379}
{"args":["NOT","DEFINED","CMAKE_COLOR_DIAGNOSTICS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":61,"time":1753231094.378556}
{"args":["CMAKE_COLOR_MAKEFILE","ON","CACHE","BOOL","Enable/Disable color output during build."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":62,"time":1753231094.378572}
{"args":["CMAKE_COLOR_MAKEFILE"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":64,"time":1753231094.378587}
{"args":["DEFINED","CMAKE_RULE_MESSAGES"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":66,"time":1753231094.3785999}
{"args":["DEFINED","CMAKE_TARGET_MESSAGES"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":69,"time":1753231094.3786139}
{"args":["NOT","DEFINED","CMAKE_EXPORT_COMPILE_COMMANDS","AND","CMAKE_GENERATOR","MATCHES","Ninja|Unix Makefiles"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":74,"time":1753231094.378629}
{"args":["CMAKE_EXPORT_COMPILE_COMMANDS","","CACHE","BOOL","Enable/Disable output of compile commands during generation."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":75,"line_end":77,"time":1753231094.378653}
{"args":["CMAKE_EXPORT_COMPILE_COMMANDS"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":78,"time":1753231094.3786709}
{"args":["NOT","DEFINED","CMAKE_EXPORT_BUILD_DATABASE","AND","CMAKE_GENERATOR","MATCHES","Ninja"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":81,"time":1753231094.378686}
{"args":["GetDefaultWindowsPrefixBase","var"],"cmd":"function","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":95,"time":1753231094.378706}
{"args":["NOT","DEFINED","CMAKE_INSTALL_PREFIX","AND","NOT","DEFINED","ENV{CMAKE_INSTALL_PREFIX}"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":187,"line_end":188,"time":1753231094.378727}
{"args":["CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":189,"time":1753231094.378757}
{"args":["DEFINED","ENV{CMAKE_INSTALL_PREFIX}"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":192,"time":1753231094.378778}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":195,"time":1753231094.378792}
{"args":["CMAKE_HOST_UNIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":198,"time":1753231094.378803}
{"args":["CMAKE_INSTALL_PREFIX","/usr/local","CACHE","PATH","Install path prefix, prepended onto install directories."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":199,"line_end":200,"time":1753231094.3788171}
{"args":["CMAKE_INSTALL_DEFAULT_COMPONENT_NAME","Unspecified"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":212,"time":1753231094.378835}
{"args":["CMAKE_SKIP_RPATH","CMAKE_SKIP_INSTALL_RPATH","CMAKE_VERBOSE_MAKEFILE"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":214,"line_end":218,"time":1753231094.378849}
{"args":["CMAKE_SYSTEM_INFO_FILE","Platform/Darwin"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":30,"time":1753231094.37888}
{"args":["Platform/Darwin","OPTIONAL","RESULT_VARIABLE","_INCLUDED_SYSTEM_INFO_FILE"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":32,"time":1753231094.378895}
{"args":["CMAKE_SYSTEM_NAME","STREQUAL","iOS","OR","CMAKE_SYSTEM_NAME","STREQUAL","tvOS","OR","CMAKE_SYSTEM_NAME","STREQUAL","visionOS","OR","CMAKE_SYSTEM_NAME","STREQUAL","watchOS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":1,"time":1753231094.3791821}
{"args":["REGEX","REPLACE","^([0-9]+)\\.([0-9]+).*$","\\1","DARWIN_MAJOR_VERSION","21.6.0"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":26,"time":1753231094.379214}
{"args":["REGEX","REPLACE","^([0-9]+)\\.([0-9]+).*$","\\2","DARWIN_MINOR_VERSION","21.6.0"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":27,"time":1753231094.37924}
{"args":["NOT","DEFINED","HAVE_FLAG_SEARCH_PATHS_FIRST"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":31,"time":1753231094.379261}
{"args":["HAVE_FLAG_SEARCH_PATHS_FIRST","0"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":32,"time":1753231094.3792779}
{"args":["21","GREATER","6"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":33,"time":1753231094.379293}
{"args":["HAVE_FLAG_SEARCH_PATHS_FIRST","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":34,"time":1753231094.379308}
{"args":["CMAKE_SHARED_LIBRARY_PREFIX","lib"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":41,"time":1753231094.3793221}
{"args":["CMAKE_SHARED_LIBRARY_SUFFIX",".dylib"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":42,"time":1753231094.3793349}
{"args":["CMAKE_EXTRA_SHARED_LIBRARY_SUFFIXES",".tbd",".so"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":43,"time":1753231094.379348}
{"args":["CMAKE_SHARED_MODULE_PREFIX","lib"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":44,"time":1753231094.3793621}
{"args":["CMAKE_SHARED_MODULE_SUFFIX",".so"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":45,"time":1753231094.3793869}
{"args":["CMAKE_APPLE_IMPORT_FILE_PREFIX","lib"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":46,"time":1753231094.379401}
{"args":["CMAKE_APPLE_IMPORT_FILE_SUFFIX",".tbd"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":47,"time":1753231094.3794129}
{"args":["CMAKE_MODULE_EXISTS","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":48,"time":1753231094.379425}
{"args":["CMAKE_DL_LIBS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":49,"time":1753231094.3794379}
{"args":["NOT","12.7","VERSION_LESS","10.5"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":50,"time":1753231094.379451}
{"args":["CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG","-Wl,-rpath,"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":51,"time":1753231094.379467}
{"args":["lang","C","CXX","OBJC","OBJCXX"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":54,"time":1753231094.379482}
{"args":["CMAKE_C_OSX_COMPATIBILITY_VERSION_FLAG","-compatibility_version "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":55,"time":1753231094.379498}
{"args":["CMAKE_C_OSX_CURRENT_VERSION_FLAG","-current_version "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":56,"time":1753231094.3795121}
{"args":["CMAKE_C_LINK_FLAGS","-Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":57,"time":1753231094.3795259}
{"args":["HAVE_FLAG_SEARCH_PATHS_FIRST"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":59,"time":1753231094.37954}
{"args":["CMAKE_C_LINK_FLAGS","-Wl,-search_paths_first -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":60,"time":1753231094.379555}
{"args":["CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS","-dynamiclib -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":63,"time":1753231094.37957}
{"args":["CMAKE_SHARED_MODULE_CREATE_C_FLAGS","-bundle -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":64,"time":1753231094.379585}
{"args":["CMAKE_SHARED_MODULE_LOADER_C_FLAG","-Wl,-bundle_loader,"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":65,"time":1753231094.3795991}
{"args":["CMAKE_CXX_OSX_COMPATIBILITY_VERSION_FLAG","-compatibility_version "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":55,"time":1753231094.3796129}
{"args":["CMAKE_CXX_OSX_CURRENT_VERSION_FLAG","-current_version "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":56,"time":1753231094.379627}
{"args":["CMAKE_CXX_LINK_FLAGS","-Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":57,"time":1753231094.3796401}
{"args":["HAVE_FLAG_SEARCH_PATHS_FIRST"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":59,"time":1753231094.379653}
{"args":["CMAKE_CXX_LINK_FLAGS","-Wl,-search_paths_first -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":60,"time":1753231094.3796771}
{"args":["CMAKE_SHARED_LIBRARY_CREATE_CXX_FLAGS","-dynamiclib -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":63,"time":1753231094.379694}
{"args":["CMAKE_SHARED_MODULE_CREATE_CXX_FLAGS","-bundle -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":64,"time":1753231094.3797109}
{"args":["CMAKE_SHARED_MODULE_LOADER_CXX_FLAG","-Wl,-bundle_loader,"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":65,"time":1753231094.379725}
{"args":["CMAKE_OBJC_OSX_COMPATIBILITY_VERSION_FLAG","-compatibility_version "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":55,"time":1753231094.379739}
{"args":["CMAKE_OBJC_OSX_CURRENT_VERSION_FLAG","-current_version "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":56,"time":1753231094.3797531}
{"args":["CMAKE_OBJC_LINK_FLAGS","-Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":57,"time":1753231094.379766}
{"args":["HAVE_FLAG_SEARCH_PATHS_FIRST"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":59,"time":1753231094.3797791}
{"args":["CMAKE_OBJC_LINK_FLAGS","-Wl,-search_paths_first -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":60,"time":1753231094.3797939}
{"args":["CMAKE_SHARED_LIBRARY_CREATE_OBJC_FLAGS","-dynamiclib -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":63,"time":1753231094.3798079}
{"args":["CMAKE_SHARED_MODULE_CREATE_OBJC_FLAGS","-bundle -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":64,"time":1753231094.379823}
{"args":["CMAKE_SHARED_MODULE_LOADER_OBJC_FLAG","-Wl,-bundle_loader,"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":65,"time":1753231094.379838}
{"args":["CMAKE_OBJCXX_OSX_COMPATIBILITY_VERSION_FLAG","-compatibility_version "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":55,"time":1753231094.3798521}
{"args":["CMAKE_OBJCXX_OSX_CURRENT_VERSION_FLAG","-current_version "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":56,"time":1753231094.3798659}
{"args":["CMAKE_OBJCXX_LINK_FLAGS","-Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":57,"time":1753231094.37988}
{"args":["HAVE_FLAG_SEARCH_PATHS_FIRST"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":59,"time":1753231094.3798959}
{"args":["CMAKE_OBJCXX_LINK_FLAGS","-Wl,-search_paths_first -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":60,"time":1753231094.3799109}
{"args":["CMAKE_SHARED_LIBRARY_CREATE_OBJCXX_FLAGS","-dynamiclib -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":63,"time":1753231094.379926}
{"args":["CMAKE_SHARED_MODULE_CREATE_OBJCXX_FLAGS","-bundle -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":64,"time":1753231094.37994}
{"args":["CMAKE_SHARED_MODULE_LOADER_OBJCXX_FLAG","-Wl,-bundle_loader,"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":65,"time":1753231094.3799629}
{"args":["CMAKE_PLATFORM_HAS_INSTALLNAME","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":68,"time":1753231094.3799789}
{"args":["CMAKE_FIND_LIBRARY_SUFFIXES",".tbd",".dylib",".so",".a"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":69,"time":1753231094.379992}
{"args":["NOT","DEFINED","CMAKE_INSTALL_NAME_TOOL"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":75,"time":1753231094.380007}
{"args":["CMAKE_INSTALL_NAME_TOOL","install_name_tool"],"cmd":"find_program","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":76,"time":1753231094.380023}
{"args":["CMAKE_INSTALL_NAME_TOOL"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":77,"time":1753231094.3810079}
{"args":["CMAKE_SHARED_LIBRARY_SONAME_C_FLAG","-install_name"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":81,"time":1753231094.381027}
{"args":["12.7","VERSION_LESS","10.5"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":83,"time":1753231094.381043}
{"args":["lang","C","CXX","Fortran","OBJC","OBJCXX"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":91,"time":1753231094.3810611}
{"args":["XCODE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":93,"time":1753231094.3810771}
{"args":["CMAKE_C_CREATE_SHARED_LIBRARY","<CMAKE_C_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":97,"line_end":98,"time":1753231094.3810921}
{"args":["CMAKE_C_CREATE_SHARED_MODULE","<CMAKE_C_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_MODULE_CREATE_C_FLAGS> <LINK_FLAGS> -o <TARGET> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":100,"line_end":101,"time":1753231094.3811109}
{"args":["CMAKE_C_CREATE_MACOSX_FRAMEWORK","<CMAKE_C_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":103,"line_end":104,"time":1753231094.3811281}
{"args":["CMAKE_C_FRAMEWORK_SEARCH_FLAG","-F"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":108,"time":1753231094.381144}
{"args":["XCODE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":93,"time":1753231094.3811581}
{"args":["CMAKE_CXX_CREATE_SHARED_LIBRARY","<CMAKE_CXX_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_CXX_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":97,"line_end":98,"time":1753231094.3811719}
{"args":["CMAKE_CXX_CREATE_SHARED_MODULE","<CMAKE_CXX_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_MODULE_CREATE_CXX_FLAGS> <LINK_FLAGS> -o <TARGET> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":100,"line_end":101,"time":1753231094.3811891}
{"args":["CMAKE_CXX_CREATE_MACOSX_FRAMEWORK","<CMAKE_CXX_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_CXX_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":103,"line_end":104,"time":1753231094.3812189}
{"args":["CMAKE_CXX_FRAMEWORK_SEARCH_FLAG","-F"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":108,"time":1753231094.381237}
{"args":["XCODE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":93,"time":1753231094.3812499}
{"args":["CMAKE_Fortran_CREATE_SHARED_LIBRARY","<CMAKE_Fortran_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_Fortran_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":97,"line_end":98,"time":1753231094.381264}
{"args":["CMAKE_Fortran_CREATE_SHARED_MODULE","<CMAKE_Fortran_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_MODULE_CREATE_Fortran_FLAGS> <LINK_FLAGS> -o <TARGET> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":100,"line_end":101,"time":1753231094.3812809}
{"args":["CMAKE_Fortran_CREATE_MACOSX_FRAMEWORK","<CMAKE_Fortran_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_Fortran_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":103,"line_end":104,"time":1753231094.3812971}
{"args":["CMAKE_Fortran_FRAMEWORK_SEARCH_FLAG","-F"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":108,"time":1753231094.3813131}
{"args":["XCODE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":93,"time":1753231094.381326}
{"args":["CMAKE_OBJC_CREATE_SHARED_LIBRARY","<CMAKE_OBJC_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_OBJC_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":97,"line_end":98,"time":1753231094.3813391}
{"args":["CMAKE_OBJC_CREATE_SHARED_MODULE","<CMAKE_OBJC_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_MODULE_CREATE_OBJC_FLAGS> <LINK_FLAGS> -o <TARGET> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":100,"line_end":101,"time":1753231094.381356}
{"args":["CMAKE_OBJC_CREATE_MACOSX_FRAMEWORK","<CMAKE_OBJC_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_OBJC_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":103,"line_end":104,"time":1753231094.381376}
{"args":["CMAKE_OBJC_FRAMEWORK_SEARCH_FLAG","-F"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":108,"time":1753231094.381392}
{"args":["XCODE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":93,"time":1753231094.3814051}
{"args":["CMAKE_OBJCXX_CREATE_SHARED_LIBRARY","<CMAKE_OBJCXX_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_OBJCXX_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":97,"line_end":98,"time":1753231094.3814189}
{"args":["CMAKE_OBJCXX_CREATE_SHARED_MODULE","<CMAKE_OBJCXX_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_MODULE_CREATE_OBJCXX_FLAGS> <LINK_FLAGS> -o <TARGET> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":100,"line_end":101,"time":1753231094.381454}
{"args":["CMAKE_OBJCXX_CREATE_MACOSX_FRAMEWORK","<CMAKE_OBJCXX_COMPILER> <LANGUAGE_COMPILE_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_OBJCXX_FLAGS> <LINK_FLAGS> -o <TARGET> <SONAME_FLAG> <TARGET_INSTALLNAME_DIR><TARGET_SONAME> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":103,"line_end":104,"time":1753231094.3814721}
{"args":["CMAKE_OBJCXX_FRAMEWORK_SEARCH_FLAG","-F"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":108,"time":1753231094.3814881}
{"args":["CMAKE_CREATE_TEXT_STUBS","<CMAKE_TAPI> stubify -isysroot <CMAKE_OSX_SYSROOT> -o <TARGET_IMPLIB> <TARGET>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":112,"time":1753231094.3815019}
{"args":["CMAKE_LINK_LIBRARY_USING_FRAMEWORK","LINKER:-framework,<LIBRARY>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":115,"time":1753231094.381516}
{"args":["CMAKE_LINK_LIBRARY_USING_FRAMEWORK_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":116,"time":1753231094.38153}
{"args":["CMAKE_LINK_LIBRARY_FRAMEWORK_ATTRIBUTES","LIBRARY_TYPE=STATIC,SHARED","DEDUPLICATION=DEFAULT","OVERRIDE=DEFAULT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":117,"time":1753231094.3815441}
{"args":["CMAKE_LINK_LIBRARY_USING_NEEDED_FRAMEWORK","LINKER:-needed_framework,<LIBRARY>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":119,"time":1753231094.3815589}
{"args":["CMAKE_LINK_LIBRARY_USING_NEEDED_FRAMEWORK_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":120,"time":1753231094.381573}
{"args":["CMAKE_LINK_LIBRARY_NEEDED_FRAMEWORK_ATTRIBUTES","LIBRARY_TYPE=STATIC,SHARED","DEDUPLICATION=DEFAULT","OVERRIDE=DEFAULT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":121,"time":1753231094.381587}
{"args":["CMAKE_LINK_LIBRARY_USING_REEXPORT_FRAMEWORK","LINKER:-reexport_framework,<LIBRARY>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":123,"time":1753231094.3816011}
{"args":["CMAKE_LINK_LIBRARY_USING_REEXPORT_FRAMEWORK_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":124,"time":1753231094.3816149}
{"args":["CMAKE_LINK_LIBRARY_REEXPORT_FRAMEWORK_ATTRIBUTES","LIBRARY_TYPE=STATIC,SHARED","DEDUPLICATION=DEFAULT","OVERRIDE=DEFAULT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":125,"time":1753231094.381628}
{"args":["CMAKE_LINK_LIBRARY_USING_WEAK_FRAMEWORK","LINKER:-weak_framework,<LIBRARY>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":127,"time":1753231094.3816431}
{"args":["CMAKE_LINK_LIBRARY_USING_WEAK_FRAMEWORK_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":128,"time":1753231094.3816559}
{"args":["CMAKE_LINK_LIBRARY_WEAK_FRAMEWORK_ATTRIBUTES","LIBRARY_TYPE=STATIC,SHARED","DEDUPLICATION=DEFAULT","OVERRIDE=DEFAULT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":129,"time":1753231094.38167}
{"args":["CMAKE_LINK_LIBRARY_USING_NEEDED_LIBRARY","PATH{LINKER:-needed_library,<LIBRARY>}NAME{LINKER:-needed-l<LIBRARY>}"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":132,"time":1753231094.3816841}
{"args":["CMAKE_LINK_LIBRARY_USING_NEEDED_LIBRARY_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":133,"time":1753231094.3817101}
{"args":["CMAKE_LINK_LIBRARY_NEEDED_LIBRARY_ATTRIBUTES","LIBRARY_TYPE=SHARED","DEDUPLICATION=DEFAULT","OVERRIDE=DEFAULT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":134,"time":1753231094.3817241}
{"args":["CMAKE_LINK_LIBRARY_USING_REEXPORT_LIBRARY","PATH{LINKER:-reexport_library,<LIBRARY>}NAME{LINKER:-reexport-l<LIBRARY>}"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":136,"time":1753231094.3817389}
{"args":["CMAKE_LINK_LIBRARY_USING_REEXPORT_LIBRARY_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":137,"time":1753231094.381753}
{"args":["CMAKE_LINK_LIBRARY_REEXPORT_LIBRARY_ATTRIBUTES","LIBRARY_TYPE=STATIC,SHARED","DEDUPLICATION=DEFAULT","OVERRIDE=DEFAULT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":138,"time":1753231094.3817661}
{"args":["CMAKE_LINK_LIBRARY_USING_WEAK_LIBRARY","PATH{LINKER:-weak_library,<LIBRARY>}NAME{LINKER:-weak-l<LIBRARY>}"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":140,"time":1753231094.3817811}
{"args":["CMAKE_LINK_LIBRARY_USING_WEAK_LIBRARY_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":141,"time":1753231094.381794}
{"args":["CMAKE_LINK_LIBRARY_WEAK_LIBRARY_ATTRIBUTES","LIBRARY_TYPE=STATIC,SHARED","DEDUPLICATION=DEFAULT","OVERRIDE=DEFAULT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":142,"time":1753231094.381808}
{"args":["NOT","DEFINED","CMAKE_FIND_FRAMEWORK"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":145,"time":1753231094.3818221}
{"args":["CMAKE_FIND_FRAMEWORK","FIRST"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":146,"time":1753231094.3818369}
{"args":["CMAKE_PLATFORM_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":151,"line_end":154,"time":1753231094.3818531}
{"args":["_CMAKE_OSX_SYSROOT_PATH"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":155,"time":1753231094.381871}
{"args":["APPEND","CMAKE_PLATFORM_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES","/System/Library/Frameworks"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":157,"line_end":158,"time":1753231094.3818851}
{"args":["12.7","VERSION_LESS","10.5"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":160,"time":1753231094.381902}
{"args":["CMAKE_SYSTEM_FRAMEWORK_PATH","~/Library/Frameworks"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":167,"line_end":169,"time":1753231094.381917}
{"args":["_CMAKE_OSX_SYSROOT_PATH"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":170,"time":1753231094.3819311}
{"args":["APPEND","CMAKE_SYSTEM_FRAMEWORK_PATH","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":171,"line_end":175,"time":1753231094.381948}
{"args":["_path","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/../../Library/Frameworks","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Developer/Library/Frameworks","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/../../../../../Library/Frameworks"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":177,"line_end":184,"time":1753231094.3819799}
{"args":["_absolute_path","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/../../Library/Frameworks","ABSOLUTE"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":185,"time":1753231094.3820009}
{"args":["EXISTS","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":186,"time":1753231094.3820219}
{"args":["APPEND","CMAKE_SYSTEM_FRAMEWORK_PATH","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":187,"time":1753231094.3820529}
{"args":[],"cmd":"break","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":188,"time":1753231094.3820701}
{"args":["EXISTS","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":192,"time":1753231094.3820839}
{"args":["INSERT","CMAKE_PLATFORM_IMPLICIT_LINK_DIRECTORIES","0","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":193,"time":1753231094.3821111}
{"args":["EXISTS","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/lib"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":195,"time":1753231094.3821321}
{"args":["OSX_DEVELOPER_ROOT","AND","EXISTS","/Applications/Xcode.app/Contents/Developer/Library/Frameworks"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":199,"time":1753231094.382153}
{"args":["APPEND","CMAKE_SYSTEM_FRAMEWORK_PATH","/Applications/Xcode.app/Contents/Developer/Library/Frameworks"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":200,"line_end":201,"time":1753231094.3821809}
{"args":["APPEND","CMAKE_SYSTEM_FRAMEWORK_PATH","/Library/Frameworks","/Network/Library/Frameworks","/System/Library/Frameworks"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":203,"line_end":206,"time":1753231094.3822031}
{"args":["CMAKE_OSX_SYSROOT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":209,"time":1753231094.38222}
{"args":["NOT","DEFINED","CMAKE_FIND_APPBUNDLE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":226,"time":1753231094.3822341}
{"args":["CMAKE_FIND_APPBUNDLE","FIRST"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":227,"time":1753231094.3822479}
{"args":["_apps_paths"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":230,"time":1753231094.382272}
{"args":["_path","~/Applications","/Applications","/Applications/Xcode.app/Contents/Developer/../Applications","/Applications/Xcode.app/Contents/Developer/Applications"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":231,"line_end":236,"time":1753231094.382287}
{"args":["_apps","~/Applications","ABSOLUTE"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":237,"time":1753231094.3823061}
{"args":["EXISTS","/Users/<USER>/Applications"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":238,"time":1753231094.3823221}
{"args":["_apps","/Applications","ABSOLUTE"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":237,"time":1753231094.3823409}
{"args":["EXISTS","/Applications"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":238,"time":1753231094.3823559}
{"args":["APPEND","_apps_paths","/Applications"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":239,"time":1753231094.3823781}
{"args":["_apps","/Applications/Xcode.app/Contents/Developer/../Applications","ABSOLUTE"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":237,"time":1753231094.3823941}
{"args":["EXISTS","/Applications/Xcode.app/Contents/Applications"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":238,"time":1753231094.38241}
{"args":["APPEND","_apps_paths","/Applications/Xcode.app/Contents/Applications"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":239,"time":1753231094.3824329}
{"args":["_apps","/Applications/Xcode.app/Contents/Developer/Applications","ABSOLUTE"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":237,"time":1753231094.3824489}
{"args":["EXISTS","/Applications/Xcode.app/Contents/Developer/Applications"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":238,"time":1753231094.3824649}
{"args":["APPEND","_apps_paths","/Applications/Xcode.app/Contents/Developer/Applications"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":239,"time":1753231094.3824871}
{"args":["_apps_paths"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":242,"time":1753231094.382503}
{"args":["REMOVE_DUPLICATES","_apps_paths"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":243,"time":1753231094.382515}
{"args":["CMAKE_SYSTEM_APPBUNDLE_PATH","/Applications;/Applications/Xcode.app/Contents/Applications;/Applications/Xcode.app/Contents/Developer/Applications"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":245,"line_end":246,"time":1753231094.3825331}
{"args":["_apps_paths"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":247,"time":1753231094.382549}
{"args":["Platform/UnixPaths"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":249,"time":1753231094.38256}
{"args":["__UNIX_PATHS_INCLUDED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":10,"time":1753231094.382705}
{"args":["__UNIX_PATHS_INCLUDED","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":13,"time":1753231094.3827231}
{"args":["UNIX","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":18,"time":1753231094.3827479}
{"args":["_CMAKE_INSTALL_DIR","/usr/local/share/cmake","PATH"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":22,"time":1753231094.3827629}
{"args":["_CMAKE_INSTALL_DIR","/usr/local/share","PATH"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":23,"time":1753231094.3827779}
{"args":["APPEND","CMAKE_SYSTEM_PREFIX_PATH","/usr/local","/usr","/","/usr/local"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":31,"line_end":37,"time":1753231094.3827939}
{"args":["NOT","CMAKE_FIND_NO_INSTALL_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":38,"time":1753231094.3828111}
{"args":["APPEND","CMAKE_SYSTEM_PREFIX_PATH","/usr/local"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":39,"line_end":42,"time":1753231094.382828}
{"args":["CMAKE_STAGING_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":43,"time":1753231094.382843}
{"args":[],"cmd":"_cmake_record_install_prefix","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":50,"time":1753231094.3828571}
{"args":["_CMAKE_SYSTEM_PREFIX_PATH_INSTALL_PREFIX_VALUE","/usr/local","PARENT_SCOPE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":10,"time":1753231094.382875}
{"args":["_CMAKE_SYSTEM_PREFIX_PATH_STAGING_PREFIX_VALUE","","PARENT_SCOPE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":11,"time":1753231094.382895}
{"args":["icount","0"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":12,"time":1753231094.38291}
{"args":["scount","0"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":13,"time":1753231094.3829219}
{"args":["value","IN","LISTS","CMAKE_SYSTEM_PREFIX_PATH"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":14,"time":1753231094.382935}
{"args":["value","STREQUAL","CMAKE_INSTALL_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":15,"time":1753231094.3829551}
{"args":["EXPR","icount","0+1"],"cmd":"math","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":16,"time":1753231094.382971}
{"args":["value","STREQUAL","CMAKE_STAGING_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1753231094.382997}
{"args":["value","STREQUAL","CMAKE_INSTALL_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":15,"time":1753231094.383013}
{"args":["value","STREQUAL","CMAKE_STAGING_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1753231094.3830271}
{"args":["value","STREQUAL","CMAKE_INSTALL_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":15,"time":1753231094.3830421}
{"args":["value","STREQUAL","CMAKE_STAGING_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1753231094.3830559}
{"args":["value","STREQUAL","CMAKE_INSTALL_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":15,"time":1753231094.383081}
{"args":["EXPR","icount","1+1"],"cmd":"math","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":16,"time":1753231094.3830969}
{"args":["value","STREQUAL","CMAKE_STAGING_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1753231094.3831141}
{"args":["value","STREQUAL","CMAKE_INSTALL_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":15,"time":1753231094.3831289}
{"args":["EXPR","icount","2+1"],"cmd":"math","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":16,"time":1753231094.3831429}
{"args":["value","STREQUAL","CMAKE_STAGING_PREFIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1753231094.3831589}
{"args":["_CMAKE_SYSTEM_PREFIX_PATH_INSTALL_PREFIX_COUNT","3","PARENT_SCOPE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":22,"time":1753231094.3831749}
{"args":["_CMAKE_SYSTEM_PREFIX_PATH_STAGING_PREFIX_COUNT","0","PARENT_SCOPE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":23,"time":1753231094.3831911}
{"args":["APPEND","CMAKE_SYSTEM_PREFIX_PATH","/usr/X11R6","/usr/pkg","/opt"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":53,"line_end":57,"time":1753231094.383209}
{"args":["APPEND","CMAKE_SYSTEM_INCLUDE_PATH","/usr/include/X11"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":60,"line_end":63,"time":1753231094.3832259}
{"args":["APPEND","CMAKE_SYSTEM_LIBRARY_PATH","/usr/lib/X11"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":65,"line_end":68,"time":1753231094.3832409}
{"args":["APPEND","CMAKE_PLATFORM_IMPLICIT_LINK_DIRECTORIES","/lib","/lib32","/lib64","/usr/lib","/usr/lib32","/usr/lib64"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":70,"line_end":72,"time":1753231094.383256}
{"args":["CMAKE_SYSROOT_COMPILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":74,"time":1753231094.3832729}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":76,"time":1753231094.3832879}
{"args":["_cmake_sysroot_compile",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":77,"time":1753231094.383301}
{"args":["_CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES_INIT","/usr/local/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include","/usr/include"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":82,"line_end":85,"time":1753231094.383317}
{"args":["_CMAKE_CXX_IMPLICIT_INCLUDE_DIRECTORIES_INIT","","/usr/include"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":86,"line_end":89,"time":1753231094.383342}
{"args":["_CMAKE_CUDA_IMPLICIT_INCLUDE_DIRECTORIES_INIT","","/usr/include"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":90,"line_end":93,"time":1753231094.383358}
{"args":["_cmake_sysroot_compile"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":95,"time":1753231094.383383}
{"args":["CMAKE_COMPILER_SYSROOT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":100,"time":1753231094.383404}
{"args":["GLOBAL","PROPERTY","FIND_LIBRARY_USE_LIB32_PATHS","TRUE"],"cmd":"set_property","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":109,"time":1753231094.3834181}
{"args":["GLOBAL","PROPERTY","FIND_LIBRARY_USE_LIB64_PATHS","TRUE"],"cmd":"set_property","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":110,"time":1753231094.3834341}
{"args":["GLOBAL","PROPERTY","FIND_LIBRARY_USE_LIBX32_PATHS","TRUE"],"cmd":"set_property","file":"/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake","frame":4,"global_frame":4,"line":111,"time":1753231094.3834469}
{"args":["CMAKE_SYSTEM_NAME","STREQUAL","Darwin"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":251,"time":1753231094.3834691}
{"args":["COMMAND","brew","--prefix","OUTPUT_VARIABLE","_cmake_homebrew_prefix","RESULT_VARIABLE","_brew_result","OUTPUT_STRIP_TRAILING_WHITESPACE"],"cmd":"execute_process","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":252,"line_end":257,"time":1753231094.3834851}
{"args":["_brew_result","EQUAL","0","AND","IS_DIRECTORY","/usr/local"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":258,"time":1753231094.4222169}
{"args":["PREPEND","CMAKE_SYSTEM_PREFIX_PATH","/usr/local"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":259,"time":1753231094.4222701}
{"args":["_cmake_homebrew_prefix"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":269,"time":1753231094.4222939}
{"args":["_brew_result"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":270,"time":1753231094.422307}
{"args":["_CMAKE_OSX_SYSROOT_PATH"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":273,"time":1753231094.4223211}
{"args":["EXISTS","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":274,"time":1753231094.422339}
{"args":["INSERT","CMAKE_SYSTEM_PREFIX_PATH","0","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":275,"time":1753231094.4223759}
{"args":["lang","C","CXX","OBJC","OBJCXX","Swift"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":276,"time":1753231094.422399}
{"args":["APPEND","_CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES_INIT","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":277,"time":1753231094.4224181}
{"args":["APPEND","_CMAKE_CXX_IMPLICIT_INCLUDE_DIRECTORIES_INIT","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":277,"time":1753231094.4224391}
{"args":["APPEND","_CMAKE_OBJC_IMPLICIT_INCLUDE_DIRECTORIES_INIT","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":277,"time":1753231094.422456}
{"args":["APPEND","_CMAKE_OBJCXX_IMPLICIT_INCLUDE_DIRECTORIES_INIT","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":277,"time":1753231094.4224939}
{"args":["APPEND","_CMAKE_Swift_IMPLICIT_INCLUDE_DIRECTORIES_INIT","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":277,"time":1753231094.4225121}
{"args":["EXISTS","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":280,"time":1753231094.4225299}
{"args":["APPEND","CMAKE_SYSTEM_PREFIX_PATH","/sw","/opt/local"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Platform/Darwin.cmake","frame":3,"global_frame":3,"line":287,"line_end":290,"time":1753231094.422554}
{"args":["NOT","_INCLUDED_SYSTEM_INFO_FILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":34,"time":1753231094.422616}
{"args":["CMAKE_EXTRA_GENERATOR"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":48,"time":1753231094.4226329}
{"args":["NOT","CMAKE_MODULE_EXISTS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":58,"time":1753231094.422648}
{"args":["CMAKE_SYSTEM_SPECIFIC_INFORMATION_LOADED","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":64,"time":1753231094.422663}
{"args":["CMakeLanguageInformation"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":11,"time":1753231094.42292}
{"args":["__cmake_include_compiler_wrapper","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake","frame":3,"global_frame":3,"line":9,"time":1753231094.423038}
{"args":["UNIX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":15,"time":1753231094.423069}
{"args":["CMAKE_C_OUTPUT_EXTENSION",".o"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":16,"time":1753231094.4230859}
{"args":["_INCLUDED_FILE","0"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":21,"time":1753231094.4231}
{"args":["CMAKE_C_COMPILER_ID"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":24,"time":1753231094.4231131}
{"args":["Compiler/AppleClang-C","OPTIONAL"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":25,"time":1753231094.4231269}
{"args":["Compiler/Clang"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":1,"time":1753231094.4232409}
{"args":["__COMPILER_CLANG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":6,"time":1753231094.423558}
{"args":["__COMPILER_CLANG","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":9,"time":1753231094.4235771}
{"args":["Compiler/CMakeCommonCompilerMacros"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":11,"time":1753231094.4235909}
{"args":["__COMPILER_CMAKE_COMMON_COMPILER_MACROS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":5,"time":1753231094.4238739}
{"args":["__COMPILER_CMAKE_COMMON_COMPILER_MACROS","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":8,"time":1753231094.4239089}
{"args":["__compiler_check_default_language_standard","lang","stdver1","std1"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":32,"time":1753231094.4239261}
{"args":["cmake_record_c_compile_features"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":71,"time":1753231094.42395}
{"args":["cmake_record_cxx_compile_features"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":106,"time":1753231094.42397}
{"args":["cmake_record_cuda_compile_features"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":146,"time":1753231094.423991}
{"args":["cmake_record_hip_compile_features"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":186,"time":1753231094.424011}
{"args":["cmake_create_cxx_import_std","std","variable"],"cmd":"function","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":205,"time":1753231094.4240301}
{"args":["__pch_header_C","c-header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":13,"time":1753231094.4240689}
{"args":["__pch_header_CXX","c++-header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":14,"time":1753231094.4240921}
{"args":["__pch_header_OBJC","objective-c-header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":15,"time":1753231094.424104}
{"args":["__pch_header_OBJCXX","objective-c++-header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":16,"time":1753231094.4241159}
{"args":["x","STREQUAL","xMSVC","OR","x","STREQUAL","xMSVC","OR","x","STREQUAL","xMSVC","OR","x","STREQUAL","xMSVC"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":18,"line_end":21,"time":1753231094.4241359}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":24,"time":1753231094.424165}
{"args":["Compiler/GNU"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":25,"time":1753231094.4241769}
{"args":["__COMPILER_GNU"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":6,"time":1753231094.4244709}
{"args":["__COMPILER_GNU","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":9,"time":1753231094.4244919}
{"args":["Compiler/CMakeCommonCompilerMacros"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":11,"time":1753231094.4245081}
{"args":["__COMPILER_CMAKE_COMMON_COMPILER_MACROS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":6,"global_frame":6,"line":5,"time":1753231094.424798}
{"args":[],"cmd":"return","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":6,"global_frame":6,"line":6,"time":1753231094.424819}
{"args":["__pch_header_C","c-header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":13,"time":1753231094.424859}
{"args":["__pch_header_CXX","c++-header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":14,"time":1753231094.4248731}
{"args":["__pch_header_OBJC","objective-c-header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":15,"time":1753231094.4248979}
{"args":["__pch_header_OBJCXX","objective-c++-header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":16,"time":1753231094.424912}
{"args":["__compiler_gnu","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":18,"time":1753231094.4249239}
{"args":["__compiler_gnu_c_standards","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":159,"time":1753231094.424947}
{"args":["__compiler_gnu_cxx_standards","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":202,"time":1753231094.424968}
{"args":["__compiler_clang","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":27,"time":1753231094.424993}
{"args":["__compiler_clang_cxx_standards","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":133,"time":1753231094.4250171}
{"args":["C"],"cmd":"__compiler_clang","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":2,"time":1753231094.4250481}
{"args":["C"],"cmd":"__compiler_gnu","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":28,"time":1753231094.4250641}
{"args":["CMAKE_C_VERBOSE_FLAG","-v"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":20,"time":1753231094.425077}
{"args":["CMAKE_C_COMPILE_OPTIONS_WARNING_AS_ERROR","-Werror"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":21,"time":1753231094.425091}
{"args":["CMAKE_C_COMPILE_OPTIONS_PIC","-fPIC"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":22,"time":1753231094.425106}
{"args":["_CMAKE_C_PIE_MAY_BE_SUPPORTED_BY_LINKER","NO"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":23,"time":1753231094.4251201}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","3.4"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":24,"time":1753231094.4251339}
{"args":["CMAKE_C_COMPILE_OPTIONS_PIE","-fPIE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":25,"time":1753231094.4251561}
{"args":["_CMAKE_C_PIE_MAY_BE_SUPPORTED_BY_LINKER","YES"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":28,"time":1753231094.425169}
{"args":["CMAKE_C_LINK_OPTIONS_PIE","-fPIE","-pie"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":29,"time":1753231094.4251831}
{"args":["CMAKE_C_LINK_OPTIONS_NO_PIE","-no-pie"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":30,"time":1753231094.4251969}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.0"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":32,"time":1753231094.4252119}
{"args":["CMAKE_C_COMPILE_OPTIONS_VISIBILITY","-fvisibility="],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":33,"time":1753231094.4252291}
{"args":["CMAKE_SHARED_LIBRARY_C_FLAGS","-fPIC"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":35,"time":1753231094.4252441}
{"args":["CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS","-shared"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":36,"time":1753231094.4252579}
{"args":["CMAKE_C_COMPILE_OPTIONS_SYSROOT","--sysroot="],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":37,"time":1753231094.425272}
{"args":["CMAKE_C_LINKER_WRAPPER_FLAG","-Wl,"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":39,"time":1753231094.425297}
{"args":["CMAKE_C_LINKER_WRAPPER_FLAG_SEP",","],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":40,"time":1753231094.4253111}
{"args":["CMAKE_C_LINK_MODE","DRIVER"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":42,"time":1753231094.425324}
{"args":["_IN_TC","GLOBAL","PROPERTY","IN_TRY_COMPILE"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":48,"time":1753231094.425338}
{"args":["CMAKE_C_COMPILER_ID","STREQUAL","GNU","AND","_IN_TC","AND","NOT","CMAKE_FORCE_DEPFILES"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":49,"time":1753231094.425359}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":50,"time":1753231094.42538}
{"args":["CMAKE_DEPFILE_FLAGS_C","-MD -MT <DEP_TARGET> -MF <DEP_FILE>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":54,"time":1753231094.425391}
{"args":["APPEND","CMAKE_C_FLAGS_INIT"," "],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":58,"time":1753231094.4254069}
{"args":["APPEND","CMAKE_C_FLAGS_DEBUG_INIT"," -g"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":59,"time":1753231094.425422}
{"args":["APPEND","CMAKE_C_FLAGS_MINSIZEREL_INIT"," -Os"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":60,"time":1753231094.425437}
{"args":["APPEND","CMAKE_C_FLAGS_RELEASE_INIT"," -O3"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":61,"time":1753231094.425452}
{"args":["APPEND","CMAKE_C_FLAGS_RELWITHDEBINFO_INIT"," -O2 -g"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":62,"time":1753231094.4254661}
{"args":["NOT","xC","STREQUAL","xFortran"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":63,"time":1753231094.4254811}
{"args":["APPEND","CMAKE_C_FLAGS_MINSIZEREL_INIT"," -DNDEBUG"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":64,"time":1753231094.425498}
{"args":["APPEND","CMAKE_C_FLAGS_RELEASE_INIT"," -DNDEBUG"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":65,"time":1753231094.4255121}
{"args":["APPEND","CMAKE_C_FLAGS_RELWITHDEBINFO_INIT"," -DNDEBUG"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":66,"time":1753231094.425525}
{"args":["CMAKE_C_CREATE_PREPROCESSED_SOURCE","<CMAKE_C_COMPILER> <DEFINES> <INCLUDES> <FLAGS> -E <SOURCE> > <PREPROCESSED_SOURCE>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":68,"time":1753231094.4255409}
{"args":["CMAKE_C_CREATE_ASSEMBLY_SOURCE","<CMAKE_C_COMPILER> <DEFINES> <INCLUDES> <FLAGS> -S <SOURCE> -o <ASSEMBLY_SOURCE>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":69,"time":1753231094.4255569}
{"args":["NOT","APPLE","OR","NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":70,"time":1753231094.4255741}
{"args":["CMAKE_INCLUDE_SYSTEM_FLAG_C","-isystem "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":71,"time":1753231094.425595}
{"args":["_CMAKE_C_IPO_SUPPORTED_BY_CMAKE","YES"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":74,"time":1753231094.4256189}
{"args":["_CMAKE_C_IPO_MAY_BE_SUPPORTED_BY_COMPILER","NO"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":75,"time":1753231094.4256339}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.5"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":80,"time":1753231094.4256489}
{"args":["_CMAKE_C_IPO_MAY_BE_SUPPORTED_BY_COMPILER","YES"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":81,"time":1753231094.425678}
{"args":["__lto_flags",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":83,"time":1753231094.4256909}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","11.0"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":90,"time":1753231094.425704}
{"args":["APPEND","__lto_flags","-flto=auto"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":91,"time":1753231094.4257209}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.7","AND","NOT","APPLE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":102,"time":1753231094.4257369}
{"args":["CMAKE_C_COMPILE_OPTIONS_IPO","-flto=auto"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":109,"time":1753231094.425756}
{"args":["CMAKE_C_ARCHIVE_CREATE_IPO","\"\" qc <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":118,"line_end":120,"time":1753231094.425771}
{"args":["CMAKE_C_ARCHIVE_APPEND_IPO","\"\" q <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":122,"line_end":124,"time":1753231094.4257879}
{"args":["CMAKE_C_ARCHIVE_FINISH_IPO","\"\" <TARGET>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":126,"line_end":128,"time":1753231094.4258029}
{"args":["C","STREQUAL","CXX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":131,"time":1753231094.4258211}
{"args":["NOT","xC","STREQUAL","xFortran"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":141,"time":1753231094.425844}
{"args":["CMAKE_PCH_EXTENSION",".gch"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":142,"time":1753231094.425863}
{"args":["NOT","CMAKE_GENERATOR","MATCHES","Xcode"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":143,"time":1753231094.4258759}
{"args":["CMAKE_PCH_PROLOGUE","#pragma GCC system_header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":144,"time":1753231094.4258959}
{"args":["CMAKE_C_COMPILE_OPTIONS_INVALID_PCH","-Winvalid-pch"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":146,"time":1753231094.42591}
{"args":["CMAKE_C_COMPILE_OPTIONS_USE_PCH","-include","<PCH_HEADER>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":147,"time":1753231094.4259231}
{"args":["CMAKE_C_COMPILE_OPTIONS_CREATE_PCH","-x","c-header","-include","<PCH_HEADER>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":148,"time":1753231094.4259379}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","4.9"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":153,"time":1753231094.425956}
{"args":["CMAKE_C_COMPILE_OPTIONS_COLOR_DIAGNOSTICS","-fdiagnostics-color=always"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":154,"time":1753231094.4259911}
{"args":["CMAKE_C_COMPILE_OPTIONS_COLOR_DIAGNOSTICS_OFF","-fno-diagnostics-color"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/GNU.cmake","frame":5,"global_frame":5,"line":155,"time":1753231094.4260061}
{"args":["CMAKE_C_COMPILE_OPTIONS_PIE","-fPIE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":29,"time":1753231094.426023}
{"args":["APPLE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.4260359}
{"args":["CMAKE_C_LINK_OPTIONS_PIE","-fPIE","-Xlinker","-pie"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":33,"time":1753231094.4260521}
{"args":["CMAKE_C_LINK_OPTIONS_NO_PIE","-Xlinker","-no_pie"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":34,"time":1753231094.4260671}
{"args":["CMAKE_INCLUDE_SYSTEM_FLAG_C","-isystem "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":36,"time":1753231094.4260819}
{"args":["CMAKE_C_COMPILE_OPTIONS_VISIBILITY","-fvisibility="],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":37,"time":1753231094.426095}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_LESS","3.4.0"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":38,"time":1753231094.4261091}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":41,"time":1753231094.426127}
{"args":["CMAKE_C_COMPILE_OPTIONS_TARGET","--target="],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":42,"time":1753231094.4261379}
{"args":["CMAKE_C_COMPILE_OPTIONS_EXTERNAL_TOOLCHAIN","--gcc-toolchain="],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":43,"time":1753231094.4261501}
{"args":["CMAKE_C_LINKER_WRAPPER_FLAG","-Xlinker"," "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":45,"time":1753231094.4261651}
{"args":["CMAKE_C_LINKER_WRAPPER_FLAG_SEP"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":46,"time":1753231094.426178}
{"args":["CMAKE_C_COMPILER_TARGET","AND","C","STREQUAL","CXX"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":48,"time":1753231094.426193}
{"args":["_CMAKE_C_IPO_SUPPORTED_BY_CMAKE","YES"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":56,"time":1753231094.4262149}
{"args":["_CMAKE_C_IPO_MAY_BE_SUPPORTED_BY_COMPILER","YES"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":57,"time":1753231094.426228}
{"args":["COMPARE","EQUAL","AppleClang","AppleClang","__is_apple_clang"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":59,"time":1753231094.426244}
{"args":["COMPARE","EQUAL","AppleClang","FujitsuClang","__is_fujitsu_clang"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":60,"time":1753231094.4262609}
{"args":["_CMAKE_LTO_THIN","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":65,"time":1753231094.426275}
{"args":["__is_apple_clang"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":66,"time":1753231094.4262879}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_LESS","8.0"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":67,"time":1753231094.4263151}
{"args":["_CMAKE_LTO_THIN"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":78,"time":1753231094.4263339}
{"args":["CMAKE_C_COMPILE_OPTIONS_IPO","-flto=thin"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":79,"time":1753231094.426348}
{"args":["ANDROID","AND","NOT","CMAKE_ANDROID_NDK_VERSION","VERSION_GREATER_EQUAL","22"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":84,"time":1753231094.4263639}
{"args":["ANDROID","OR","__is_apple_clang"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":89,"time":1753231094.426384}
{"args":["__ar","/usr/bin/ar"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":90,"time":1753231094.4264021}
{"args":["__ranlib","/usr/bin/ranlib"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":91,"time":1753231094.426415}
{"args":["CMAKE_C_ARCHIVE_CREATE_IPO","\"/usr/bin/ar\" qc <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":97,"line_end":99,"time":1753231094.42643}
{"args":["CMAKE_C_ARCHIVE_APPEND_IPO","\"/usr/bin/ar\" q <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":101,"line_end":103,"time":1753231094.4264481}
{"args":["CMAKE_C_ARCHIVE_FINISH_IPO","\"/usr/bin/ranlib\" <TARGET>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":105,"line_end":107,"time":1753231094.4264641}
{"args":["CMAKE_PCH_EXTENSION",".pch"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":109,"time":1753231094.4264791}
{"args":["NOT","CMAKE_GENERATOR","MATCHES","Xcode"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":110,"time":1753231094.4264929}
{"args":["CMAKE_PCH_PROLOGUE","#pragma clang system_header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":111,"time":1753231094.4265089}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","11.0.0","AND","NOT","__is_apple_clang"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":113,"time":1753231094.4265261}
{"args":["CMAKE_C_COMPILE_OPTIONS_USE_PCH","-Xclang","-include-pch","-Xclang","<PCH_FILE>","-Xclang","-include","-Xclang","<PCH_HEADER>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":116,"time":1753231094.426549}
{"args":["CMAKE_C_COMPILE_OPTIONS_CREATE_PCH","-Xclang","-emit-pch","-Xclang","-include","-Xclang","<PCH_HEADER>","-x","c-header"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":117,"time":1753231094.4265699}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","2.6"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":120,"time":1753231094.4265881}
{"args":["CMAKE_HOST_WIN32","AND","CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","3.7"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":122,"time":1753231094.426609}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":125,"time":1753231094.426625}
{"args":["CMAKE_C_COMPILE_OPTIONS_COLOR_DIAGNOSTICS","-fcolor-diagnostics"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":126,"time":1753231094.426636}
{"args":["CMAKE_C_COMPILE_OPTIONS_COLOR_DIAGNOSTICS_OFF","-fno-color-diagnostics"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/Clang.cmake","frame":4,"global_frame":4,"line":127,"time":1753231094.4266591}
{"args":["NOT","x","STREQUAL","xMSVC"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":5,"time":1753231094.4266789}
{"args":["(","NOT","DEFINED","CMAKE_DEPENDS_USE_COMPILER","OR","CMAKE_DEPENDS_USE_COMPILER",")","AND","CMAKE_GENERATOR","MATCHES","Makefiles","AND","CMAKE_DEPFILE_FLAGS_C"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":6,"line_end":8,"time":1753231094.4266989}
{"args":["CMAKE_C_DEPFILE_FORMAT","gcc"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":10,"time":1753231094.426728}
{"args":["CMAKE_C_DEPENDS_USE_COMPILER","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":11,"time":1753231094.4267409}
{"args":["CMAKE_C_COMPILE_OPTIONS_EXPLICIT_LANGUAGE","-x","c"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":15,"time":1753231094.4267559}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.0"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":17,"time":1753231094.42677}
{"args":["CMAKE_C90_STANDARD_COMPILE_OPTION","-std=c90"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":18,"time":1753231094.4267859}
{"args":["CMAKE_C90_EXTENSION_COMPILE_OPTION","-std=gnu90"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":19,"time":1753231094.4267991}
{"args":["CMAKE_C90_STANDARD__HAS_FULL_SUPPORT","ON"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":20,"time":1753231094.4268119}
{"args":["CMAKE_C99_STANDARD_COMPILE_OPTION","-std=c99"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":22,"time":1753231094.4268279}
{"args":["CMAKE_C99_EXTENSION_COMPILE_OPTION","-std=gnu99"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":23,"time":1753231094.426841}
{"args":["CMAKE_C99_STANDARD__HAS_FULL_SUPPORT","ON"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":24,"time":1753231094.4268539}
{"args":["CMAKE_C11_STANDARD_COMPILE_OPTION","-std=c11"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":26,"time":1753231094.426867}
{"args":["CMAKE_C11_EXTENSION_COMPILE_OPTION","-std=gnu11"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":27,"time":1753231094.4268789}
{"args":["CMAKE_C11_STANDARD__HAS_FULL_SUPPORT","ON"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":28,"time":1753231094.426892}
{"args":["CMAKE_C_STANDARD_LATEST","11"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":30,"time":1753231094.4269049}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","11.0"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":33,"time":1753231094.426918}
{"args":["CMAKE_C17_STANDARD_COMPILE_OPTION","-std=c17"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":34,"time":1753231094.426934}
{"args":["CMAKE_C17_EXTENSION_COMPILE_OPTION","-std=gnu17"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":35,"time":1753231094.4269459}
{"args":["CMAKE_C_STANDARD_LATEST","17"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":36,"time":1753231094.4269691}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","11.0.3"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":39,"time":1753231094.4269831}
{"args":["CMAKE_C23_STANDARD_COMPILE_OPTION","-std=c2x"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":40,"time":1753231094.4269991}
{"args":["CMAKE_C23_EXTENSION_COMPILE_OPTION","-std=gnu2x"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":41,"time":1753231094.427012}
{"args":["CMAKE_C_STANDARD_LATEST","23"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":42,"time":1753231094.4270239}
{"args":["C","4.0","99","9.1","11","12.0.5","17"],"cmd":"__compiler_check_default_language_standard","file":"/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake","frame":3,"global_frame":3,"line":45,"time":1753231094.4270389}
{"args":["__std_ver_pairs","4.0;99;9.1;11;12.0.5;17"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":33,"time":1753231094.427058}
{"args":["REGEX","REPLACE"," *; *"," ","__std_ver_pairs","4.0;99;9.1;11;12.0.5;17"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":34,"time":1753231094.4270749}
{"args":["REGEX","MATCHALL","[^ ]+ [^ ]+","__std_ver_pairs","4.0 99 9.1 11 12.0.5 17"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.427104}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","4.0"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":39,"time":1753231094.4271269}
{"args":["NOT","CMAKE_C_COMPILER_FORCED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":40,"time":1753231094.42716}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":46,"time":1753231094.427176}
{"args":["REVERSE","__std_ver_pairs"],"cmd":"list","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":47,"time":1753231094.4271879}
{"args":["__std_ver_pair","IN","LISTS","__std_ver_pairs"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":48,"time":1753231094.427206}
{"args":["REGEX","MATCH","([^ ]+) (.+)","__std_ver_pair","12.0.5 17"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":49,"time":1753231094.4272239}
{"args":["__stdver","12.0.5"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":50,"time":1753231094.427242}
{"args":["__std","17"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":51,"time":1753231094.4272549}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","__stdver"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":53,"time":1753231094.427268}
{"args":["NOT","DEFINED","CMAKE_C_EXTENSIONS_DEFAULT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":54,"time":1753231094.427284}
{"args":["CMAKE_C_EXTENSIONS_DEFAULT","ON"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":56,"time":1753231094.4273}
{"args":["NOT","DEFINED","CMAKE_C_STANDARD_DEFAULT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":58,"time":1753231094.4273379}
{"args":["CMAKE_C_STANDARD_DEFAULT","17"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":59,"time":1753231094.4273551}
{"args":["__std"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":62,"time":1753231094.4273691}
{"args":["__stdver"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":63,"time":1753231094.427381}
{"args":["REGEX","MATCH","([^ ]+) (.+)","__std_ver_pair","9.1 11"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":49,"time":1753231094.4273939}
{"args":["__stdver","9.1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":50,"time":1753231094.427412}
{"args":["__std","11"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":51,"time":1753231094.4274249}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","__stdver"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":53,"time":1753231094.4274371}
{"args":["NOT","DEFINED","CMAKE_C_EXTENSIONS_DEFAULT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":54,"time":1753231094.427453}
{"args":["NOT","DEFINED","CMAKE_C_STANDARD_DEFAULT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":58,"time":1753231094.4274681}
{"args":["__std"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":62,"time":1753231094.4274831}
{"args":["__stdver"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":63,"time":1753231094.427494}
{"args":["REGEX","MATCH","([^ ]+) (.+)","__std_ver_pair","4.0 99"],"cmd":"string","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":49,"time":1753231094.4275069}
{"args":["__stdver","4.0"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":50,"time":1753231094.4275241}
{"args":["__std","99"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":51,"time":1753231094.427536}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","__stdver"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":53,"time":1753231094.4275489}
{"args":["NOT","DEFINED","CMAKE_C_EXTENSIONS_DEFAULT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":54,"time":1753231094.4275639}
{"args":["NOT","DEFINED","CMAKE_C_STANDARD_DEFAULT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":58,"time":1753231094.4275789}
{"args":["__std"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":62,"time":1753231094.427593}
{"args":["__stdver"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":63,"time":1753231094.4276049}
{"args":["__std_ver_pairs"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":67,"time":1753231094.4276299}
{"args":["CMAKE_BASE_NAME"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":28,"time":1753231094.4276519}
{"args":["CMAKE_BASE_NAME","/usr/bin/cc","NAME_WE"],"cmd":"get_filename_component","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":29,"time":1753231094.427665}
{"args":["CMAKE_COMPILER_IS_GNUCC"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":30,"time":1753231094.4276791}
{"args":["CMAKE_SYSTEM_PROCESSOR"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":36,"time":1753231094.4276929}
{"args":["CMAKE_C_COMPILER_ID"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":37,"time":1753231094.427706}
{"args":["Platform/Apple-AppleClang-C-x86_64","OPTIONAL","RESULT_VARIABLE","_INCLUDED_FILE"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":38,"time":1753231094.4277201}
{"args":["NOT","_INCLUDED_FILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":40,"time":1753231094.4277749}
{"args":["Platform/Apple-cc-x86_64","OPTIONAL"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":41,"time":1753231094.4277921}
{"args":["CMAKE_C_COMPILER_ID"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":47,"time":1753231094.4278209}
{"args":["Platform/Apple-AppleClang-C","OPTIONAL","RESULT_VARIABLE","_INCLUDED_FILE"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":48,"line_end":49,"time":1753231094.4278359}
{"args":["Platform/Apple-Clang-C"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake","frame":3,"global_frame":3,"line":1,"time":1753231094.427927}
{"args":["Platform/Apple-Clang"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake","frame":4,"global_frame":4,"line":1,"time":1753231094.4280059}
{"args":[],"cmd":"include_guard","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":6,"time":1753231094.4281321}
{"args":["__apple_compiler_clang","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":8,"time":1753231094.4281509}
{"args":["C"],"cmd":"__apple_compiler_clang","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake","frame":4,"global_frame":4,"line":2,"time":1753231094.4281771}
{"args":["CMAKE_C_VERBOSE_FLAG","-v -Wl,-v"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":9,"time":1753231094.4281919}
{"args":["CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS","-dynamiclib -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":10,"time":1753231094.4282069}
{"args":["CMAKE_SHARED_MODULE_CREATE_C_FLAGS","-bundle -Wl,-headerpad_max_install_names"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":11,"time":1753231094.4282229}
{"args":["CMAKE_C_SYSROOT_FLAG","-isysroot"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":12,"time":1753231094.428237}
{"args":["CMAKE_C_OSX_DEPLOYMENT_TARGET_FLAG","-mmacosx-version-min="],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":13,"time":1753231094.428251}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","3.2"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":14,"time":1753231094.428266}
{"args":["CMAKE_C_SYSTEM_FRAMEWORK_SEARCH_FLAG","-iframework "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":15,"time":1753231094.428299}
{"args":["CMAKE_C_LINK_LIBRARY_USING_FRAMEWORK","-framework <LIBRARY>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":18,"time":1753231094.4283149}
{"args":["CMAKE_C_LINK_LIBRARY_USING_FRAMEWORK_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":19,"time":1753231094.4283299}
{"args":["CMAKE_C_LINK_LIBRARY_FRAMEWORK_ATTRIBUTES","LIBRARY_TYPE=STATIC,SHARED","DEDUPLICATION=DEFAULT","OVERRIDE=DEFAULT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":20,"time":1753231094.428345}
{"args":["CMAKE_C_USING_LINKER_SYSTEM","-fuse-ld=ld"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":23,"time":1753231094.4283619}
{"args":["CMAKE_C_USING_LINKER_APPLE_CLASSIC","-fuse-ld=ld","LINKER:-ld_classic"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":24,"time":1753231094.428376}
{"args":["CMAKE_C_USING_LINKER_LLD","-fuse-ld=lld"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":25,"time":1753231094.428391}
{"args":["CMAKE_C_USING_LINKER_MOLD","-fuse-ld=mold"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":26,"time":1753231094.4284041}
{"args":["CMAKE_C_USING_LINKER_SOLD","-fuse-ld=sold"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":27,"time":1753231094.4284179}
{"args":["NOT","CMAKE_C_COMPILER_APPLE_SYSROOT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":29,"time":1753231094.428432}
{"args":["_CMAKE_OSX_SYSROOT_PATH","MATCHES","/iPhoneOS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":33,"time":1753231094.428452}
{"args":["_CMAKE_OSX_SYSROOT_PATH","MATCHES","/iPhoneSimulator"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":35,"time":1753231094.4284849}
{"args":["_CMAKE_OSX_SYSROOT_PATH","MATCHES","/AppleTVOS"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":37,"time":1753231094.4284999}
{"args":["_CMAKE_OSX_SYSROOT_PATH","MATCHES","/AppleTVSimulator"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":39,"time":1753231094.428515}
{"args":["_CMAKE_OSX_SYSROOT_PATH","MATCHES","/XROS"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":41,"time":1753231094.428529}
{"args":["_CMAKE_OSX_SYSROOT_PATH","MATCHES","/XRSimulator"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":43,"time":1753231094.428544}
{"args":["_CMAKE_OSX_SYSROOT_PATH","MATCHES","/WatchOS"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":45,"time":1753231094.4285581}
{"args":["_CMAKE_OSX_SYSROOT_PATH","MATCHES","/WatchSimulator"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":47,"time":1753231094.4285719}
{"args":["_CMAKE_OSX_SYSROOT_PATH","MATCHES","/MacOSX","AND","CMAKE_SYSTEM_NAME","STREQUAL","iOS"],"cmd":"elseif","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":49,"time":1753231094.4285879}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":51,"time":1753231094.428616}
{"args":["CMAKE_C_OSX_DEPLOYMENT_TARGET_FLAG","-mmacosx-version-min="],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake","frame":5,"global_frame":5,"line":52,"time":1753231094.4286289}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.2"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake","frame":3,"global_frame":3,"line":2,"time":1753231094.4286511}
{"args":["CMAKE_C_SYSTEM_FRAMEWORK_SEARCH_FLAG","-iframework "],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake","frame":3,"global_frame":3,"line":3,"time":1753231094.428669}
{"args":["NOT","_INCLUDED_FILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":51,"time":1753231094.4286871}
{"args":["CMAKE_C_COMPILER_WRAPPER"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":57,"time":1753231094.4287009}
{"args":["NOT","_INCLUDED_FILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":65,"time":1753231094.428714}
{"args":["CMAKE_C_SIZEOF_DATA_PTR"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":69,"time":1753231094.4287269}
{"args":["f","IN","LISTS","CMAKE_C_ABI_FILES"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":70,"time":1753231094.428741}
{"args":["CMAKE_C_ABI_FILES"],"cmd":"unset","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":73,"time":1753231094.428755}
{"args":["CMAKE_USER_MAKE_RULES_OVERRIDE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":82,"time":1753231094.428767}
{"args":["CMAKE_USER_MAKE_RULES_OVERRIDE_C"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":88,"time":1753231094.428781}
{"args":["CMAKE_C_FLAGS_INIT","  "],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":94,"time":1753231094.4287961}
{"args":["CMAKE_C_FLAGS","Flags used by the C compiler"],"cmd":"cmake_initialize_per_config_variable","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":96,"time":1753231094.4288111}
{"args":["STRIP","  ","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":9,"time":1753231094.4288321}
{"args":["CMAKE_C_FLAGS","","CACHE","STRING","Flags used by the C compiler during all build types."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":10,"line_end":11,"time":1753231094.428853}
{"args":["CMAKE_C_FLAGS"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":12,"time":1753231094.4288731}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":14,"time":1753231094.4288969}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":15,"time":1753231094.428916}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":17,"time":1753231094.428932}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":18,"time":1753231094.4289479}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":20,"time":1753231094.428977}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":21,"time":1753231094.428992}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":22,"line_end":23,"time":1753231094.429018}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":25,"time":1753231094.429049}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":28,"time":1753231094.4290631}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":29,"time":1753231094.4290791}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":30,"time":1753231094.429096}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":31,"time":1753231094.429121}
{"args":["STRIP"," -g","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":32,"time":1753231094.4291501}
{"args":["CMAKE_C_FLAGS_DEBUG","-g","CACHE","STRING","Flags used by the C compiler during DEBUG builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":33,"line_end":34,"time":1753231094.429168}
{"args":["CMAKE_C_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":35,"time":1753231094.4291949}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":30,"time":1753231094.42922}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":31,"time":1753231094.429245}
{"args":["STRIP"," -O3 -DNDEBUG","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":32,"time":1753231094.4292591}
{"args":["CMAKE_C_FLAGS_RELEASE","-O3 -DNDEBUG","CACHE","STRING","Flags used by the C compiler during RELEASE builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":33,"line_end":34,"time":1753231094.429275}
{"args":["CMAKE_C_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":35,"time":1753231094.429292}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":30,"time":1753231094.429306}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":31,"time":1753231094.4293211}
{"args":["STRIP"," -Os -DNDEBUG","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":32,"time":1753231094.4293461}
{"args":["CMAKE_C_FLAGS_MINSIZEREL","-Os -DNDEBUG","CACHE","STRING","Flags used by the C compiler during MINSIZEREL builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":33,"line_end":34,"time":1753231094.4293711}
{"args":["CMAKE_C_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":35,"time":1753231094.4294009}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":30,"time":1753231094.4294169}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":31,"time":1753231094.4294319}
{"args":["STRIP"," -O2 -g -DNDEBUG","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":32,"time":1753231094.4294469}
{"args":["CMAKE_C_FLAGS_RELWITHDEBINFO","-O2 -g -DNDEBUG","CACHE","STRING","Flags used by the C compiler during RELWITHDEBINFO builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":33,"line_end":34,"time":1753231094.4294629}
{"args":["CMAKE_C_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":35,"time":1753231094.4294801}
{"args":["CMAKE_C_STANDARD_LIBRARIES_INIT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":98,"time":1753231094.4294989}
{"args":["NOT","CMAKE_C_COMPILER_LAUNCHER","AND","DEFINED","ENV{CMAKE_C_COMPILER_LAUNCHER}"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":104,"time":1753231094.4295149}
{"args":["NOT","CMAKE_C_LINKER_LAUNCHER","AND","DEFINED","ENV{CMAKE_C_LINKER_LAUNCHER}"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":109,"time":1753231094.429534}
{"args":["CMakeCommonLanguageInclude"],"cmd":"include","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":114,"time":1753231094.4295509}
{"args":["APPEND","CMAKE_EXE_LINKER_FLAGS_INIT"," "],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":9,"time":1753231094.4297249}
{"args":["APPEND","CMAKE_SHARED_LINKER_FLAGS_INIT"," "],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":10,"time":1753231094.429745}
{"args":["APPEND","CMAKE_MODULE_LINKER_FLAGS_INIT"," "],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":11,"time":1753231094.42976}
{"args":["CMAKE_EXE_LINKER_FLAGS","Flags used by the linker"],"cmd":"cmake_initialize_per_config_variable","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":13,"time":1753231094.429786}
{"args":["STRIP"," ","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":9,"time":1753231094.42981}
{"args":["CMAKE_EXE_LINKER_FLAGS","","CACHE","STRING","Flags used by the linker during all build types."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":10,"line_end":11,"time":1753231094.429827}
{"args":["CMAKE_EXE_LINKER_FLAGS"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":12,"time":1753231094.429846}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":14,"time":1753231094.4298589}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":15,"time":1753231094.429877}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":17,"time":1753231094.429893}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":18,"time":1753231094.4299209}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":20,"time":1753231094.4299359}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":21,"time":1753231094.4299469}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":22,"line_end":23,"time":1753231094.4299641}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":25,"time":1753231094.4299819}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":28,"time":1753231094.4299951}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":29,"time":1753231094.430011}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.430027}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.430042}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.430057}
{"args":["CMAKE_EXE_LINKER_FLAGS_DEBUG","","CACHE","STRING","Flags used by the linker during DEBUG builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.430074}
{"args":["CMAKE_EXE_LINKER_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4300921}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4301071}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4301219}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.430136}
{"args":["CMAKE_EXE_LINKER_FLAGS_RELEASE","","CACHE","STRING","Flags used by the linker during RELEASE builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.4301519}
{"args":["CMAKE_EXE_LINKER_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4301691}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4301839}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.430198}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.430213}
{"args":["CMAKE_EXE_LINKER_FLAGS_MINSIZEREL","","CACHE","STRING","Flags used by the linker during MINSIZEREL builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.430239}
{"args":["CMAKE_EXE_LINKER_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4302621}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4302781}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4302931}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.4303081}
{"args":["CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO","","CACHE","STRING","Flags used by the linker during RELWITHDEBINFO builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.4303229}
{"args":["CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4303401}
{"args":["CMAKE_SHARED_LINKER_FLAGS","Flags used by the linker during the creation of shared libraries"],"cmd":"cmake_initialize_per_config_variable","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":14,"time":1753231094.430361}
{"args":["STRIP"," ","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":9,"time":1753231094.430382}
{"args":["CMAKE_SHARED_LINKER_FLAGS","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during all build types."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":10,"line_end":11,"time":1753231094.4303989}
{"args":["CMAKE_SHARED_LINKER_FLAGS"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":12,"time":1753231094.4304161}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":14,"time":1753231094.4304299}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":15,"time":1753231094.4304471}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":17,"time":1753231094.4304619}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":18,"time":1753231094.4304769}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":20,"time":1753231094.43049}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":21,"time":1753231094.430501}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":22,"line_end":23,"time":1753231094.430517}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":25,"time":1753231094.4305339}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":28,"time":1753231094.430557}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":29,"time":1753231094.4305739}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4305899}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4306059}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.43062}
{"args":["CMAKE_SHARED_LINKER_FLAGS_DEBUG","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during DEBUG builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.4306359}
{"args":["CMAKE_SHARED_LINKER_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.430655}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.43067}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.430685}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.4307001}
{"args":["CMAKE_SHARED_LINKER_FLAGS_RELEASE","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during RELEASE builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.430716}
{"args":["CMAKE_SHARED_LINKER_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.430733}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.430748}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.430763}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.4307771}
{"args":["CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.430793}
{"args":["CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4308109}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.430829}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4308441}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.4308591}
{"args":["CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.4308851}
{"args":["CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4309051}
{"args":["CMAKE_MODULE_LINKER_FLAGS","Flags used by the linker during the creation of modules"],"cmd":"cmake_initialize_per_config_variable","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":15,"time":1753231094.4309261}
{"args":["STRIP"," ","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":9,"time":1753231094.4309471}
{"args":["CMAKE_MODULE_LINKER_FLAGS","","CACHE","STRING","Flags used by the linker during the creation of modules during all build types."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":10,"line_end":11,"time":1753231094.430963}
{"args":["CMAKE_MODULE_LINKER_FLAGS"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":12,"time":1753231094.43098}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":14,"time":1753231094.430994}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":15,"time":1753231094.431011}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":17,"time":1753231094.431026}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":18,"time":1753231094.431041}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":20,"time":1753231094.4310541}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":21,"time":1753231094.4310651}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":22,"line_end":23,"time":1753231094.4310811}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":25,"time":1753231094.431097}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":28,"time":1753231094.4311111}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":29,"time":1753231094.4311261}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4311421}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4311571}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.4311719}
{"args":["CMAKE_MODULE_LINKER_FLAGS_DEBUG","","CACHE","STRING","Flags used by the linker during the creation of modules during DEBUG builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.4311979}
{"args":["CMAKE_MODULE_LINKER_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4312179}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4314489}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4314809}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.43151}
{"args":["CMAKE_MODULE_LINKER_FLAGS_RELEASE","","CACHE","STRING","Flags used by the linker during the creation of modules during RELEASE builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.43153}
{"args":["CMAKE_MODULE_LINKER_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4315519}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4315701}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.431586}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.431601}
{"args":["CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL","","CACHE","STRING","Flags used by the linker during the creation of modules during MINSIZEREL builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.431618}
{"args":["CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.431637}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4316521}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4316671}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.4316821}
{"args":["CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO","","CACHE","STRING","Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.4316981}
{"args":["CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.431716}
{"args":["CMAKE_STATIC_LINKER_FLAGS","Flags used by the linker during the creation of static libraries"],"cmd":"cmake_initialize_per_config_variable","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":16,"time":1753231094.4317379}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":9,"time":1753231094.431778}
{"args":["CMAKE_STATIC_LINKER_FLAGS","","CACHE","STRING","Flags used by the linker during the creation of static libraries during all build types."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":10,"line_end":11,"time":1753231094.4317961}
{"args":["CMAKE_STATIC_LINKER_FLAGS"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":12,"time":1753231094.431814}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":14,"time":1753231094.431828}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":15,"time":1753231094.4318471}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":17,"time":1753231094.4318631}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":18,"time":1753231094.431879}
{"args":[],"cmd":"else","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":20,"time":1753231094.4318919}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":21,"time":1753231094.4319029}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":22,"line_end":23,"time":1753231094.4319191}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":25,"time":1753231094.431936}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":28,"time":1753231094.4319501}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":29,"time":1753231094.4319661}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.431983}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4320021}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.432018}
{"args":["CMAKE_STATIC_LINKER_FLAGS_DEBUG","","CACHE","STRING","Flags used by the linker during the creation of static libraries during DEBUG builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.432034}
{"args":["CMAKE_STATIC_LINKER_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4320531}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4320681}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4320829}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.4321091}
{"args":["CMAKE_STATIC_LINKER_FLAGS_RELEASE","","CACHE","STRING","Flags used by the linker during the creation of static libraries during RELEASE builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.432126}
{"args":["CMAKE_STATIC_LINKER_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4321451}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4321599}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4321749}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.4321899}
{"args":["CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL","","CACHE","STRING","Flags used by the linker during the creation of static libraries during MINSIZEREL builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.4322059}
{"args":["CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4322231}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1753231094.4322381}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1753231094.4322519}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1753231094.432267}
{"args":["CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO","","CACHE","STRING","Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1753231094.432282}
{"args":["CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1753231094.4322989}
{"args":["CMAKE_BUILD_TOOL","/usr/bin/make"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":19,"time":1753231094.4323189}
{"args":["CMAKE_VERBOSE_MAKEFILE"],"cmd":"mark_as_advanced","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":21,"line_end":23,"time":1753231094.4323361}
{"args":["_cmake_common_language_platform_flags","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":27,"time":1753231094.4323499}
{"args":["C"],"cmd":"_cmake_common_language_platform_flags","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":115,"time":1753231094.4323809}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":28,"time":1753231094.4323969}
{"args":["NOT","DEFINED","CMAKE_C_COMPILE_OPTIONS_PIC"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":32,"time":1753231094.4324169}
{"args":["NOT","DEFINED","CMAKE_C_COMPILE_OPTIONS_PIE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":36,"time":1753231094.432447}
{"args":["NOT","DEFINED","CMAKE_C_LINK_OPTIONS_PIE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":39,"time":1753231094.4324651}
{"args":["NOT","DEFINED","CMAKE_C_LINK_OPTIONS_NO_PIE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":42,"time":1753231094.432482}
{"args":["NOT","DEFINED","CMAKE_C_COMPILE_OPTIONS_DLL"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":46,"time":1753231094.4324989}
{"args":["CMAKE_C_COMPILE_OPTIONS_DLL",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":47,"time":1753231094.4325149}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":50,"time":1753231094.4325299}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_LINK_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":54,"time":1753231094.4325471}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":58,"time":1753231094.432564}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG_SEP"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":62,"time":1753231094.4325809}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_RPATH_LINK_C_FLAG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":66,"time":1753231094.4325981}
{"args":["CMAKE_SHARED_LIBRARY_RPATH_LINK_C_FLAG",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":67,"time":1753231094.432615}
{"args":["NOT","DEFINED","CMAKE_EXE_EXPORTS_C_FLAG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":70,"time":1753231094.4326301}
{"args":["CMAKE_EXE_EXPORTS_C_FLAG",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":71,"time":1753231094.432646}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_SONAME_C_FLAG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":74,"time":1753231094.4326611}
{"args":["NOT","DEFINED","CMAKE_EXECUTABLE_RUNTIME_C_FLAG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":78,"time":1753231094.432678}
{"args":["CMAKE_EXECUTABLE_RUNTIME_C_FLAG","-Wl,-rpath,"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":79,"time":1753231094.432694}
{"args":["NOT","DEFINED","CMAKE_EXECUTABLE_RUNTIME_C_FLAG_SEP"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":82,"time":1753231094.4327099}
{"args":["CMAKE_EXECUTABLE_RUNTIME_C_FLAG_SEP",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":83,"time":1753231094.4327259}
{"args":["NOT","DEFINED","CMAKE_EXECUTABLE_RPATH_LINK_C_FLAG"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":86,"time":1753231094.43274}
{"args":["CMAKE_EXECUTABLE_RPATH_LINK_C_FLAG",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":87,"time":1753231094.4327569}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_LINK_C_WITH_RUNTIME_PATH"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":90,"time":1753231094.4327819}
{"args":["CMAKE_SHARED_LIBRARY_LINK_C_WITH_RUNTIME_PATH",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":91,"time":1753231094.432801}
{"args":["NOT","DEFINED","CMAKE_INCLUDE_FLAG_C"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":94,"time":1753231094.432816}
{"args":["NOT","CMAKE_MODULE_EXISTS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":101,"time":1753231094.432832}
{"args":["NOT","DEFINED","CMAKE_SHARED_MODULE_CREATE_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":106,"time":1753231094.4328489}
{"args":["NOT","DEFINED","CMAKE_SHARED_MODULE_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":109,"time":1753231094.4328661}
{"args":["CMAKE_SHARED_MODULE_C_FLAGS","-fPIC"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":110,"time":1753231094.4328821}
{"args":["type","IN","ITEMS","SHARED_LIBRARY","SHARED_MODULE","EXE"],"cmd":"foreach","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":113,"time":1753231094.432899}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_LINK_STATIC_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":114,"time":1753231094.43292}
{"args":["CMAKE_SHARED_LIBRARY_LINK_STATIC_C_FLAGS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":115,"line_end":116,"time":1753231094.4329369}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_LINK_DYNAMIC_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":118,"time":1753231094.4329519}
{"args":["CMAKE_SHARED_LIBRARY_LINK_DYNAMIC_C_FLAGS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":119,"line_end":120,"time":1753231094.4329679}
{"args":["NOT","DEFINED","CMAKE_SHARED_MODULE_LINK_STATIC_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":114,"time":1753231094.4329829}
{"args":["CMAKE_SHARED_MODULE_LINK_STATIC_C_FLAGS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":115,"line_end":116,"time":1753231094.4329989}
{"args":["NOT","DEFINED","CMAKE_SHARED_MODULE_LINK_DYNAMIC_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":118,"time":1753231094.4330139}
{"args":["CMAKE_SHARED_MODULE_LINK_DYNAMIC_C_FLAGS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":119,"line_end":120,"time":1753231094.4330299}
{"args":["NOT","DEFINED","CMAKE_EXE_LINK_STATIC_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":114,"time":1753231094.4330449}
{"args":["CMAKE_EXE_LINK_STATIC_C_FLAGS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":115,"line_end":116,"time":1753231094.4330609}
{"args":["NOT","DEFINED","CMAKE_EXE_LINK_DYNAMIC_C_FLAGS"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":118,"time":1753231094.433075}
{"args":["CMAKE_EXE_LINK_DYNAMIC_C_FLAGS",""],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":119,"line_end":120,"time":1753231094.4331081}
{"args":["NOT","CMAKE_C_CREATE_SHARED_LIBRARY"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":145,"time":1753231094.4331479}
{"args":["NOT","CMAKE_C_CREATE_SHARED_MODULE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":151,"time":1753231094.4331629}
{"args":["NOT","DEFINED","CMAKE_C_ARCHIVE_CREATE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":157,"time":1753231094.433177}
{"args":["CMAKE_C_ARCHIVE_CREATE","<CMAKE_AR> qc <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":158,"time":1753231094.433192}
{"args":["NOT","DEFINED","CMAKE_C_ARCHIVE_APPEND"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":160,"time":1753231094.4332261}
{"args":["CMAKE_C_ARCHIVE_APPEND","<CMAKE_AR> q <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":161,"time":1753231094.4332399}
{"args":["NOT","DEFINED","CMAKE_C_ARCHIVE_FINISH"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":163,"time":1753231094.4332631}
{"args":["CMAKE_C_ARCHIVE_FINISH","<CMAKE_RANLIB> <TARGET>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":164,"time":1753231094.4332759}
{"args":["NOT","CMAKE_C_COMPILE_OBJECT"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":168,"time":1753231094.4332891}
{"args":["CMAKE_C_COMPILE_OBJECT","<CMAKE_C_COMPILER> <DEFINES> <INCLUDES> <FLAGS> -o <OBJECT> -c <SOURCE>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":169,"line_end":170,"time":1753231094.4333031}
{"args":["NOT","CMAKE_C_LINK_EXECUTABLE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":173,"time":1753231094.4333179}
{"args":["CMAKE_C_LINK_EXECUTABLE","<CMAKE_C_COMPILER> <FLAGS> <CMAKE_C_LINK_FLAGS> <LINK_FLAGS> <OBJECTS> -o <TARGET> <LINK_LIBRARIES>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":174,"line_end":175,"time":1753231094.433332}
{"args":["CMAKE_C_USE_LINKER_INFORMATION","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":178,"time":1753231094.433347}
{"args":["CMAKE_C_INFORMATION_LOADED","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":180,"time":1753231094.4333601}
{"args":["CMAKE_C_COMPILER_FORCED"],"cmd":"if","file":"/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake","frame":2,"global_frame":2,"line":5,"time":1753231094.433532}
{"args":["CMAKE_C_COMPILER_WORKS","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake","frame":2,"global_frame":2,"line":8,"time":1753231094.4335511}
{"args":[],"cmd":"return","file":"/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake","frame":2,"global_frame":2,"line":9,"time":1753231094.4335649}
{"args":["Internal/CMakeCommonLinkerInformation"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":11,"time":1753231094.433671}
{"args":["_cmake_common_linker_platform_flags","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake","frame":3,"global_frame":3,"line":8,"time":1753231094.433826}
{"args":["_INCLUDED_FILE","0"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":13,"time":1753231094.4338529}
{"args":["CMAKE_C_COMPILER_LINKER_ID"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":16,"time":1753231094.433893}
{"args":["Linker/AppleClang-C","OPTIONAL"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":17,"time":1753231094.4339099}
{"args":["Linker/AppleClang"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake","frame":3,"global_frame":3,"line":1,"time":1753231094.4339941}
{"args":[],"cmd":"include_guard","file":"/usr/local/share/cmake/Modules/Linker/AppleClang.cmake","frame":4,"global_frame":4,"line":7,"time":1753231094.4340789}
{"args":["__linker_appleclang","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Linker/AppleClang.cmake","frame":4,"global_frame":4,"line":9,"time":1753231094.4340971}
{"args":["C"],"cmd":"__linker_appleclang","file":"/usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake","frame":3,"global_frame":3,"line":3,"time":1753231094.434123}
{"args":["CMAKE_C_LINK_OPTIONS_WARNING_AS_ERROR","LINKER:-fatal_warnings"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Linker/AppleClang.cmake","frame":4,"global_frame":4,"line":11,"time":1753231094.4341381}
{"args":["CMAKE_SYSTEM_PROCESSOR","AND","CMAKE_C_COMPILER_LINKER_ID"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":21,"time":1753231094.4341569}
{"args":["Platform/Apple-AppleClang-C-x86_64","OPTIONAL","RESULT_VARIABLE","_INCLUDED_FILE"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":22,"time":1753231094.4341769}
{"args":["CMAKE_C_COMPILER_LINKER_ID"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":27,"time":1753231094.4342141}
{"args":["Platform/Linker/Apple-AppleClang-C","OPTIONAL","RESULT_VARIABLE","_INCLUDED_FILE"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":28,"line_end":29,"time":1753231094.434231}
{"args":["Platform/Linker/Apple-AppleClang"],"cmd":"include","file":"/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake","frame":3,"global_frame":3,"line":4,"time":1753231094.434319}
{"args":[],"cmd":"include_guard","file":"/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake","frame":4,"global_frame":4,"line":6,"time":1753231094.434418}
{"args":["__apple_linker_appleclang","lang"],"cmd":"macro","file":"/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake","frame":4,"global_frame":4,"line":8,"time":1753231094.4344361}
{"args":["C"],"cmd":"__apple_linker_appleclang","file":"/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake","frame":3,"global_frame":3,"line":6,"time":1753231094.4344599}
{"args":["CMAKE_C_PLATFORM_LINKER_ID","AppleClang"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake","frame":4,"global_frame":4,"line":9,"time":1753231094.4344749}
{"args":["CMAKE_C_LINK_LIBRARIES_PROCESSING","ORDER=REVERSE","DEDUPLICATION=ALL"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake","frame":4,"global_frame":4,"line":10,"time":1753231094.43449}
{"args":["CMAKE_C_LINK_LIBRARY_USING_WHOLE_ARCHIVE","LINKER:-force_load,<LIB_ITEM>"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake","frame":4,"global_frame":4,"line":14,"time":1753231094.4345081}
{"args":["CMAKE_C_LINK_LIBRARY_USING_WHOLE_ARCHIVE_SUPPORTED","TRUE"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake","frame":4,"global_frame":4,"line":15,"time":1753231094.4345231}
{"args":["CMAKE_C_LINK_LIBRARY_WHOLE_ARCHIVE_ATTRIBUTES","LIBRARY_TYPE=STATIC","DEDUPLICATION=YES","OVERRIDE=DEFAULT"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake","frame":4,"global_frame":4,"line":16,"time":1753231094.4345391}
{"args":["NOT","_INCLUDED_FILE"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":33,"time":1753231094.4345729}
{"args":["C"],"cmd":"_cmake_common_linker_platform_flags","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":37,"time":1753231094.4345901}
{"args":["CMAKE_EXECUTABLE_FORMAT","STREQUAL","ELF"],"cmd":"if","file":"/usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake","frame":3,"global_frame":3,"line":10,"time":1753231094.4346049}
{"args":["CMAKE_C_LINKER_INFORMATION_LOADED","1"],"cmd":"set","file":"/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":39,"time":1753231094.4346271}
{"args":["/usr/local/share/cmake/Modules/CMakeCCompiler.cmake.in","/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeFiles/4.0.2/CMakeCCompiler.cmake","@ONLY"],"cmd":"configure_file","file":"/usr/local/share/cmake/Modules/Internal/CMakeInspectCLinker.cmake","frame":2,"global_frame":2,"line":5,"line_end":8,"time":1753231094.4347091}
{"args":["VERSION","4.0.2"],"cmd":"cmake_minimum_required","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":4,"time":1753231094.4352701}
{"args":["TMP_PATHS_LIST"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":6,"time":1753231094.4352989}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":7,"time":1753231094.4353149}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":8,"time":1753231094.4353311}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":9,"time":1753231094.4353449}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":10,"time":1753231094.435358}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":11,"time":1753231094.4353721}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":12,"time":1753231094.435385}
{"args":["APPEND","TMP_PATHS_LIST","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr;/usr/local;/usr/local;/usr;/;/usr/local;/usr/local;/usr/X11R6;/usr/pkg;/opt;/sw;/opt/local"],"cmd":"list","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":13,"time":1753231094.4353991}
{"args":["APPEND","TMP_PATHS_LIST","~/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Library/Frameworks;/Library/Frameworks;/Network/Library/Frameworks;/System/Library/Frameworks"],"cmd":"list","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":14,"time":1753231094.43542}
{"args":["APPEND","TMP_PATHS_LIST","/Applications;/Applications/Xcode.app/Contents/Applications;/Applications/Xcode.app/Contents/Developer/Applications"],"cmd":"list","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":15,"time":1753231094.4354579}
{"args":["LIB_ARCH_LIST"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":17,"time":1753231094.4354761}
{"args":["CMAKE_LIBRARY_ARCHITECTURE_REGEX"],"cmd":"if","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":18,"time":1753231094.4354889}
{"args":["MESON_ARCH_LIST",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":28,"time":1753231094.4355071}
{"args":["MESON_PATHS_LIST","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr;/usr/local;/usr/local;/usr;/;/usr/local;/usr/local;/usr/X11R6;/usr/pkg;/opt;/sw;/opt/local;~/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Library/Frameworks;/Library/Frameworks;/Network/Library/Frameworks;/System/Library/Frameworks;/Applications;/Applications/Xcode.app/Contents/Applications;/Applications/Xcode.app/Contents/Developer/Applications"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":29,"time":1753231094.4355209}
{"args":["MESON_CMAKE_ROOT","/usr/local/share/cmake"],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":30,"time":1753231094.43555}
{"args":["MESON_CMAKE_SYSROOT",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":31,"time":1753231094.435564}
{"args":["MESON_FIND_ROOT_PATH",""],"cmd":"set","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":32,"time":1753231094.4355781}
{"args":["STATUS","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr;/usr/local;/usr/local;/usr;/;/usr/local;/usr/local;/usr/X11R6;/usr/pkg;/opt;/sw;/opt/local;~/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Network/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks;/Applications/Xcode.app/Contents/Developer/Library/Frameworks;/Library/Frameworks;/Network/Library/Frameworks;/System/Library/Frameworks;/Applications;/Applications/Xcode.app/Contents/Applications;/Applications/Xcode.app/Contents/Developer/Applications"],"cmd":"message","file":"/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeLists.txt","frame":1,"global_frame":1,"line":34,"time":1753231094.4355919}
