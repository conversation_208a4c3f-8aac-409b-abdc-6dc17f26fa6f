# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "CMakeFiles/4.0.2/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.2/CMakeSystem.cmake"
  "CMakeLists.txt"
  "CMakeMesonToolchainFile.cmake"
  "/usr/local/share/cmake/Modules/CMakeCCompiler.cmake.in"
  "/usr/local/share/cmake/Modules/CMakeCInformation.cmake"
  "/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake"
  "/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake"
  "/usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake"
  "/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/usr/local/share/cmake/Modules/CMakeSystem.cmake.in"
  "/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake"
  "/usr/local/share/cmake/Modules/CMakeUnixFindMake.cmake"
  "/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake"
  "/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/local/share/cmake/Modules/Compiler/Clang.cmake"
  "/usr/local/share/cmake/Modules/Compiler/GNU.cmake"
  "/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"
  "/usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/usr/local/share/cmake/Modules/Internal/CMakeInspectCLinker.cmake"
  "/usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake"
  "/usr/local/share/cmake/Modules/Linker/AppleClang.cmake"
  "/usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"
  "/usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake"
  "/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake"
  "/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake"
  "/usr/local/share/cmake/Modules/Platform/Darwin.cmake"
  "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"
  "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"
  "/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/4.0.2/CMakeSystem.cmake"
  "CMakeFiles/4.0.2/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.2/CMakeCCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  )
