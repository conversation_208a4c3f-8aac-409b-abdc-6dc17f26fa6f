# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: CompInfo
# Configurations: 
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__ -B/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /usr/local/bin/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /usr/local/bin/ninja -t targets
  description = All primary targets available:

