set(CMAKE_HOST_SYSTEM "Darwin-21.6.0")
set(CMAKE_HOST_SYSTEM_NAME "Darwin")
set(CMAKE_HOST_SYSTEM_VERSION "21.6.0")
set(CMAKE_HOST_SYSTEM_PROCESSOR "x86_64")

include("/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__/CMakeMesonTempToolchainFile.cmake")

set(CMAKE_SYSTEM "Darwin-21.6.0")
set(CMAKE_SYSTEM_NAME "Darwin")
set(CMAKE_SYSTEM_VERSION "21.6.0")
set(CMAKE_SYSTEM_PROCESSOR "x86_64")

set(CMAKE_CROSSCOMPILING "FALSE")

set(CMAKE_SYSTEM_LOADED 1)
