# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: CompInfo
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__ && /usr/local/bin/ccmake -S/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__ -B/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__ && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__ -B/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__

build all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/__CMake_compiler_info__/cmake_install.cmake: RERUN_CMAKE | /usr/local/share/cmake/Modules/CMakeCCompiler.cmake.in /usr/local/share/cmake/Modules/CMakeCCompilerABI.c /usr/local/share/cmake/Modules/CMakeCInformation.cmake /usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake/Modules/CMakeCompilerIdDetection.cmake /usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake /usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake /usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake /usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake /usr/local/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake /usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake /usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake /usr/local/share/cmake/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake/Modules/CMakeNinjaFindMake.cmake /usr/local/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/local/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake /usr/local/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake /usr/local/share/cmake/Modules/CMakeSystem.cmake.in /usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake /usr/local/share/cmake/Modules/CMakeTestCompilerCommon.cmake /usr/local/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/local/share/cmake/Modules/Compiler/Clang.cmake /usr/local/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/GNU.cmake /usr/local/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/local/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake /usr/local/share/cmake/Modules/Internal/CMakeInspectCLinker.cmake /usr/local/share/cmake/Modules/Internal/FeatureTesting.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake /usr/local/share/cmake/Modules/Linker/AppleClang.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake /usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake /usr/local/share/cmake/Modules/Platform/Darwin.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /usr/local/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake CMakeLists.txt CMakeMesonTempToolchainFile.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /usr/local/share/cmake/Modules/CMakeCCompiler.cmake.in /usr/local/share/cmake/Modules/CMakeCCompilerABI.c /usr/local/share/cmake/Modules/CMakeCInformation.cmake /usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake/Modules/CMakeCompilerIdDetection.cmake /usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake /usr/local/share/cmake/Modules/CMakeDetermineCompiler.cmake /usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake /usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake /usr/local/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake /usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake /usr/local/share/cmake/Modules/CMakeFindBinUtils.cmake /usr/local/share/cmake/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake/Modules/CMakeNinjaFindMake.cmake /usr/local/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/local/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake /usr/local/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake /usr/local/share/cmake/Modules/CMakeSystem.cmake.in /usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake /usr/local/share/cmake/Modules/CMakeTestCompilerCommon.cmake /usr/local/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/local/share/cmake/Modules/Compiler/Clang.cmake /usr/local/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/GNU.cmake /usr/local/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/local/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake /usr/local/share/cmake/Modules/Internal/CMakeInspectCLinker.cmake /usr/local/share/cmake/Modules/Internal/FeatureTesting.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake /usr/local/share/cmake/Modules/Linker/AppleClang.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake /usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake /usr/local/share/cmake/Modules/Platform/Darwin.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /usr/local/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake CMakeLists.txt CMakeMesonTempToolchainFile.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
