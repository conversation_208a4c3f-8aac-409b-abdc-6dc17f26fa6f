# This is the build file for project "fontconfig"
# It is autogenerated by the Meson build system.
# Do not edit by hand.

ninja_required_version = 1.8.2

# Rules for module scanning.

# Rules for compiling.

rule c_COMPILER
 command = cc $ARGS -MD -MQ $out -MF $DEPFILE -o $out -c $in
 deps = gcc
 depfile = $DEPFILE_UNQUOTED
 description = Compiling C object $out

rule c_PREPROCESSOR
 command = cc -E -P -xc $ARGS -MD -MQ $out -MF $DEPFILE -o $out -c $in
 deps = gcc
 depfile = $DEPFILE_UNQUOTED
 description = Compiling C object $out

# Rules for linking.

rule STATIC_LINKER
 command = rm -f $out && ar $LINK_ARGS $out $in && ranlib -c $out
 description = Linking static target $out

rule c_LINKER
 command = cc $ARGS -o $out $in $LINK_ARGS
 description = Linking target $out

rule SHSYM
 command = /usr/local/bin/meson --internal symbolextractor /Users/<USER>/work/fontconfig/build_meson_dll $in $IMPLIB $out $CROSS
 description = Generating symbol file $out
 restat = 1

# Other rules

rule CUSTOM_COMMAND
 command = $COMMAND
 description = $DESC
 restat = 1

rule REGENERATE_BUILD
 command = /usr/local/bin/meson --internal regenerate /Users/<USER>/work/fontconfig .
 description = Regenerating build files
 generator = 1

# Phony build target, always out of date

build PHONY: phony 

# Build rules for targets

build fcstdint.h: CUSTOM_COMMAND ../src/fcstdint.h.in
 COMMAND = /usr/local/bin/meson --internal copy ../src/fcstdint.h.in fcstdint.h
 description = Copying$ file$ fcstdint.h

build fcalias.h fcaliastail.h: CUSTOM_COMMAND fontconfig/fontconfig.h ../src/fcdeprecate.h ../fontconfig/fcprivate.h | ../src/makealias.py /usr/local/opt/python@3.13/bin/python3.13
 COMMAND = /usr/local/opt/python@3.13/bin/python3.13 ../src/makealias.py /Users/<USER>/work/fontconfig/src fcalias.h fcaliastail.h fontconfig/fontconfig.h ../src/fcdeprecate.h ../fontconfig/fcprivate.h
 description = Generating$ alias_headers$ with$ a$ custom$ command

build fcftalias.h fcftaliastail.h: CUSTOM_COMMAND ../fontconfig/fcfreetype.h | ../src/makealias.py /usr/local/opt/python@3.13/bin/python3.13
 COMMAND = /usr/local/opt/python@3.13/bin/python3.13 ../src/makealias.py /Users/<USER>/work/fontconfig/src fcftalias.h fcftaliastail.h ../fontconfig/fcfreetype.h
 description = Generating$ ft_alias_headers$ with$ a$ custom$ command

build fc-case/fccase.h: CUSTOM_COMMAND ../fc-case/CaseFolding.txt ../fc-case/fccase.tmpl.h | /Users/<USER>/work/fontconfig/fc-case/fc-case.py
 COMMAND = /Users/<USER>/work/fontconfig/fc-case/fc-case.py ../fc-case/CaseFolding.txt --template ../fc-case/fccase.tmpl.h --output fc-case/fccase.h
 description = Generating$ fc-case/fccase.h$ with$ a$ custom$ command

build fc-lang/fclang.h: CUSTOM_COMMAND ../fc-lang/aa.orth ../fc-lang/ab.orth ../fc-lang/af.orth ../fc-lang/am.orth ../fc-lang/ar.orth ../fc-lang/as.orth ../fc-lang/ast.orth ../fc-lang/av.orth ../fc-lang/ay.orth ../fc-lang/az_az.orth ../fc-lang/az_ir.orth ../fc-lang/ba.orth ../fc-lang/bm.orth ../fc-lang/be.orth ../fc-lang/bg.orth ../fc-lang/bh.orth ../fc-lang/bho.orth ../fc-lang/bi.orth ../fc-lang/bin.orth ../fc-lang/bn.orth ../fc-lang/bo.orth ../fc-lang/br.orth ../fc-lang/bs.orth ../fc-lang/bua.orth ../fc-lang/ca.orth ../fc-lang/ce.orth ../fc-lang/ch.orth ../fc-lang/chm.orth ../fc-lang/chr.orth ../fc-lang/co.orth ../fc-lang/cs.orth ../fc-lang/cu.orth ../fc-lang/cv.orth ../fc-lang/cy.orth ../fc-lang/da.orth ../fc-lang/de.orth ../fc-lang/dz.orth ../fc-lang/el.orth ../fc-lang/en.orth ../fc-lang/eo.orth ../fc-lang/es.orth ../fc-lang/et.orth ../fc-lang/eu.orth ../fc-lang/fa.orth ../fc-lang/fi.orth ../fc-lang/fj.orth ../fc-lang/fo.orth ../fc-lang/fr.orth ../fc-lang/ff.orth ../fc-lang/fur.orth ../fc-lang/fy.orth ../fc-lang/ga.orth ../fc-lang/gd.orth ../fc-lang/gez.orth ../fc-lang/gl.orth ../fc-lang/gn.orth ../fc-lang/gu.orth ../fc-lang/gv.orth ../fc-lang/ha.orth ../fc-lang/haw.orth ../fc-lang/he.orth ../fc-lang/hi.orth ../fc-lang/ho.orth ../fc-lang/hr.orth ../fc-lang/hu.orth ../fc-lang/hy.orth ../fc-lang/ia.orth ../fc-lang/ig.orth ../fc-lang/id.orth ../fc-lang/ie.orth ../fc-lang/ik.orth ../fc-lang/io.orth ../fc-lang/is.orth ../fc-lang/it.orth ../fc-lang/iu.orth ../fc-lang/ja.orth ../fc-lang/ka.orth ../fc-lang/kaa.orth ../fc-lang/ki.orth ../fc-lang/kk.orth ../fc-lang/kl.orth ../fc-lang/km.orth ../fc-lang/kn.orth ../fc-lang/ko.orth ../fc-lang/kok.orth ../fc-lang/ks.orth ../fc-lang/ku_am.orth ../fc-lang/ku_ir.orth ../fc-lang/kum.orth ../fc-lang/kv.orth ../fc-lang/kw.orth ../fc-lang/ky.orth ../fc-lang/la.orth ../fc-lang/lb.orth ../fc-lang/lez.orth ../fc-lang/ln.orth ../fc-lang/lo.orth ../fc-lang/lt.orth ../fc-lang/lv.orth ../fc-lang/mg.orth ../fc-lang/mh.orth ../fc-lang/mi.orth ../fc-lang/mk.orth ../fc-lang/ml.orth ../fc-lang/mn_cn.orth ../fc-lang/mo.orth ../fc-lang/mr.orth ../fc-lang/mt.orth ../fc-lang/my.orth ../fc-lang/nb.orth ../fc-lang/nds.orth ../fc-lang/ne.orth ../fc-lang/nl.orth ../fc-lang/nn.orth ../fc-lang/no.orth ../fc-lang/nr.orth ../fc-lang/nso.orth ../fc-lang/ny.orth ../fc-lang/oc.orth ../fc-lang/om.orth ../fc-lang/or.orth ../fc-lang/os.orth ../fc-lang/pa.orth ../fc-lang/pl.orth ../fc-lang/ps_af.orth ../fc-lang/ps_pk.orth ../fc-lang/pt.orth ../fc-lang/rm.orth ../fc-lang/ro.orth ../fc-lang/ru.orth ../fc-lang/sa.orth ../fc-lang/sah.orth ../fc-lang/sco.orth ../fc-lang/se.orth ../fc-lang/sel.orth ../fc-lang/sh.orth ../fc-lang/shs.orth ../fc-lang/si.orth ../fc-lang/sk.orth ../fc-lang/sl.orth ../fc-lang/sm.orth ../fc-lang/sma.orth ../fc-lang/smj.orth ../fc-lang/smn.orth ../fc-lang/sms.orth ../fc-lang/so.orth ../fc-lang/sq.orth ../fc-lang/sr.orth ../fc-lang/ss.orth ../fc-lang/st.orth ../fc-lang/sv.orth ../fc-lang/sw.orth ../fc-lang/syr.orth ../fc-lang/ta.orth ../fc-lang/te.orth ../fc-lang/tg.orth ../fc-lang/th.orth ../fc-lang/ti_er.orth ../fc-lang/ti_et.orth ../fc-lang/tig.orth ../fc-lang/tk.orth ../fc-lang/tl.orth ../fc-lang/tn.orth ../fc-lang/to.orth ../fc-lang/tr.orth ../fc-lang/ts.orth ../fc-lang/tt.orth ../fc-lang/tw.orth ../fc-lang/tyv.orth ../fc-lang/ug.orth ../fc-lang/uk.orth ../fc-lang/ur.orth ../fc-lang/uz.orth ../fc-lang/ve.orth ../fc-lang/vi.orth ../fc-lang/vo.orth ../fc-lang/vot.orth ../fc-lang/wa.orth ../fc-lang/wen.orth ../fc-lang/wo.orth ../fc-lang/xh.orth ../fc-lang/yap.orth ../fc-lang/yi.orth ../fc-lang/yo.orth ../fc-lang/zh_cn.orth ../fc-lang/zh_hk.orth ../fc-lang/zh_mo.orth ../fc-lang/zh_sg.orth ../fc-lang/zh_tw.orth ../fc-lang/zu.orth ../fc-lang/ak.orth ../fc-lang/an.orth ../fc-lang/ber_dz.orth ../fc-lang/ber_ma.orth ../fc-lang/byn.orth ../fc-lang/crh.orth ../fc-lang/csb.orth ../fc-lang/dv.orth ../fc-lang/ee.orth ../fc-lang/fat.orth ../fc-lang/fil.orth ../fc-lang/hne.orth ../fc-lang/hsb.orth ../fc-lang/ht.orth ../fc-lang/hz.orth ../fc-lang/ii.orth ../fc-lang/jv.orth ../fc-lang/kab.orth ../fc-lang/kj.orth ../fc-lang/kr.orth ../fc-lang/ku_iq.orth ../fc-lang/ku_tr.orth ../fc-lang/kwm.orth ../fc-lang/lg.orth ../fc-lang/li.orth ../fc-lang/mai.orth ../fc-lang/mn_mn.orth ../fc-lang/ms.orth ../fc-lang/na.orth ../fc-lang/ng.orth ../fc-lang/nv.orth ../fc-lang/ota.orth ../fc-lang/pa_pk.orth ../fc-lang/pap_an.orth ../fc-lang/pap_aw.orth ../fc-lang/qu.orth ../fc-lang/quz.orth ../fc-lang/rn.orth ../fc-lang/rw.orth ../fc-lang/sc.orth ../fc-lang/sd.orth ../fc-lang/sg.orth ../fc-lang/sid.orth ../fc-lang/sn.orth ../fc-lang/su.orth ../fc-lang/ty.orth ../fc-lang/wal.orth ../fc-lang/za.orth ../fc-lang/lah.orth ../fc-lang/nqo.orth ../fc-lang/brx.orth ../fc-lang/sat.orth ../fc-lang/doi.orth ../fc-lang/mni.orth ../fc-lang/und_zsye.orth ../fc-lang/und_zmth.orth ../fc-lang/anp.orth ../fc-lang/bhb.orth ../fc-lang/hif.orth ../fc-lang/mag.orth ../fc-lang/raj.orth ../fc-lang/the.orth ../fc-lang/agr.orth ../fc-lang/ayc.orth ../fc-lang/bem.orth ../fc-lang/ckb.orth ../fc-lang/cmn.orth ../fc-lang/dsb.orth ../fc-lang/hak.orth ../fc-lang/lij.orth ../fc-lang/lzh.orth ../fc-lang/mfe.orth ../fc-lang/mhr.orth ../fc-lang/miq.orth ../fc-lang/mjw.orth ../fc-lang/mnw.orth ../fc-lang/nan.orth ../fc-lang/nhn.orth ../fc-lang/niu.orth ../fc-lang/rif.orth ../fc-lang/sgs.orth ../fc-lang/shn.orth ../fc-lang/szl.orth ../fc-lang/tcy.orth ../fc-lang/tpi.orth ../fc-lang/unm.orth ../fc-lang/wae.orth ../fc-lang/yue.orth ../fc-lang/yuw.orth ../fc-lang/got.orth ../fc-lang/cop.orth | ../fc-lang/fclang.tmpl.h /Users/<USER>/work/fontconfig/fc-lang/fc-lang.py
 COMMAND = /Users/<USER>/work/fontconfig/fc-lang/fc-lang.py aa.orth ab.orth af.orth am.orth ar.orth as.orth ast.orth av.orth ay.orth az_az.orth az_ir.orth ba.orth bm.orth be.orth bg.orth bh.orth bho.orth bi.orth bin.orth bn.orth bo.orth br.orth bs.orth bua.orth ca.orth ce.orth ch.orth chm.orth chr.orth co.orth cs.orth cu.orth cv.orth cy.orth da.orth de.orth dz.orth el.orth en.orth eo.orth es.orth et.orth eu.orth fa.orth fi.orth fj.orth fo.orth fr.orth ff.orth fur.orth fy.orth ga.orth gd.orth gez.orth gl.orth gn.orth gu.orth gv.orth ha.orth haw.orth he.orth hi.orth ho.orth hr.orth hu.orth hy.orth ia.orth ig.orth id.orth ie.orth ik.orth io.orth is.orth it.orth iu.orth ja.orth ka.orth kaa.orth ki.orth kk.orth kl.orth km.orth kn.orth ko.orth kok.orth ks.orth ku_am.orth ku_ir.orth kum.orth kv.orth kw.orth ky.orth la.orth lb.orth lez.orth ln.orth lo.orth lt.orth lv.orth mg.orth mh.orth mi.orth mk.orth ml.orth mn_cn.orth mo.orth mr.orth mt.orth my.orth nb.orth nds.orth ne.orth nl.orth nn.orth no.orth nr.orth nso.orth ny.orth oc.orth om.orth or.orth os.orth pa.orth pl.orth ps_af.orth ps_pk.orth pt.orth rm.orth ro.orth ru.orth sa.orth sah.orth sco.orth se.orth sel.orth sh.orth shs.orth si.orth sk.orth sl.orth sm.orth sma.orth smj.orth smn.orth sms.orth so.orth sq.orth sr.orth ss.orth st.orth sv.orth sw.orth syr.orth ta.orth te.orth tg.orth th.orth ti_er.orth ti_et.orth tig.orth tk.orth tl.orth tn.orth to.orth tr.orth ts.orth tt.orth tw.orth tyv.orth ug.orth uk.orth ur.orth uz.orth ve.orth vi.orth vo.orth vot.orth wa.orth wen.orth wo.orth xh.orth yap.orth yi.orth yo.orth zh_cn.orth zh_hk.orth zh_mo.orth zh_sg.orth zh_tw.orth zu.orth ak.orth an.orth ber_dz.orth ber_ma.orth byn.orth crh.orth csb.orth dv.orth ee.orth fat.orth fil.orth hne.orth hsb.orth ht.orth hz.orth ii.orth jv.orth kab.orth kj.orth kr.orth ku_iq.orth ku_tr.orth kwm.orth lg.orth li.orth mai.orth mn_mn.orth ms.orth na.orth ng.orth nv.orth ota.orth pa_pk.orth pap_an.orth pap_aw.orth qu.orth quz.orth rn.orth rw.orth sc.orth sd.orth sg.orth sid.orth sn.orth su.orth ty.orth wal.orth za.orth lah.orth nqo.orth brx.orth sat.orth doi.orth mni.orth und_zsye.orth und_zmth.orth anp.orth bhb.orth hif.orth mag.orth raj.orth the.orth agr.orth ayc.orth bem.orth ckb.orth cmn.orth dsb.orth hak.orth lij.orth lzh.orth mfe.orth mhr.orth miq.orth mjw.orth mnw.orth nan.orth nhn.orth niu.orth rif.orth sgs.orth shn.orth szl.orth tcy.orth tpi.orth unm.orth wae.orth yue.orth yuw.orth got.orth cop.orth --template ../fc-lang/fclang.tmpl.h --output fc-lang/fclang.h --directory /Users/<USER>/work/fontconfig/fc-lang
 description = Generating$ fc-lang/fclang.h$ with$ a$ custom$ command

build src/libpatternlib_internal.a.p/fcpat.c.o: c_COMPILER ../src/fcpat.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = src/libpatternlib_internal.a.p/fcpat.c.o.d
 DEPFILE_UNQUOTED = src/libpatternlib_internal.a.p/fcpat.c.o.d
 ARGS = -Isrc/libpatternlib_internal.a.p -Isrc -I../src -I. -I.. -Ifc-lang -I../fc-lang -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build src/libpatternlib_internal.a: STATIC_LINKER src/libpatternlib_internal.a.p/fcpat.c.o
 LINK_ARGS = csr

build src/preprocessor_0.p/fcobjshash.gperf.h.i: c_PREPROCESSOR ../src/fcobjshash.gperf.h
 DEPFILE = src/preprocessor_0.p/fcobjshash.gperf.h.i.d
 DEPFILE_UNQUOTED = src/preprocessor_0.p/fcobjshash.gperf.h.i.d
 ARGS = -Isrc/preprocessor_0.p -Isrc -I../src -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build src/fcobjshash.gperf: CUSTOM_COMMAND src/preprocessor_0.p/fcobjshash.gperf.h.i | /Users/<USER>/work/fontconfig/src/cutout.py
 COMMAND = /Users/<USER>/work/fontconfig/src/cutout.py src/preprocessor_0.p/fcobjshash.gperf.h.i src/fcobjshash.gperf
 description = Generating$ src/fcobjshash.gperf$ with$ a$ custom$ command

build src/fcobjshash.h: CUSTOM_COMMAND src/fcobjshash.gperf | /usr/bin/gperf
 COMMAND = /usr/bin/gperf --pic -m 100 src/fcobjshash.gperf --output-file src/fcobjshash.h
 description = Generating$ src/fcobjshash.h$ with$ a$ custom$ command

build libfontconfig.1.dylib.p/src_fcatomic.c.o: c_COMPILER ../src/fcatomic.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcatomic.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcatomic.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fccache.c.o: c_COMPILER ../src/fccache.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fccache.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fccache.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fccfg.c.o: c_COMPILER ../src/fccfg.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fccfg.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fccfg.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fccharset.c.o: c_COMPILER ../src/fccharset.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fccharset.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fccharset.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fccompat.c.o: c_COMPILER ../src/fccompat.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fccompat.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fccompat.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcdbg.c.o: c_COMPILER ../src/fcdbg.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcdbg.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcdbg.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcdefault.c.o: c_COMPILER ../src/fcdefault.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcdefault.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcdefault.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcdir.c.o: c_COMPILER ../src/fcdir.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcdir.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcdir.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcformat.c.o: c_COMPILER ../src/fcformat.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcformat.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcformat.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcfreetype.c.o: c_COMPILER ../src/fcfreetype.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcfreetype.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcfreetype.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcfs.c.o: c_COMPILER ../src/fcfs.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcfs.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcfs.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcptrlist.c.o: c_COMPILER ../src/fcptrlist.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcptrlist.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcptrlist.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fchash.c.o: c_COMPILER ../src/fchash.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fchash.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fchash.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcinit.c.o: c_COMPILER ../src/fcinit.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcinit.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcinit.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fclang.c.o: c_COMPILER ../src/fclang.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fclang.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fclang.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fclist.c.o: c_COMPILER ../src/fclist.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fclist.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fclist.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcmatch.c.o: c_COMPILER ../src/fcmatch.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcmatch.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcmatch.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcmatrix.c.o: c_COMPILER ../src/fcmatrix.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcmatrix.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcmatrix.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcname.c.o: c_COMPILER ../src/fcname.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcname.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcname.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcobjs.c.o: c_COMPILER ../src/fcobjs.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcobjs.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcobjs.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcrange.c.o: c_COMPILER ../src/fcrange.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcrange.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcrange.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcserialize.c.o: c_COMPILER ../src/fcserialize.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcserialize.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcserialize.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcstat.c.o: c_COMPILER ../src/fcstat.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcstat.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcstat.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcstr.c.o: c_COMPILER ../src/fcstr.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcstr.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcstr.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcweight.c.o: c_COMPILER ../src/fcweight.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcweight.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcweight.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_fcxml.c.o: c_COMPILER ../src/fcxml.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_fcxml.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_fcxml.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/src_ftglue.c.o: c_COMPILER ../src/ftglue.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.1.dylib.p/src_ftglue.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.1.dylib.p/src_ftglue.c.o.d
 ARGS = -Ilibfontconfig.1.dylib.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols: SHSYM libfontconfig.1.dylib
 IMPLIB = libfontconfig.1.dylib

build libfontconfig.1.dylib: c_LINKER libfontconfig.1.dylib.p/src_fcatomic.c.o libfontconfig.1.dylib.p/src_fccache.c.o libfontconfig.1.dylib.p/src_fccfg.c.o libfontconfig.1.dylib.p/src_fccharset.c.o libfontconfig.1.dylib.p/src_fccompat.c.o libfontconfig.1.dylib.p/src_fcdbg.c.o libfontconfig.1.dylib.p/src_fcdefault.c.o libfontconfig.1.dylib.p/src_fcdir.c.o libfontconfig.1.dylib.p/src_fcformat.c.o libfontconfig.1.dylib.p/src_fcfreetype.c.o libfontconfig.1.dylib.p/src_fcfs.c.o libfontconfig.1.dylib.p/src_fcptrlist.c.o libfontconfig.1.dylib.p/src_fchash.c.o libfontconfig.1.dylib.p/src_fcinit.c.o libfontconfig.1.dylib.p/src_fclang.c.o libfontconfig.1.dylib.p/src_fclist.c.o libfontconfig.1.dylib.p/src_fcmatch.c.o libfontconfig.1.dylib.p/src_fcmatrix.c.o libfontconfig.1.dylib.p/src_fcname.c.o libfontconfig.1.dylib.p/src_fcobjs.c.o libfontconfig.1.dylib.p/src_fcrange.c.o libfontconfig.1.dylib.p/src_fcserialize.c.o libfontconfig.1.dylib.p/src_fcstat.c.o libfontconfig.1.dylib.p/src_fcstr.c.o libfontconfig.1.dylib.p/src_fcweight.c.o libfontconfig.1.dylib.p/src_fcxml.c.o libfontconfig.1.dylib.p/src_ftglue.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -shared -install_name @rpath/libfontconfig.1.dylib -compatibility_version 17 -current_version 17 -Wl,-rpath,/usr/local/opt/freetype/lib src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build libfontconfig.a.p/src_fcatomic.c.o: c_COMPILER ../src/fcatomic.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcatomic.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcatomic.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fccache.c.o: c_COMPILER ../src/fccache.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fccache.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fccache.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fccfg.c.o: c_COMPILER ../src/fccfg.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fccfg.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fccfg.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fccharset.c.o: c_COMPILER ../src/fccharset.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fccharset.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fccharset.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fccompat.c.o: c_COMPILER ../src/fccompat.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fccompat.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fccompat.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcdbg.c.o: c_COMPILER ../src/fcdbg.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcdbg.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcdbg.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcdefault.c.o: c_COMPILER ../src/fcdefault.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcdefault.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcdefault.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcdir.c.o: c_COMPILER ../src/fcdir.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcdir.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcdir.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcformat.c.o: c_COMPILER ../src/fcformat.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcformat.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcformat.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcfreetype.c.o: c_COMPILER ../src/fcfreetype.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcfreetype.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcfreetype.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcfs.c.o: c_COMPILER ../src/fcfs.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcfs.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcfs.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcptrlist.c.o: c_COMPILER ../src/fcptrlist.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcptrlist.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcptrlist.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fchash.c.o: c_COMPILER ../src/fchash.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fchash.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fchash.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcinit.c.o: c_COMPILER ../src/fcinit.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcinit.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcinit.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fclang.c.o: c_COMPILER ../src/fclang.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fclang.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fclang.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fclist.c.o: c_COMPILER ../src/fclist.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fclist.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fclist.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcmatch.c.o: c_COMPILER ../src/fcmatch.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcmatch.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcmatch.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcmatrix.c.o: c_COMPILER ../src/fcmatrix.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcmatrix.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcmatrix.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcname.c.o: c_COMPILER ../src/fcname.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcname.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcname.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcobjs.c.o: c_COMPILER ../src/fcobjs.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcobjs.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcobjs.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcrange.c.o: c_COMPILER ../src/fcrange.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcrange.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcrange.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcserialize.c.o: c_COMPILER ../src/fcserialize.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcserialize.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcserialize.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcstat.c.o: c_COMPILER ../src/fcstat.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcstat.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcstat.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcstr.c.o: c_COMPILER ../src/fcstr.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcstr.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcstr.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcweight.c.o: c_COMPILER ../src/fcweight.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcweight.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcweight.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_fcxml.c.o: c_COMPILER ../src/fcxml.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_fcxml.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_fcxml.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a.p/src_ftglue.c.o: c_COMPILER ../src/ftglue.c || fc-case/fccase.h fc-lang/fclang.h fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h src/fcobjshash.h
 DEPFILE = libfontconfig.a.p/src_ftglue.c.o.d
 DEPFILE_UNQUOTED = libfontconfig.a.p/src_ftglue.c.o.d
 ARGS = -Ilibfontconfig.a.p -I. -I.. -Ifc-lang -I../fc-lang -Ifc-case -Isrc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build libfontconfig.a: STATIC_LINKER libfontconfig.a.p/src_fcatomic.c.o libfontconfig.a.p/src_fccache.c.o libfontconfig.a.p/src_fccfg.c.o libfontconfig.a.p/src_fccharset.c.o libfontconfig.a.p/src_fccompat.c.o libfontconfig.a.p/src_fcdbg.c.o libfontconfig.a.p/src_fcdefault.c.o libfontconfig.a.p/src_fcdir.c.o libfontconfig.a.p/src_fcformat.c.o libfontconfig.a.p/src_fcfreetype.c.o libfontconfig.a.p/src_fcfs.c.o libfontconfig.a.p/src_fcptrlist.c.o libfontconfig.a.p/src_fchash.c.o libfontconfig.a.p/src_fcinit.c.o libfontconfig.a.p/src_fclang.c.o libfontconfig.a.p/src_fclist.c.o libfontconfig.a.p/src_fcmatch.c.o libfontconfig.a.p/src_fcmatrix.c.o libfontconfig.a.p/src_fcname.c.o libfontconfig.a.p/src_fcobjs.c.o libfontconfig.a.p/src_fcrange.c.o libfontconfig.a.p/src_fcserialize.c.o libfontconfig.a.p/src_fcstat.c.o libfontconfig.a.p/src_fcstr.c.o libfontconfig.a.p/src_fcweight.c.o libfontconfig.a.p/src_fcxml.c.o libfontconfig.a.p/src_ftglue.c.o
 LINK_ARGS = csr

build fc-cache/fc-cache.p/fc-cache.c.o: c_COMPILER ../fc-cache/fc-cache.c || fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h
 DEPFILE = fc-cache/fc-cache.p/fc-cache.c.o.d
 DEPFILE_UNQUOTED = fc-cache/fc-cache.p/fc-cache.c.o.d
 ARGS = -Ifc-cache/fc-cache.p -Ifc-cache -I../fc-cache -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build fc-cache/fc-cache: c_LINKER fc-cache/fc-cache.p/fc-cache.c.o | libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,@loader_path/.. -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.1.dylib

build fc-cat/fc-cat.p/fc-cat.c.o: c_COMPILER ../fc-cat/fc-cat.c || fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h
 DEPFILE = fc-cat/fc-cat.p/fc-cat.c.o.d
 DEPFILE_UNQUOTED = fc-cat/fc-cat.p/fc-cat.c.o.d
 ARGS = -Ifc-cat/fc-cat.p -Ifc-cat -I../fc-cat -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build fc-cat/fc-cat: c_LINKER fc-cat/fc-cat.p/fc-cat.c.o | libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,@loader_path/.. -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.1.dylib

build fc-conflist/fc-conflist.p/fc-conflist.c.o: c_COMPILER ../fc-conflist/fc-conflist.c || fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h
 DEPFILE = fc-conflist/fc-conflist.p/fc-conflist.c.o.d
 DEPFILE_UNQUOTED = fc-conflist/fc-conflist.p/fc-conflist.c.o.d
 ARGS = -Ifc-conflist/fc-conflist.p -Ifc-conflist -I../fc-conflist -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build fc-conflist/fc-conflist: c_LINKER fc-conflist/fc-conflist.p/fc-conflist.c.o | libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,@loader_path/.. -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.1.dylib

build fc-list/fc-list.p/fc-list.c.o: c_COMPILER ../fc-list/fc-list.c || fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h
 DEPFILE = fc-list/fc-list.p/fc-list.c.o.d
 DEPFILE_UNQUOTED = fc-list/fc-list.p/fc-list.c.o.d
 ARGS = -Ifc-list/fc-list.p -Ifc-list -I../fc-list -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build fc-list/fc-list: c_LINKER fc-list/fc-list.p/fc-list.c.o | libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,@loader_path/.. -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.1.dylib

build fc-match/fc-match.p/fc-match.c.o: c_COMPILER ../fc-match/fc-match.c || fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h
 DEPFILE = fc-match/fc-match.p/fc-match.c.o.d
 DEPFILE_UNQUOTED = fc-match/fc-match.p/fc-match.c.o.d
 ARGS = -Ifc-match/fc-match.p -Ifc-match -I../fc-match -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build fc-match/fc-match: c_LINKER fc-match/fc-match.p/fc-match.c.o | libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,@loader_path/.. -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.1.dylib

build fc-pattern/fc-pattern.p/fc-pattern.c.o: c_COMPILER ../fc-pattern/fc-pattern.c || fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h
 DEPFILE = fc-pattern/fc-pattern.p/fc-pattern.c.o.d
 DEPFILE_UNQUOTED = fc-pattern/fc-pattern.p/fc-pattern.c.o.d
 ARGS = -Ifc-pattern/fc-pattern.p -Ifc-pattern -I../fc-pattern -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build fc-pattern/fc-pattern: c_LINKER fc-pattern/fc-pattern.p/fc-pattern.c.o | libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,@loader_path/.. -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.1.dylib

build fc-query/fc-query.p/fc-query.c.o: c_COMPILER ../fc-query/fc-query.c || fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h
 DEPFILE = fc-query/fc-query.p/fc-query.c.o.d
 DEPFILE_UNQUOTED = fc-query/fc-query.p/fc-query.c.o.d
 ARGS = -Ifc-query/fc-query.p -Ifc-query -I../fc-query -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build fc-query/fc-query: c_LINKER fc-query/fc-query.p/fc-query.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,@loader_path/.. -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.1.dylib /usr/local/opt/freetype/lib/libfreetype.dylib

build fc-scan/fc-scan.p/fc-scan.c.o: c_COMPILER ../fc-scan/fc-scan.c || fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h
 DEPFILE = fc-scan/fc-scan.p/fc-scan.c.o.d
 DEPFILE_UNQUOTED = fc-scan/fc-scan.p/fc-scan.c.o.d
 ARGS = -Ifc-scan/fc-scan.p -Ifc-scan -I../fc-scan -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build fc-scan/fc-scan: c_LINKER fc-scan/fc-scan.p/fc-scan.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,@loader_path/.. -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.1.dylib /usr/local/opt/freetype/lib/libfreetype.dylib

build fc-validate/fc-validate.p/fc-validate.c.o: c_COMPILER ../fc-validate/fc-validate.c || fcalias.h fcaliastail.h fcftalias.h fcftaliastail.h fcstdint.h
 DEPFILE = fc-validate/fc-validate.p/fc-validate.c.o.d
 DEPFILE_UNQUOTED = fc-validate/fc-validate.p/fc-validate.c.o.d
 ARGS = -Ifc-validate/fc-validate.p -Ifc-validate -I../fc-validate -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build fc-validate/fc-validate: c_LINKER fc-validate/fc-validate.p/fc-validate.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.1.dylib.p/libfontconfig.1.dylib.symbols
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,@loader_path/.. -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.1.dylib /usr/local/opt/freetype/lib/libfreetype.dylib

build test/testfonts: CUSTOM_COMMAND  | /usr/local/opt/python@3.13/bin/python3.13 PHONY
 COMMAND = /usr/local/opt/python@3.13/bin/python3.13 /Users/<USER>/work/fontconfig/build-aux/fetch-testfonts.py --target-dir ./testfonts --try-symlink
 description = Generating$ test/fetch_test_fonts$ with$ a$ custom$ command

build test/test_bz89617.p/test-bz89617.c.o: c_COMPILER ../test/test-bz89617.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_bz89617.p/test-bz89617.c.o.d
 DEPFILE_UNQUOTED = test/test_bz89617.p/test-bz89617.c.o.d
 ARGS = -Itest/test_bz89617.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H '-DSRCDIR="/Users/<USER>/work/fontconfig/test"'

build test/test_bz89617: c_LINKER test/test_bz89617.p/test-bz89617.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_bz131804.p/test-bz131804.c.o: c_COMPILER ../test/test-bz131804.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_bz131804.p/test-bz131804.c.o.d
 DEPFILE_UNQUOTED = test/test_bz131804.p/test-bz131804.c.o.d
 ARGS = -Itest/test_bz131804.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_bz131804: c_LINKER test/test_bz131804.p/test-bz131804.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_bz96676.p/test-bz96676.c.o: c_COMPILER ../test/test-bz96676.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_bz96676.p/test-bz96676.c.o.d
 DEPFILE_UNQUOTED = test/test_bz96676.p/test-bz96676.c.o.d
 ARGS = -Itest/test_bz96676.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_bz96676: c_LINKER test/test_bz96676.p/test-bz96676.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_name_parse.p/test-name-parse.c.o: c_COMPILER ../test/test-name-parse.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_name_parse.p/test-name-parse.c.o.d
 DEPFILE_UNQUOTED = test/test_name_parse.p/test-name-parse.c.o.d
 ARGS = -Itest/test_name_parse.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_name_parse: c_LINKER test/test_name_parse.p/test-name-parse.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_bz106618.p/test-bz106618.c.o: c_COMPILER ../test/test-bz106618.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_bz106618.p/test-bz106618.c.o.d
 DEPFILE_UNQUOTED = test/test_bz106618.p/test-bz106618.c.o.d
 ARGS = -Itest/test_bz106618.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_bz106618: c_LINKER test/test_bz106618.p/test-bz106618.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_bz1744377.p/test-bz1744377.c.o: c_COMPILER ../test/test-bz1744377.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_bz1744377.p/test-bz1744377.c.o.d
 DEPFILE_UNQUOTED = test/test_bz1744377.p/test-bz1744377.c.o.d
 ARGS = -Itest/test_bz1744377.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_bz1744377: c_LINKER test/test_bz1744377.p/test-bz1744377.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_issue180.p/test-issue180.c.o: c_COMPILER ../test/test-issue180.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_issue180.p/test-issue180.c.o.d
 DEPFILE_UNQUOTED = test/test_issue180.p/test-issue180.c.o.d
 ARGS = -Itest/test_issue180.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_issue180: c_LINKER test/test_issue180.p/test-issue180.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_family_matching.p/test-family-matching.c.o: c_COMPILER ../test/test-family-matching.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_family_matching.p/test-family-matching.c.o.d
 DEPFILE_UNQUOTED = test/test_family_matching.p/test-family-matching.c.o.d
 ARGS = -Itest/test_family_matching.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_family_matching: c_LINKER test/test_family_matching.p/test-family-matching.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_ptrlist.p/test-ptrlist.c.o: c_COMPILER ../test/test-ptrlist.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_ptrlist.p/test-ptrlist.c.o.d
 DEPFILE_UNQUOTED = test/test_ptrlist.p/test-ptrlist.c.o.d
 ARGS = -Itest/test_ptrlist.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_ptrlist: c_LINKER test/test_ptrlist.p/test-ptrlist.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_bz106632.p/test-bz106632.c.o: c_COMPILER ../test/test-bz106632.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_bz106632.p/test-bz106632.c.o.d
 DEPFILE_UNQUOTED = test/test_bz106632.p/test-bz106632.c.o.d
 ARGS = -Itest/test_bz106632.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H '-DFONTFILE="/Users/<USER>/work/fontconfig/test/4x6.pcf"'

build test/test_bz106632: c_LINKER test/test_bz106632.p/test-bz106632.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_issue107.p/test-issue107.c.o: c_COMPILER ../test/test-issue107.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_issue107.p/test-issue107.c.o.d
 DEPFILE_UNQUOTED = test/test_issue107.p/test-issue107.c.o.d
 ARGS = -Itest/test_issue107.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_issue107: c_LINKER test/test_issue107.p/test-issue107.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_crbug1004254.p/test-crbug1004254.c.o: c_COMPILER ../test/test-crbug1004254.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_crbug1004254.p/test-crbug1004254.c.o.d
 DEPFILE_UNQUOTED = test/test_crbug1004254.p/test-crbug1004254.c.o.d
 ARGS = -Itest/test_crbug1004254.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_crbug1004254: c_LINKER test/test_crbug1004254.p/test-crbug1004254.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/test_mt_fccfg.p/test-mt-fccfg.c.o: c_COMPILER ../test/test-mt-fccfg.c || fc-lang/fclang.h fcstdint.h
 DEPFILE = test/test_mt_fccfg.p/test-mt-fccfg.c.o.d
 DEPFILE_UNQUOTED = test/test_mt_fccfg.p/test-mt-fccfg.c.o.d
 ARGS = -Itest/test_mt_fccfg.p -Itest -I../test -I. -I.. -Ifc-lang -I../fc-lang -Isrc -I../src -fdiagnostics-color=always -Wall -Winvalid-pch -std=c11 -O0 -g -DHAVE_CONFIG_H

build test/test_mt_fccfg: c_LINKER test/test_mt_fccfg.p/test-mt-fccfg.c.o | /usr/local/opt/freetype/lib/libfreetype.dylib libfontconfig.a src/libpatternlib_internal.a
 LINK_ARGS = -Wl,-dead_strip_dylibs -Wl,-headerpad_max_install_names -Wl,-rpath,/usr/local/opt/freetype/lib libfontconfig.a src/libpatternlib_internal.a /usr/local/opt/freetype/lib/libfreetype.dylib -lexpat -lm -lexpat

build test/out.expected: CUSTOM_COMMAND ../test/out.expected-no-long-family-names
 COMMAND = /usr/local/bin/meson --internal copy ../test/out.expected-no-long-family-names test/out.expected
 description = Copying$ file$ test/out.expected

build conf.d/35-lang-normalize.conf: CUSTOM_COMMAND  | /Users/<USER>/work/fontconfig/conf.d/write-35-lang-normalize-conf.py
 COMMAND = /Users/<USER>/work/fontconfig/conf.d/write-35-lang-normalize-conf.py aa,ab,af,am,ar,as,ast,av,ay,ba,bm,be,bg,bh,bho,bi,bin,bn,bo,br,bs,bua,ca,ce,ch,chm,chr,co,cs,cu,cv,cy,da,de,dz,el,en,eo,es,et,eu,fa,fi,fj,fo,fr,ff,fur,fy,ga,gd,gez,gl,gn,gu,gv,ha,haw,he,hi,ho,hr,hu,hy,ia,ig,id,ie,ik,io,is,it,iu,ja,ka,kaa,ki,kk,kl,km,kn,ko,kok,ks,kum,kv,kw,ky,la,lb,lez,ln,lo,lt,lv,mg,mh,mi,mk,ml,mo,mr,mt,my,nb,nds,ne,nl,nn,no,nr,nso,ny,oc,om,or,os,pa,pl,pt,rm,ro,ru,sa,sah,sco,se,sel,sh,shs,si,sk,sl,sm,sma,smj,smn,sms,so,sq,sr,ss,st,sv,sw,syr,ta,te,tg,th,tig,tk,tl,tn,to,tr,ts,tt,tw,tyv,ug,uk,ur,uz,ve,vi,vo,vot,wa,wen,wo,xh,yap,yi,yo,zu,ak,an,byn,crh,csb,dv,ee,fat,fil,hne,hsb,ht,hz,ii,jv,kab,kj,kr,kwm,lg,li,mai,ms,na,ng,nv,ota,qu,quz,rn,rw,sc,sd,sg,sid,sn,su,ty,wal,za,lah,nqo,brx,sat,doi,mni,anp,bhb,hif,mag,raj,the,agr,ayc,bem,ckb,cmn,dsb,hak,lij,lzh,mfe,mhr,miq,mjw,mnw,nan,nhn,niu,rif,sgs,shn,szl,tcy,tpi,unm,wae,yue,yuw,got,cop conf.d/35-lang-normalize.conf
 description = Generating$ conf.d/35-lang-normalize.conf$ with$ a$ custom$ command

build doc/fontconfig-devel.sgml: CUSTOM_COMMAND ../doc/fontconfig-devel.sgml
 COMMAND = /usr/local/bin/meson --internal copy ../doc/fontconfig-devel.sgml doc/fontconfig-devel.sgml
 description = Copying$ file$ doc/fontconfig-devel.sgml

build doc/fontconfig-user.sgml: CUSTOM_COMMAND ../doc/fontconfig-user.sgml
 COMMAND = /usr/local/bin/meson --internal copy ../doc/fontconfig-user.sgml doc/fontconfig-user.sgml
 description = Copying$ file$ doc/fontconfig-user.sgml

build doc/fcatomic.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcatomic.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcatomic.fncs doc/fcatomic.sgml
 description = Generating$ doc/fcatomic.sgml$ with$ a$ custom$ command

build doc/fcblanks.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcblanks.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcblanks.fncs doc/fcblanks.sgml
 description = Generating$ doc/fcblanks.sgml$ with$ a$ custom$ command

build doc/fccache.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fccache.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fccache.fncs doc/fccache.sgml
 description = Generating$ doc/fccache.sgml$ with$ a$ custom$ command

build doc/fccharset.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fccharset.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fccharset.fncs doc/fccharset.sgml
 description = Generating$ doc/fccharset.sgml$ with$ a$ custom$ command

build doc/fcconfig.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcconfig.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcconfig.fncs doc/fcconfig.sgml
 description = Generating$ doc/fcconfig.sgml$ with$ a$ custom$ command

build doc/fcconstant.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcconstant.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcconstant.fncs doc/fcconstant.sgml
 description = Generating$ doc/fcconstant.sgml$ with$ a$ custom$ command

build doc/fcdircache.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcdircache.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcdircache.fncs doc/fcdircache.sgml
 description = Generating$ doc/fcdircache.sgml$ with$ a$ custom$ command

build doc/fcfile.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcfile.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcfile.fncs doc/fcfile.sgml
 description = Generating$ doc/fcfile.sgml$ with$ a$ custom$ command

build doc/fcfontset.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcfontset.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcfontset.fncs doc/fcfontset.sgml
 description = Generating$ doc/fcfontset.sgml$ with$ a$ custom$ command

build doc/fcfontations.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcfontations.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcfontations.fncs doc/fcfontations.sgml
 description = Generating$ doc/fcfontations.sgml$ with$ a$ custom$ command

build doc/fcformat.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcformat.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcformat.fncs doc/fcformat.sgml
 description = Generating$ doc/fcformat.sgml$ with$ a$ custom$ command

build doc/fcfreetype.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcfreetype.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcfreetype.fncs doc/fcfreetype.sgml
 description = Generating$ doc/fcfreetype.sgml$ with$ a$ custom$ command

build doc/fcinit.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcinit.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcinit.fncs doc/fcinit.sgml
 description = Generating$ doc/fcinit.sgml$ with$ a$ custom$ command

build doc/fclangset.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fclangset.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fclangset.fncs doc/fclangset.sgml
 description = Generating$ doc/fclangset.sgml$ with$ a$ custom$ command

build doc/fcmatrix.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcmatrix.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcmatrix.fncs doc/fcmatrix.sgml
 description = Generating$ doc/fcmatrix.sgml$ with$ a$ custom$ command

build doc/fcobjectset.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcobjectset.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcobjectset.fncs doc/fcobjectset.sgml
 description = Generating$ doc/fcobjectset.sgml$ with$ a$ custom$ command

build doc/fcobjecttype.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcobjecttype.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcobjecttype.fncs doc/fcobjecttype.sgml
 description = Generating$ doc/fcobjecttype.sgml$ with$ a$ custom$ command

build doc/fcpattern.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcpattern.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcpattern.fncs doc/fcpattern.sgml
 description = Generating$ doc/fcpattern.sgml$ with$ a$ custom$ command

build doc/fcrange.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcrange.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcrange.fncs doc/fcrange.sgml
 description = Generating$ doc/fcrange.sgml$ with$ a$ custom$ command

build doc/fcstring.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcstring.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcstring.fncs doc/fcstring.sgml
 description = Generating$ doc/fcstring.sgml$ with$ a$ custom$ command

build doc/fcstrset.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcstrset.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcstrset.fncs doc/fcstrset.sgml
 description = Generating$ doc/fcstrset.sgml$ with$ a$ custom$ command

build doc/fcvalue.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcvalue.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcvalue.fncs doc/fcvalue.sgml
 description = Generating$ doc/fcvalue.sgml$ with$ a$ custom$ command

build doc/fcweight.sgml: CUSTOM_COMMAND ../doc/func.sgml ../doc/fcweight.fncs | /Users/<USER>/work/fontconfig/doc/edit-sgml.py
 COMMAND = /Users/<USER>/work/fontconfig/doc/edit-sgml.py ../doc/func.sgml ../doc/fcweight.fncs doc/fcweight.sgml
 description = Generating$ doc/fcweight.sgml$ with$ a$ custom$ command

# Test rules

build test: phony meson-internal__test

build meson-internal__test: CUSTOM_COMMAND all meson-test-prereq PHONY
 COMMAND = /usr/local/bin/meson test --no-rebuild --print-errorlogs
 DESC = Running$ all$ tests
 pool = console

build benchmark: phony meson-internal__benchmark

build meson-internal__benchmark: CUSTOM_COMMAND all meson-benchmark-prereq PHONY
 COMMAND = /usr/local/bin/meson test --benchmark --logbase benchmarklog --num-processes=1 --no-rebuild
 DESC = Running$ benchmark$ suite
 pool = console

# Install rules

build install: phony meson-internal__install

build meson-internal__install: CUSTOM_COMMAND PHONY | all
 DESC = Installing$ files
 COMMAND = /usr/local/bin/meson install --no-rebuild
 pool = console

build dist: phony meson-internal__dist

build meson-internal__dist: CUSTOM_COMMAND PHONY
 DESC = Creating$ source$ packages
 COMMAND = /usr/local/bin/meson dist
 pool = console

# Suffix

build ctags: phony meson-internal__ctags

build meson-internal__ctags: CUSTOM_COMMAND PHONY
 COMMAND = /usr/local/bin/meson --internal tags ctags /Users/<USER>/work/fontconfig
 pool = console

build uninstall: phony meson-internal__uninstall

build meson-internal__uninstall: CUSTOM_COMMAND PHONY
 COMMAND = /usr/local/bin/meson --internal uninstall
 pool = console

build all: phony fcstdint.h fc-lang/fclang.h src/libpatternlib_internal.a src/fcobjshash.gperf libfontconfig.1.dylib libfontconfig.a fc-cache/fc-cache fc-cat/fc-cat fc-conflist/fc-conflist fc-list/fc-list fc-match/fc-match fc-pattern/fc-pattern fc-query/fc-query fc-scan/fc-scan fc-validate/fc-validate test/test_bz89617 test/test_bz131804 test/test_bz96676 test/test_name_parse test/test_bz106618 test/test_bz1744377 test/test_issue180 test/test_family_matching test/test_ptrlist test/test_bz106632 test/test_issue107 test/test_crbug1004254 test/test_mt_fccfg test/out.expected conf.d/35-lang-normalize.conf doc/fontconfig-devel.sgml doc/fontconfig-user.sgml

build meson-test-prereq: phony test/test_bz89617 test/test_bz131804 test/test_bz96676 test/test_name_parse test/test_bz106618 test/test_bz1744377 test/test_issue180 test/test_family_matching test/test_ptrlist test/test_bz106632 test/test_issue107 test/test_crbug1004254 test/test_mt_fccfg

build meson-benchmark-prereq: phony 

build clean: phony meson-internal__clean

build clean-ctlist: phony meson-internal__clean-ctlist

build meson-internal__clean-ctlist: CUSTOM_COMMAND PHONY
 COMMAND = /usr/local/bin/meson --internal cleantrees /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cleantrees.dat
 description = Cleaning$ custom$ target$ directories

build meson-internal__clean: CUSTOM_COMMAND PHONY | clean-ctlist
 COMMAND = /usr/local/bin/ninja -t clean
 description = Cleaning

build build.ninja: REGENERATE_BUILD ../meson.build /Users/<USER>/work/fontconfig/meson.options ../meson-cc-tests/freetype-pcf-long-family-names.c ../subprojects/libintl/meson.build ../meson-cc-tests/flexible-array-member-test.c ../meson-cc-tests/pthread-prio-inherit-test.c ../meson-cc-tests/stdatomic-primitives-test.c ../meson-cc-tests/intel-atomic-primitives-test.c ../meson-cc-tests/solaris-atomic-operations.c /usr/bin/gperf ../meson-cc-tests/gperf.txt ../fontconfig/meson.build ../fontconfig/fontconfig.h.in ../fc-case/meson.build ../fc-lang/meson.build ../src/meson.build ../fc-cache/meson.build ../fc-cat/meson.build ../fc-conflist/meson.build ../fc-list/meson.build ../fc-match/meson.build ../fc-pattern/meson.build ../fc-query/meson.build ../fc-scan/meson.build ../fc-validate/meson.build ../test/meson.build ../conf.d/meson.build ../conf.d/README.in ../its/meson.build ../doc/meson.build ../doc/extract-man-list.py ../doc/fcatomic.fncs ../doc/fcblanks.fncs ../doc/fccache.fncs ../doc/fccharset.fncs ../doc/fcconfig.fncs ../doc/fcconstant.fncs ../doc/fcdircache.fncs ../doc/fcfile.fncs ../doc/fcfontset.fncs ../doc/fcfontations.fncs ../doc/fcformat.fncs ../doc/fcfreetype.fncs ../doc/fcinit.fncs ../doc/fclangset.fncs ../doc/fcmatrix.fncs ../doc/fcobjectset.fncs ../doc/fcobjecttype.fncs ../doc/fcpattern.fncs ../doc/fcrange.fncs ../doc/fcstring.fncs ../doc/fcstrset.fncs ../doc/fcvalue.fncs ../doc/fcweight.fncs ../doc/cache-version.sgml.in ../doc/version.sgml.in ../doc/confdir.sgml.in ../meson-config.h.in ../fonts.conf.in meson-private/coredata.dat
 pool = console

build meson-implicit-outs: phony libfontconfig.dylib

build reconfigure: REGENERATE_BUILD PHONY
 pool = console

build ../meson.build /Users/<USER>/work/fontconfig/meson.options ../meson-cc-tests/freetype-pcf-long-family-names.c ../subprojects/libintl/meson.build ../meson-cc-tests/flexible-array-member-test.c ../meson-cc-tests/pthread-prio-inherit-test.c ../meson-cc-tests/stdatomic-primitives-test.c ../meson-cc-tests/intel-atomic-primitives-test.c ../meson-cc-tests/solaris-atomic-operations.c /usr/bin/gperf ../meson-cc-tests/gperf.txt ../fontconfig/meson.build ../fontconfig/fontconfig.h.in ../fc-case/meson.build ../fc-lang/meson.build ../src/meson.build ../fc-cache/meson.build ../fc-cat/meson.build ../fc-conflist/meson.build ../fc-list/meson.build ../fc-match/meson.build ../fc-pattern/meson.build ../fc-query/meson.build ../fc-scan/meson.build ../fc-validate/meson.build ../test/meson.build ../conf.d/meson.build ../conf.d/README.in ../its/meson.build ../doc/meson.build ../doc/extract-man-list.py ../doc/fcatomic.fncs ../doc/fcblanks.fncs ../doc/fccache.fncs ../doc/fccharset.fncs ../doc/fcconfig.fncs ../doc/fcconstant.fncs ../doc/fcdircache.fncs ../doc/fcfile.fncs ../doc/fcfontset.fncs ../doc/fcfontations.fncs ../doc/fcformat.fncs ../doc/fcfreetype.fncs ../doc/fcinit.fncs ../doc/fclangset.fncs ../doc/fcmatrix.fncs ../doc/fcobjectset.fncs ../doc/fcobjecttype.fncs ../doc/fcpattern.fncs ../doc/fcrange.fncs ../doc/fcstring.fncs ../doc/fcstrset.fncs ../doc/fcvalue.fncs ../doc/fcweight.fncs ../doc/cache-version.sgml.in ../doc/version.sgml.in ../doc/confdir.sgml.in ../meson-config.h.in ../fonts.conf.in meson-private/coredata.dat: phony 

default all

