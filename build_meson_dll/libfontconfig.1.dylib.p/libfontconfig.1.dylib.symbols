         name @rpath/libfontconfig.1.dylib (offset 24)
compatibility version 17.0.0
_FcAlignSize T
_FcAtomicCreate T
_FcAtomicDeleteNew T
_FcAtomicDestroy T
_FcAtomicLock T
_FcAtomicNewFile T
_FcAtomicOrigFile T
_FcAtomicReplaceOrig T
_FcAtomicUnlock T
_FcBlanksAdd T
_FcBlanksCreate T
_FcBlanksDestroy T
_FcBlanksIsMember T
_FcCacheAllocate T
_FcCacheCopySet T
_FcCacheCreateTagFile T
_FcCacheDir T
_FcCacheFini T
_FcCacheNumFont T
_FcCacheNumSubdir T
_FcCacheObjectDereference T
_FcCacheObjectReference T
_FcCacheSubdir T
_FcCharSetAddChar T
_FcCharSetCopy T
_FcCharSetCount T
_FcCharSetCoverage T
_FcCharSetCreate T
_FcCharSetDelChar T
_FcCharSetDestroy T
_FcCharSetEqual T
_FcCharSetFindLeafCreate T
_FcCharSetFirstPage T
_FcCharSetFreeze T
_FcCharSetFreezerCreate T
_FcCharSetFreezerDestroy T
_FcCharSetHasChar T
_FcCharSetIntersect T
_FcCharSetIntersectCount T
_FcCharSetIsSubset T
_FcCharSetMerge T
_FcCharSetNew T
_FcCharSetNextPage T
_FcCharSetPrint T
_FcCharSetPromote T
_FcCharSetSerialize T
_FcCharSetSerializeAlloc T
_FcCharSetSubtract T
_FcCharSetSubtractCount T
_FcCharSetUnion T
_FcConfigAcceptFilename T
_FcConfigAcceptFilter T
_FcConfigAcceptFont T
_FcConfigAddBlank T
_FcConfigAddCache T
_FcConfigAddCacheDir T
_FcConfigAddConfigDir T
_FcConfigAddConfigFile T
_FcConfigAddFontDir T
_FcConfigAddRule T
_FcConfigAllocExpr T
_FcConfigAppFontAddDir T
_FcConfigAppFontAddFile T
_FcConfigAppFontClear T
_FcConfigBuildFonts T
_FcConfigCompareValue T
_FcConfigCreate T
_FcConfigDestroy T
_FcConfigEnableHome T
_FcConfigFileInfoIterGet T
_FcConfigFileInfoIterInit T
_FcConfigFileInfoIterNext T
_FcConfigFilename T
_FcConfigFini T
_FcConfigGetBlanks T
_FcConfigGetCache T
_FcConfigGetCacheDirs T
_FcConfigGetConfigDirs T
_FcConfigGetConfigFiles T
_FcConfigGetCurrent T
_FcConfigGetDefaultLang T
_FcConfigGetDefaultLangs T
_FcConfigGetDesktopName T
_FcConfigGetFilename T
_FcConfigGetFontDirs T
_FcConfigGetFonts T
_FcConfigGetPrgname T
_FcConfigGetRescanInterval T
_FcConfigGetRescanInverval T
_FcConfigGetSysRoot T
_FcConfigGlobAdd T
_FcConfigHome T
_FcConfigInit T
_FcConfigMapFontPath T
_FcConfigMapSalt T
_FcConfigParseAndLoad T
_FcConfigParseAndLoadFromMemory T
_FcConfigParseOnly T
_FcConfigPatternsAdd T
_FcConfigPreferAppFont T
_FcConfigRealFilename T
_FcConfigReference T
_FcConfigResetFontDirs T
_FcConfigSetCurrent T
_FcConfigSetDefaultSubstitute T
_FcConfigSetFontSetFilter T
_FcConfigSetFonts T
_FcConfigSetRescanInterval T
_FcConfigSetRescanInverval T
_FcConfigSetSysRoot T
_FcConfigSubstitute T
_FcConfigSubstituteWithPat T
_FcConfigUptoDate T
_FcConfigXdgCacheHome T
_FcConfigXdgConfigHome T
_FcConfigXdgDataDirs T
_FcConfigXdgDataHome T
_FcDebugVal S
_FcDefaultSubstitute T
_FcDirCacheBuild T
_FcDirCacheClean T
_FcDirCacheCreateTagFile T
_FcDirCacheCreateUUID T
_FcDirCacheDeleteUUID T
_FcDirCacheLoad T
_FcDirCacheLoadFile T
_FcDirCacheLock T
_FcDirCacheRead T
_FcDirCacheRebuild T
_FcDirCacheReference T
_FcDirCacheRescan T
_FcDirCacheScan T
_FcDirCacheUnlink T
_FcDirCacheUnload T
_FcDirCacheUnlock T
_FcDirCacheValid T
_FcDirCacheWrite T
_FcDirSave T
_FcDirScan T
_FcDirScanConfig T
_FcEditDestroy T
_FcEditPrint T
_FcExprPrint T
_FcFileIsDir T
_FcFileIsFile T
_FcFileIsLink T
_FcFileScan T
_FcFileScanConfig T
_FcFini T
_FcFontList T
_FcFontMatch T
_FcFontRenderPrepare T
_FcFontSetAdd T
_FcFontSetCreate T
_FcFontSetDeserialize T
_FcFontSetDestroy T
_FcFontSetList T
_FcFontSetMatch T
_FcFontSetPrint T
_FcFontSetSerialize T
_FcFontSetSerializeAlloc T
_FcFontSetSort T
_FcFontSetSortDestroy T
_FcFontSort T
_FcFreeTypeCharIndex T
_FcFreeTypeCharSet T
_FcFreeTypeCharSetAndSpacing T
_FcFreeTypeQuery T
_FcFreeTypeQueryAll T
_FcFreeTypeQueryFace T
_FcGetDefaultLang T
_FcGetDefaultLangs T
_FcGetDesktopName T
_FcGetLangs T
_FcGetPrgname T
_FcGetVersion T
_FcHashStrCopy T
_FcHashTableAdd T
_FcHashTableCreate T
_FcHashTableDestroy T
_FcHashTableFind T
_FcHashTableRemove T
_FcHashTableReplace T
_FcIdentityMatrix S
_FcInit T
_FcInitBringUptoDate T
_FcInitDebug T
_FcInitLoadConfig T
_FcInitLoadConfigAndFonts T
_FcInitLoadOwnConfig T
_FcInitLoadOwnConfigAndFonts T
_FcInitReinitialize T
_FcIsFsMmapSafe T
_FcIsFsMtimeBroken T
_FcLangCompare T
_FcLangGetCharSet T
_FcLangIsExclusive T
_FcLangIsExclusiveFromOs2 T
_FcLangNormalize T
_FcLangSetAdd T
_FcLangSetCompare T
_FcLangSetContains T
_FcLangSetCopy T
_FcLangSetCreate T
_FcLangSetDel T
_FcLangSetDestroy T
_FcLangSetEqual T
_FcLangSetFromCharSet T
_FcLangSetGetLangs T
_FcLangSetHasLang T
_FcLangSetHash T
_FcLangSetPrint T
_FcLangSetPromote T
_FcLangSetSerialize T
_FcLangSetSerializeAlloc T
_FcLangSetSubtract T
_FcLangSetUnion T
_FcListPatternMatchAny T
_FcMakeDirectory T
_FcMakeTempfile T
_FcMatrixCopy T
_FcMatrixEqual T
_FcMatrixFree T
_FcMatrixMultiply T
_FcMatrixRotate T
_FcMatrixScale T
_FcMatrixShear T
_FcNameBool T
_FcNameConstant T
_FcNameConstantWithObjectCheck T
_FcNameGetConstant T
_FcNameGetConstantFor T
_FcNameGetObjectType T
_FcNameParse T
_FcNameParseCharSet T
_FcNameParseLangSet T
_FcNameRegisterConstants T
_FcNameRegisterObjectTypes T
_FcNameUnparse T
_FcNameUnparseCharSet T
_FcNameUnparseEscaped T
_FcNameUnparseLangSet T
_FcNameUnparseValue T
_FcNameUnparseValueList T
_FcNameUnregisterConstants T
_FcNameUnregisterObjectTypes T
_FcObjectFini T
_FcObjectFromName T
_FcObjectGetSet T
_FcObjectInit T
_FcObjectLookupBuiltinIdByName T
_FcObjectLookupIdByName T
_FcObjectLookupOtherNameById T
_FcObjectLookupOtherTypeById T
_FcObjectLookupOtherTypeByName T
_FcObjectName T
_FcObjectSetAdd T
_FcObjectSetBuild T
_FcObjectSetCreate T
_FcObjectSetDestroy T
_FcObjectSetVaBuild T
_FcObjectValidType T
_FcOpPrint T
_FcOpen T
_FcPatternAdd T
_FcPatternAddBool T
_FcPatternAddCharSet T
_FcPatternAddDouble T
_FcPatternAddFTFace T
_FcPatternAddInteger T
_FcPatternAddLangSet T
_FcPatternAddMatrix T
_FcPatternAddRange T
_FcPatternAddString T
_FcPatternAddWeak T
_FcPatternAppend T
_FcPatternBuild T
_FcPatternCacheRewriteFile T
_FcPatternCreate T
_FcPatternDel T
_FcPatternDestroy T
_FcPatternDuplicate T
_FcPatternEqual T
_FcPatternEqualSubset T
_FcPatternFilter T
_FcPatternFindIter T
_FcPatternFindObjectIter T
_FcPatternFormat T
_FcPatternGet T
_FcPatternGetBool T
_FcPatternGetCharSet T
_FcPatternGetDouble T
_FcPatternGetFTFace T
_FcPatternGetInteger T
_FcPatternGetLangSet T
_FcPatternGetMatrix T
_FcPatternGetRange T
_FcPatternGetString T
_FcPatternGetWithBinding T
_FcPatternHash T
_FcPatternIterEqual T
_FcPatternIterGetObject T
_FcPatternIterGetObjectId T
_FcPatternIterGetValue T
_FcPatternIterGetValues T
_FcPatternIterIsValid T
_FcPatternIterNext T
_FcPatternIterStart T
_FcPatternIterValueCount T
_FcPatternObjectAdd T
_FcPatternObjectAddBool T
_FcPatternObjectAddCharSet T
_FcPatternObjectAddDouble T
_FcPatternObjectAddInteger T
_FcPatternObjectAddLangSet T
_FcPatternObjectAddRange T
_FcPatternObjectAddString T
_FcPatternObjectAddWithBinding T
_FcPatternObjectCount T
_FcPatternObjectDel T
_FcPatternObjectFindElt T
_FcPatternObjectGet T
_FcPatternObjectGetBool T
_FcPatternObjectGetCharSet T
_FcPatternObjectGetDouble T
_FcPatternObjectGetInteger T
_FcPatternObjectGetLangSet T
_FcPatternObjectGetRange T
_FcPatternObjectGetString T
_FcPatternObjectGetWithBinding T
_FcPatternObjectInsertElt T
_FcPatternObjectListAdd T
_FcPatternPosition T
_FcPatternPrint T
_FcPatternPrint2 T
_FcPatternReference T
_FcPatternRemove T
_FcPatternSerialize T
_FcPatternSerializeAlloc T
_FcPatternVaBuild T
_FcPtrListCreate T
_FcPtrListDestroy T
_FcPtrListIterAdd T
_FcPtrListIterGetValue T
_FcPtrListIterInit T
_FcPtrListIterInitAtLast T
_FcPtrListIterIsValid T
_FcPtrListIterNext T
_FcPtrListIterRemove T
_FcRandom T
_FcRangeCompare T
_FcRangeCopy T
_FcRangeCreateDouble T
_FcRangeCreateInteger T
_FcRangeDestroy T
_FcRangeGetDouble T
_FcRangeHash T
_FcRangeIsInRange T
_FcRangePromote T
_FcRangeSerialize T
_FcRangeSerializeAlloc T
_FcReadLink T
_FcRuleDestroy T
_FcRulePrint T
_FcRuleSetAdd T
_FcRuleSetAddDescription T
_FcRuleSetCreate T
_FcRuleSetDestroy T
_FcRuleSetEnable T
_FcRuleSetReference T
_FcScandir T
_FcSerializeAlloc T
_FcSerializeCreate T
_FcSerializeDestroy T
_FcSerializeOffset T
_FcSerializePtr T
_FcSerializeReserve T
_FcStat T
_FcStatChecksum T
_FcStrBasename T
_FcStrBufChar T
_FcStrBufData T
_FcStrBufDestroy T
_FcStrBufDone T
_FcStrBufDoneStatic T
_FcStrBufInit T
_FcStrBufString T
_FcStrBuildFilename T
_FcStrCanonFilename T
_FcStrCmp T
_FcStrCmpIgnoreBlanksAndCase T
_FcStrCmpIgnoreCase T
_FcStrContainsIgnoreBlanksAndCase T
_FcStrContainsIgnoreCase T
_FcStrContainsWord T
_FcStrCopy T
_FcStrCopyFilename T
_FcStrDirname T
_FcStrDowncase T
_FcStrFree T
_FcStrGlobMatch T
_FcStrHashIgnoreBlanksAndCase T
_FcStrHashIgnoreCase T
_FcStrIsAbsoluteFilename T
_FcStrLastSlash T
_FcStrListCreate T
_FcStrListDone T
_FcStrListFirst T
_FcStrListNext T
_FcStrMatchIgnoreCaseAndDelims T
_FcStrPlus T
_FcStrRealPath T
_FcStrSerialize T
_FcStrSerializeAlloc T
_FcStrSetAdd T
_FcStrSetAddFilename T
_FcStrSetAddFilenamePairWithSalt T
_FcStrSetAddLangs T
_FcStrSetAddTriple T
_FcStrSetCreate T
_FcStrSetCreateEx T
_FcStrSetDel T
_FcStrSetDeleteAll T
_FcStrSetDestroy T
_FcStrSetEqual T
_FcStrSetInsert T
_FcStrSetMember T
_FcStrSetMemberAB T
_FcStrStr T
_FcStrStrIgnoreCase T
_FcStrTripleSecond T
_FcStrTripleThird T
_FcStrUsesHome T
_FcStringHash T
_FcTestDestroy T
_FcTestPrint T
_FcUcs4ToUtf8 T
_FcUtf16Len T
_FcUtf16ToUcs4 T
_FcUtf8Len T
_FcUtf8ToUcs4 T
_FcValueCanonicalize T
_FcValueDestroy T
_FcValueEqual T
_FcValueListAppend T
_FcValueListCreate T
_FcValueListDestroy T
_FcValueListDuplicate T
_FcValueListPrepend T
_FcValueListPrint T
_FcValueListPrintWithPosition T
_FcValueListSerialize T
_FcValueListSerializeAlloc T
_FcValuePrint T
_FcValuePrintFile T
_FcValuePrintWithPosition T
_FcValueSave T
_FcWeightFromOpenType T
_FcWeightFromOpenTypeDouble T
_FcWeightToOpenType T
_FcWeightToOpenTypeDouble T
_default_langs S
_ftglue_face_goto_table T
_ftglue_stream_frame_enter T
_ftglue_stream_frame_exit T
_ftglue_stream_pos T
_ftglue_stream_seek T
_other_types S

