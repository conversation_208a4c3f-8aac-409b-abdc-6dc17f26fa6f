Build started at 2025-07-23T08:38:13.885499
Main binary: /usr/local/opt/python@3.13/bin/python3.13
Build Options: -Dbuildtype=debug -Dbuildtype=debug
Python system: Darwin
The Meson build system
Version: 1.8.0
Source dir: /Users/<USER>/work/fontconfig
Build dir: /Users/<USER>/work/fontconfig/build_meson_dll
Build type: native build
Project name: fontconfig
Project version: 2.17.1
C compiler for the host machine: cc (clang 14.0.0 "Apple clang version 14.0.0 (clang-1400.0.29.202)")
C linker for the host machine: cc ld64 820.1
-----------
Detecting archiver via: `llvm-ar-14 --version` -> [Errno 2] No such file or directory: 'llvm-ar-14'
-----------
Detecting archiver via: `llvm-ar --version` -> [Errno 2] No such file or directory: 'llvm-ar'
-----------
Detecting archiver via: `ar --version` -> 1
stderr:
usage:  ar -d [-TLsv] archive file ...
	ar -m [-TLsv] archive file ...
	ar -m [-abiTLsv] position archive file ...
	ar -p [-TLsv] archive [file ...]
	ar -q [-cTLsv] archive file ...
	ar -r [-cuTLsv] archive file ...
	ar -r [-abciuTLsv] position archive file ...
	ar -t [-TLsv] archive [file ...]
	ar -x [-ouTLsv] archive [file ...]
-----------
C compiler for the build machine: cc (clang 14.0.0 "Apple clang version 14.0.0 (clang-1400.0.29.202)")
C linker for the build machine: cc ld64 820.1
-----------
Detecting archiver via: `llvm-ar-14 --version` -> [Errno 2] No such file or directory: 'llvm-ar-14'
-----------
Detecting archiver via: `llvm-ar --version` -> [Errno 2] No such file or directory: 'llvm-ar'
-----------
Detecting archiver via: `ar --version` -> 1
stderr:
usage:  ar -d [-TLsv] archive file ...
	ar -m [-TLsv] archive file ...
	ar -m [-abiTLsv] position archive file ...
	ar -p [-TLsv] archive [file ...]
	ar -q [-cTLsv] archive file ...
	ar -r [-cuTLsv] archive file ...
	ar -r [-abciuTLsv] position archive file ...
	ar -t [-TLsv] archive [file ...]
	ar -x [-ouTLsv] archive [file ...]
-----------
Build machine cpu family: x86_64
Build machine cpu: x86_64
Host machine cpu family: x86_64
Host machine cpu: x86_64
Target machine cpu family: x86_64
Target machine cpu: x86_64
Running compile:
Working directory:  /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpmpmfdiye
Code:
int main(void) { return 0; }

-----------
Command line: `cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpmpmfdiye/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpmpmfdiye/output.exe -O0 -Werror=implicit-function-declaration -lm -Wl,-undefined,dynamic_lookup` -> 0
Library m found: YES
Dependency freetype2 found: YES 26.2.20 (cached)
Running compile:
Working directory:  /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmppqv0wont
Source file: /Users/<USER>/work/fontconfig/meson-cc-tests/freetype-pcf-long-family-names.c
-----------
Command line: `cc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 /Users/<USER>/work/fontconfig/meson-cc-tests/freetype-pcf-long-family-names.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmppqv0wont/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11` -> 1
stderr:
/Users/<USER>/work/fontconfig/meson-cc-tests/freetype-pcf-long-family-names.c:4:4: error: "No pcf long family names support"
#  error "No pcf long family names support"
   ^
1 error generated.
-----------
Pkg-config binary missing from cross or native file, or env var undefined.
Trying a default Pkg-config fallback at pkg-config
Found pkg-config: YES (/usr/local/bin/pkg-config) 2.4.3
Determining dependency 'json-c' with pkg-config executable '/usr/local/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/local/bin/pkg-config
-----------
Called: `/usr/local/bin/pkg-config --modversion json-c` -> 1
stderr:
Package json-c was not found in the pkg-config search path.
Perhaps you should add the directory containing `json-c.pc'
to the PKG_CONFIG_PATH environment variable
Package 'json-c' not found
-----------
Finding framework path by running:  cc -v -E - 

Looking for framework json-c in /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks
CMake binary for host machine is not cached
CMake binary missing from cross or native file, or env var undefined.
Trying a default CMake fallback at cmake
Found CMake: /usr/local/bin/cmake (4.0.2)
Extracting basic cmake information
Try CMake generator: auto
Calling CMake (['/usr/local/bin/cmake']) in /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c with:
  - "--trace-expand"
  - "--trace-format=json-v1"
  - "--no-warn-unused-cli"
  - "--trace-redirect=cmake_trace.txt"
  - "-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/cmake_json-c/CMakeMesonToolchainFile.cmake"
  - "."
  -- Module search paths:    ['/', '/Applications', '/Applications/Xcode.app/Contents/Applications', '/Applications/Xcode.app/Contents/Developer/Applications', '/Applications/Xcode.app/Contents/Developer/Library/Frameworks', '/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks', '/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks', '/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr', '/Library/Frameworks', '/System/Library/Frameworks', '/opt', '/usr', '/usr/X11R6', '/usr/local']
  -- CMake root:             /usr/local/share/cmake
  -- CMake architectures:    []
  -- CMake lib search paths: ['lib', 'lib32', 'lib64', 'libx32', 'share', '']
Preliminary CMake check failed. Aborting.
Run-time dependency json-c found: NO (tried pkgconfig, framework and cmake)
Dependency expat found: YES 2.4.1 (cached)
Program python3 found: YES (/usr/local/opt/python@3.13/bin/python3.13)
Program pytest found: NO
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpq6cr6qel/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpq6cr6qel/output.exe -O0 -Werror=implicit-function-declaration 

Code:
 #include <libintl.h>

int main() {
    gettext("Hello world");
}
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpq6cr6qel/testfile.c:1:10: fatal error: 'libintl.h' file not found
#include <libintl.h>
         ^~~~~~~~~~~
1 error generated.

Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpjwxznwwx/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration 

Code:
 
        #ifdef __has_include
         #if !__has_include("libintl.h")
          #error "Header 'libintl.h' could not be found"
         #endif
        #else
         #include <libintl.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpjwxznwwx/testfile.c:4:12: error: "Header 'libintl.h' could not be found"
          #error "Header 'libintl.h' could not be found"
           ^
1 error generated.

Running compile:
Working directory:  /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpepz_q82e
Code:
int main(void) { return 0; }

-----------
Command line: `cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpepz_q82e/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpepz_q82e/output.exe -O0 -Werror=implicit-function-declaration -lintl -Wl,-undefined,dynamic_lookup` -> 0
Run-time dependency intl found: NO (tried builtin and system)
Looking for a fallback subproject for the dependency intl

Executing subproject libintl 

Project name: libintl
Project version: undefined
C compiler for the host machine: cc (clang 14.0.0 "Apple clang version 14.0.0 (clang-1400.0.29.202)")
C linker for the host machine: cc ld64 820.1
C compiler for the build machine: cc (clang 14.0.0 "Apple clang version 14.0.0 (clang-1400.0.29.202)")
C linker for the build machine: cc ld64 820.1
Using cached compile:
Cached command line:  cc -I/usr/local/include /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpjqhs9xz6/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("libintl.h")
          #error "Header 'libintl.h' could not be found"
         #endif
        #else
         #include <libintl.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpjqhs9xz6/testfile.c:4:12: error: "Header 'libintl.h' could not be found"
          #error "Header 'libintl.h' could not be found"
           ^
1 error generated.

Has header "libintl.h" : NO (cached)
Build targets in project: 0
Subproject libintl finished.

Dependency intl from subproject subprojects/libintl found: NO
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmplso536p_/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("dirent.h")
          #error "Header 'dirent.h' could not be found"
         #endif
        #else
         #include <dirent.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "dirent.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpywksi0f7/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("dlfcn.h")
          #error "Header 'dlfcn.h' could not be found"
         #endif
        #else
         #include <dlfcn.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "dlfcn.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmphgt64ogl/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("fcntl.h")
          #error "Header 'fcntl.h' could not be found"
         #endif
        #else
         #include <fcntl.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "fcntl.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmph3ounxud/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("inttypes.h")
          #error "Header 'inttypes.h' could not be found"
         #endif
        #else
         #include <inttypes.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "inttypes.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp72pcsfvn/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("stdint.h")
          #error "Header 'stdint.h' could not be found"
         #endif
        #else
         #include <stdint.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "stdint.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmprhnp_rlz/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("stdio.h")
          #error "Header 'stdio.h' could not be found"
         #endif
        #else
         #include <stdio.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "stdio.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpu4ywd1fa/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("stdlib.h")
          #error "Header 'stdlib.h' could not be found"
         #endif
        #else
         #include <stdlib.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "stdlib.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpn5_aul8f/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("strings.h")
          #error "Header 'strings.h' could not be found"
         #endif
        #else
         #include <strings.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "strings.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp68fgxkev/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("string.h")
          #error "Header 'string.h' could not be found"
         #endif
        #else
         #include <string.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "string.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpw09elx3o/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("unistd.h")
          #error "Header 'unistd.h' could not be found"
         #endif
        #else
         #include <unistd.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "unistd.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp7oxvo67b/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("sys/statvfs.h")
          #error "Header 'sys/statvfs.h' could not be found"
         #endif
        #else
         #include <sys/statvfs.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "sys/statvfs.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpbvz2b5ti/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("sys/vfs.h")
          #error "Header 'sys/vfs.h' could not be found"
         #endif
        #else
         #include <sys/vfs.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpbvz2b5ti/testfile.c:4:12: error: "Header 'sys/vfs.h' could not be found"
          #error "Header 'sys/vfs.h' could not be found"
           ^
1 error generated.

Has header "sys/vfs.h" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpp4w11n0c/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("sys/statfs.h")
          #error "Header 'sys/statfs.h' could not be found"
         #endif
        #else
         #include <sys/statfs.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpp4w11n0c/testfile.c:4:12: error: "Header 'sys/statfs.h' could not be found"
          #error "Header 'sys/statfs.h' could not be found"
           ^
1 error generated.

Has header "sys/statfs.h" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp4vdvt5v3/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("sys/stat.h")
          #error "Header 'sys/stat.h' could not be found"
         #endif
        #else
         #include <sys/stat.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "sys/stat.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpof6tfqcx/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("sys/types.h")
          #error "Header 'sys/types.h' could not be found"
         #endif
        #else
         #include <sys/types.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "sys/types.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmprp8foy5n/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("sys/param.h")
          #error "Header 'sys/param.h' could not be found"
         #endif
        #else
         #include <sys/param.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "sys/param.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpj43drzue/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("sys/mount.h")
          #error "Header 'sys/mount.h' could not be found"
         #endif
        #else
         #include <sys/mount.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "sys/mount.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp4lywrgph/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("time.h")
          #error "Header 'time.h' could not be found"
         #endif
        #else
         #include <time.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "time.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpq9dbwzo8/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("wchar.h")
          #error "Header 'wchar.h' could not be found"
         #endif
        #else
         #include <wchar.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "wchar.h" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpouye43hi/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpouye43hi/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define link meson_disable_define_of_link
        
        #include <limits.h>
        #undef link
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char link (void);
        
        #if defined __stub_link || defined __stub___link
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return link ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "link" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp1m8gayo9/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp1m8gayo9/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define mkstemp meson_disable_define_of_mkstemp
        
        #include <limits.h>
        #undef mkstemp
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char mkstemp (void);
        
        #if defined __stub_mkstemp || defined __stub___mkstemp
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return mkstemp ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "mkstemp" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpkg2w58vv/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpkg2w58vv/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define mkostemp meson_disable_define_of_mkostemp
        
        #include <limits.h>
        #undef mkostemp
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char mkostemp (void);
        
        #if defined __stub_mkostemp || defined __stub___mkostemp
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return mkostemp ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "mkostemp" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpuli8s83f/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpuli8s83f/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define _mktemp_s meson_disable_define_of__mktemp_s
        
        #include <limits.h>
        #undef _mktemp_s
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char _mktemp_s (void);
        
        #if defined __stub__mktemp_s || defined __stub____mktemp_s
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return _mktemp_s ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 Undefined symbols for architecture x86_64:
  "__mktemp_s", referenced from:
      _main in testfile-f1eeb5.o
ld: symbol(s) not found for architecture x86_64
clang: error: linker command failed with exit code 1 (use -v to see invocation)

Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpd_v8k4k6/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpd_v8k4k6/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        int main(void) {

        /* With some toolchains (MSYS2/mingw for example) the compiler
         * provides various builtins which are not really implemented and
         * fall back to the stdlib where they aren't provided and fail at
         * build/link time. In case the user provides a header, including
         * the header didn't lead to the function being defined, and the
         * function we are checking isn't a builtin itself we assume the
         * builtin is not functional and we just error out. */
        #if !1 && !defined(_mktemp_s) && !0
            #error "No definition for __builtin__mktemp_s found in the prefix"
        #endif

        #ifdef __has_builtin
            #if !__has_builtin(__builtin__mktemp_s)
                #error "__builtin__mktemp_s not found"
            #endif
        #elif ! defined(_mktemp_s)
            __builtin__mktemp_s;
        #endif
        return 0;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpd_v8k4k6/testfile.c:17:18: error: "__builtin__mktemp_s not found"
                #error "__builtin__mktemp_s not found"
                 ^
1 error generated.

Checking for function "_mktemp_s" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpu17hip8m/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpu17hip8m/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define mkdtemp meson_disable_define_of_mkdtemp
        
        #include <limits.h>
        #undef mkdtemp
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char mkdtemp (void);
        
        #if defined __stub_mkdtemp || defined __stub___mkdtemp
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return mkdtemp ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "mkdtemp" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpv_v4tzn5/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpv_v4tzn5/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define getopt meson_disable_define_of_getopt
        
        #include <limits.h>
        #undef getopt
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char getopt (void);
        
        #if defined __stub_getopt || defined __stub___getopt
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return getopt ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "getopt" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp80_gh053/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp80_gh053/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define getopt_long meson_disable_define_of_getopt_long
        
        #include <limits.h>
        #undef getopt_long
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char getopt_long (void);
        
        #if defined __stub_getopt_long || defined __stub___getopt_long
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return getopt_long ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "getopt_long" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpc2i41eu8/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpc2i41eu8/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define getprogname meson_disable_define_of_getprogname
        
        #include <limits.h>
        #undef getprogname
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char getprogname (void);
        
        #if defined __stub_getprogname || defined __stub___getprogname
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return getprogname ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "getprogname" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp5idnopw7/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp5idnopw7/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define getexecname meson_disable_define_of_getexecname
        
        #include <limits.h>
        #undef getexecname
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char getexecname (void);
        
        #if defined __stub_getexecname || defined __stub___getexecname
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return getexecname ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 Undefined symbols for architecture x86_64:
  "_getexecname", referenced from:
      _main in testfile-004a57.o
ld: symbol(s) not found for architecture x86_64
clang: error: linker command failed with exit code 1 (use -v to see invocation)

Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpg1x70q5m/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpg1x70q5m/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        int main(void) {

        /* With some toolchains (MSYS2/mingw for example) the compiler
         * provides various builtins which are not really implemented and
         * fall back to the stdlib where they aren't provided and fail at
         * build/link time. In case the user provides a header, including
         * the header didn't lead to the function being defined, and the
         * function we are checking isn't a builtin itself we assume the
         * builtin is not functional and we just error out. */
        #if !1 && !defined(getexecname) && !0
            #error "No definition for __builtin_getexecname found in the prefix"
        #endif

        #ifdef __has_builtin
            #if !__has_builtin(__builtin_getexecname)
                #error "__builtin_getexecname not found"
            #endif
        #elif ! defined(getexecname)
            __builtin_getexecname;
        #endif
        return 0;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpg1x70q5m/testfile.c:17:18: error: "__builtin_getexecname not found"
                #error "__builtin_getexecname not found"
                 ^
1 error generated.

Checking for function "getexecname" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpxqycaeah/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpxqycaeah/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define rand meson_disable_define_of_rand
        
        #include <limits.h>
        #undef rand
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char rand (void);
        
        #if defined __stub_rand || defined __stub___rand
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return rand ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "rand" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmptod1su6m/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmptod1su6m/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define random meson_disable_define_of_random
        
        #include <limits.h>
        #undef random
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char random (void);
        
        #if defined __stub_random || defined __stub___random
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return random ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "random" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmplyzawwf8/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmplyzawwf8/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define lrand48 meson_disable_define_of_lrand48
        
        #include <limits.h>
        #undef lrand48
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char lrand48 (void);
        
        #if defined __stub_lrand48 || defined __stub___lrand48
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return lrand48 ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "lrand48" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmprx0go9yg/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmprx0go9yg/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define random_r meson_disable_define_of_random_r
        
        #include <limits.h>
        #undef random_r
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char random_r (void);
        
        #if defined __stub_random_r || defined __stub___random_r
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return random_r ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 Undefined symbols for architecture x86_64:
  "_random_r", referenced from:
      _main in testfile-c80900.o
ld: symbol(s) not found for architecture x86_64
clang: error: linker command failed with exit code 1 (use -v to see invocation)

Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpb9vivmvw/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpb9vivmvw/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        int main(void) {

        /* With some toolchains (MSYS2/mingw for example) the compiler
         * provides various builtins which are not really implemented and
         * fall back to the stdlib where they aren't provided and fail at
         * build/link time. In case the user provides a header, including
         * the header didn't lead to the function being defined, and the
         * function we are checking isn't a builtin itself we assume the
         * builtin is not functional and we just error out. */
        #if !1 && !defined(random_r) && !0
            #error "No definition for __builtin_random_r found in the prefix"
        #endif

        #ifdef __has_builtin
            #if !__has_builtin(__builtin_random_r)
                #error "__builtin_random_r not found"
            #endif
        #elif ! defined(random_r)
            __builtin_random_r;
        #endif
        return 0;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpb9vivmvw/testfile.c:17:18: error: "__builtin_random_r not found"
                #error "__builtin_random_r not found"
                 ^
1 error generated.

Checking for function "random_r" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpr1g1z90o/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpr1g1z90o/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define rand_r meson_disable_define_of_rand_r
        
        #include <limits.h>
        #undef rand_r
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char rand_r (void);
        
        #if defined __stub_rand_r || defined __stub___rand_r
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return rand_r ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "rand_r" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp434zffnv/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp434zffnv/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define readlink meson_disable_define_of_readlink
        
        #include <limits.h>
        #undef readlink
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char readlink (void);
        
        #if defined __stub_readlink || defined __stub___readlink
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return readlink ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "readlink" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp4vy68c4y/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp4vy68c4y/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define fstatvfs meson_disable_define_of_fstatvfs
        
        #include <limits.h>
        #undef fstatvfs
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char fstatvfs (void);
        
        #if defined __stub_fstatvfs || defined __stub___fstatvfs
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return fstatvfs ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "fstatvfs" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpv6nhoy4x/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpv6nhoy4x/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define fstatfs meson_disable_define_of_fstatfs
        
        #include <limits.h>
        #undef fstatfs
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char fstatfs (void);
        
        #if defined __stub_fstatfs || defined __stub___fstatfs
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return fstatfs ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "fstatfs" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpi_5hhrz9/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpi_5hhrz9/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define lstat meson_disable_define_of_lstat
        
        #include <limits.h>
        #undef lstat
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char lstat (void);
        
        #if defined __stub_lstat || defined __stub___lstat
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return lstat ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "lstat" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp48cnvp4c/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp48cnvp4c/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define strerror meson_disable_define_of_strerror
        
        #include <limits.h>
        #undef strerror
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char strerror (void);
        
        #if defined __stub_strerror || defined __stub___strerror
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return strerror ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp48cnvp4c/testfile.c:10:14: warning: incompatible redeclaration of library function 'strerror' [-Wincompatible-library-redeclaration]
        char strerror (void);
             ^
/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp48cnvp4c/testfile.c:10:14: note: 'strerror' is a builtin with type 'char *(int)'
1 warning generated.

Checking for function "strerror" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp0nodhef3/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp0nodhef3/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define strerror_r meson_disable_define_of_strerror_r
        
        #include <limits.h>
        #undef strerror_r
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char strerror_r (void);
        
        #if defined __stub_strerror_r || defined __stub___strerror_r
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return strerror_r ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "strerror_r" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpsoiuf866/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpsoiuf866/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define mmap meson_disable_define_of_mmap
        
        #include <limits.h>
        #undef mmap
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char mmap (void);
        
        #if defined __stub_mmap || defined __stub___mmap
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return mmap ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "mmap" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpfhxxjexa/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpfhxxjexa/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define vprintf meson_disable_define_of_vprintf
        
        #include <limits.h>
        #undef vprintf
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char vprintf (void);
        
        #if defined __stub_vprintf || defined __stub___vprintf
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return vprintf ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpfhxxjexa/testfile.c:10:14: warning: incompatible redeclaration of library function 'vprintf' [-Wincompatible-library-redeclaration]
        char vprintf (void);
             ^
/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpfhxxjexa/testfile.c:10:14: note: 'vprintf' is a builtin with type 'int (const char *, struct __va_list_tag *)'
1 warning generated.

Checking for function "vprintf" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpajbrpchd/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpajbrpchd/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define vsnprintf meson_disable_define_of_vsnprintf
        
        #include <limits.h>
        #undef vsnprintf
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char vsnprintf (void);
        
        #if defined __stub_vsnprintf || defined __stub___vsnprintf
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return vsnprintf ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpajbrpchd/testfile.c:10:14: warning: incompatible redeclaration of library function 'vsnprintf' [-Wincompatible-library-redeclaration]
        char vsnprintf (void);
             ^
/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpajbrpchd/testfile.c:10:14: note: 'vsnprintf' is a builtin with type 'int (char *, unsigned long, const char *, struct __va_list_tag *)'
1 warning generated.

Checking for function "vsnprintf" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp03ackfuh/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp03ackfuh/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define vsprintf meson_disable_define_of_vsprintf
        
        #include <limits.h>
        #undef vsprintf
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char vsprintf (void);
        
        #if defined __stub_vsprintf || defined __stub___vsprintf
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return vsprintf ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp03ackfuh/testfile.c:10:14: warning: incompatible redeclaration of library function 'vsprintf' [-Wincompatible-library-redeclaration]
        char vsprintf (void);
             ^
/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp03ackfuh/testfile.c:10:14: note: 'vsprintf' is a builtin with type 'int (char *, const char *, struct __va_list_tag *)'
1 warning generated.

Checking for function "vsprintf" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpcejctcmw/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpcejctcmw/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define getpagesize meson_disable_define_of_getpagesize
        
        #include <limits.h>
        #undef getpagesize
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char getpagesize (void);
        
        #if defined __stub_getpagesize || defined __stub___getpagesize
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return getpagesize ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "getpagesize" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpyytoztwy/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpyytoztwy/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define getpid meson_disable_define_of_getpid
        
        #include <limits.h>
        #undef getpid
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char getpid (void);
        
        #if defined __stub_getpid || defined __stub___getpid
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return getpid ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "getpid" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpbuitcgdd/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpbuitcgdd/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define dcgettext meson_disable_define_of_dcgettext
        
        #include <limits.h>
        #undef dcgettext
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char dcgettext (void);
        
        #if defined __stub_dcgettext || defined __stub___dcgettext
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return dcgettext ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 Undefined symbols for architecture x86_64:
  "_dcgettext", referenced from:
      _main in testfile-cbe066.o
ld: symbol(s) not found for architecture x86_64
clang: error: linker command failed with exit code 1 (use -v to see invocation)

Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpad7i0udt/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpad7i0udt/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        int main(void) {

        /* With some toolchains (MSYS2/mingw for example) the compiler
         * provides various builtins which are not really implemented and
         * fall back to the stdlib where they aren't provided and fail at
         * build/link time. In case the user provides a header, including
         * the header didn't lead to the function being defined, and the
         * function we are checking isn't a builtin itself we assume the
         * builtin is not functional and we just error out. */
        #if !1 && !defined(dcgettext) && !0
            #error "No definition for __builtin_dcgettext found in the prefix"
        #endif

        #ifdef __has_builtin
            #if !__has_builtin(__builtin_dcgettext)
                #error "__builtin_dcgettext not found"
            #endif
        #elif ! defined(dcgettext)
            __builtin_dcgettext;
        #endif
        return 0;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpad7i0udt/testfile.c:17:18: error: "__builtin_dcgettext not found"
                #error "__builtin_dcgettext not found"
                 ^
1 error generated.

Checking for function "dcgettext" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp8ggvv_we/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp8ggvv_we/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define gettext meson_disable_define_of_gettext
        
        #include <limits.h>
        #undef gettext
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char gettext (void);
        
        #if defined __stub_gettext || defined __stub___gettext
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return gettext ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 Undefined symbols for architecture x86_64:
  "_gettext", referenced from:
      _main in testfile-a3e208.o
ld: symbol(s) not found for architecture x86_64
clang: error: linker command failed with exit code 1 (use -v to see invocation)

Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp51kfl7em/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp51kfl7em/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        int main(void) {

        /* With some toolchains (MSYS2/mingw for example) the compiler
         * provides various builtins which are not really implemented and
         * fall back to the stdlib where they aren't provided and fail at
         * build/link time. In case the user provides a header, including
         * the header didn't lead to the function being defined, and the
         * function we are checking isn't a builtin itself we assume the
         * builtin is not functional and we just error out. */
        #if !1 && !defined(gettext) && !0
            #error "No definition for __builtin_gettext found in the prefix"
        #endif

        #ifdef __has_builtin
            #if !__has_builtin(__builtin_gettext)
                #error "__builtin_gettext not found"
            #endif
        #elif ! defined(gettext)
            __builtin_gettext;
        #endif
        return 0;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp51kfl7em/testfile.c:17:18: error: "__builtin_gettext not found"
                #error "__builtin_gettext not found"
                 ^
1 error generated.

Checking for function "gettext" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpp_653udm/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpp_653udm/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports 

Code:
 
        #define localtime_r meson_disable_define_of_localtime_r
        
        #include <limits.h>
        #undef localtime_r
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char localtime_r (void);
        
        #if defined __stub_localtime_r || defined __stub___localtime_r
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return localtime_r ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "localtime_r" : YES (cached)
Using cached compile:
Cached command line:  cc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp4qhp6z3q/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp4qhp6z3q/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports /usr/local/opt/freetype/lib/libfreetype.dylib 

Code:
 
        #define FT_Get_BDF_Property meson_disable_define_of_FT_Get_BDF_Property
        
        #include <limits.h>
        #undef FT_Get_BDF_Property
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char FT_Get_BDF_Property (void);
        
        #if defined __stub_FT_Get_BDF_Property || defined __stub___FT_Get_BDF_Property
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return FT_Get_BDF_Property ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "FT_Get_BDF_Property" with dependency freetype2: YES (cached)
Using cached compile:
Cached command line:  cc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpattgrh3q/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpattgrh3q/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports /usr/local/opt/freetype/lib/libfreetype.dylib 

Code:
 
        #define FT_Get_PS_Font_Info meson_disable_define_of_FT_Get_PS_Font_Info
        
        #include <limits.h>
        #undef FT_Get_PS_Font_Info
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char FT_Get_PS_Font_Info (void);
        
        #if defined __stub_FT_Get_PS_Font_Info || defined __stub___FT_Get_PS_Font_Info
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return FT_Get_PS_Font_Info ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "FT_Get_PS_Font_Info" with dependency freetype2: YES (cached)
Using cached compile:
Cached command line:  cc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpya44gue4/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpya44gue4/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports /usr/local/opt/freetype/lib/libfreetype.dylib 

Code:
 
        #define FT_Has_PS_Glyph_Names meson_disable_define_of_FT_Has_PS_Glyph_Names
        
        #include <limits.h>
        #undef FT_Has_PS_Glyph_Names
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char FT_Has_PS_Glyph_Names (void);
        
        #if defined __stub_FT_Has_PS_Glyph_Names || defined __stub___FT_Has_PS_Glyph_Names
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return FT_Has_PS_Glyph_Names ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "FT_Has_PS_Glyph_Names" with dependency freetype2: YES (cached)
Using cached compile:
Cached command line:  cc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp9j_23yr5/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp9j_23yr5/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports /usr/local/opt/freetype/lib/libfreetype.dylib 

Code:
 
        #define FT_Get_X11_Font_Format meson_disable_define_of_FT_Get_X11_Font_Format
        
        #include <limits.h>
        #undef FT_Get_X11_Font_Format
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char FT_Get_X11_Font_Format (void);
        
        #if defined __stub_FT_Get_X11_Font_Format || defined __stub___FT_Get_X11_Font_Format
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return FT_Get_X11_Font_Format ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "FT_Get_X11_Font_Format" with dependency freetype2: YES (cached)
Using cached compile:
Cached command line:  cc -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/opt/libpng/include/libpng16 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpzcvlj8jf/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpzcvlj8jf/output.exe -O0 -Werror=implicit-function-declaration -std=c11 -Wl,-no_weak_imports /usr/local/opt/freetype/lib/libfreetype.dylib 

Code:
 
        #define FT_Done_MM_Var meson_disable_define_of_FT_Done_MM_Var
        
        #include <limits.h>
        #undef FT_Done_MM_Var
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char FT_Done_MM_Var (void);
        
        #if defined __stub_FT_Done_MM_Var || defined __stub___FT_Done_MM_Var
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return FT_Done_MM_Var ();
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for function "FT_Done_MM_Var" with dependency freetype2: YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpijuaztyd/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpijuaztyd/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 
        #include <fcntl.h>
        int main(void) {
            /* If it's not defined as a macro, try to use as a symbol */
            #ifndef posix_fadvise
                posix_fadvise;
            #endif
            return 0;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpijuaztyd/testfile.c:6:17: error: use of undeclared identifier 'posix_fadvise'
                posix_fadvise;
                ^
1 error generated.

Header "fcntl.h" has symbol "posix_fadvise" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpk0oivlcw/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpk0oivlcw/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <sys/statvfs.h>

        void bar(void) {
            struct statvfs foo;
            (void) ( foo.f_basetype );

            (void) foo;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpk0oivlcw/testfile.c:5:26: error: no member named 'f_basetype' in 'struct statvfs'
            (void) ( foo.f_basetype );
                     ~~~ ^
1 error generated.

Checking whether type "struct statvfs" has member "f_basetype" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp76pmb1z2/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp76pmb1z2/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <sys/statvfs.h>

        void bar(void) {
            struct statvfs foo;
            (void) ( foo.f_fstypename );

            (void) foo;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp76pmb1z2/testfile.c:5:26: error: no member named 'f_fstypename' in 'struct statvfs'
            (void) ( foo.f_fstypename );
                     ~~~ ^
1 error generated.

Checking whether type "struct statvfs" has member "f_fstypename" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpjk7e62it/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpjk7e62it/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <sys/vfs.h>
#include <sys/statfs.h>
#include <sys/param.h>
#include <sys/mount.h>

        void bar(void) {
            struct statfs foo;
            (void) ( foo.f_flags );

            (void) foo;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpjk7e62it/testfile.c:1:10: fatal error: 'sys/vfs.h' file not found
#include <sys/vfs.h>
         ^~~~~~~~~~~
1 error generated.

Checking whether type "struct statfs" has member "f_flags" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmps9ovz_m_/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmps9ovz_m_/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <sys/vfs.h>
#include <sys/statfs.h>
#include <sys/param.h>
#include <sys/mount.h>

        void bar(void) {
            struct statfs foo;
            (void) ( foo.f_fstypename );

            (void) foo;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmps9ovz_m_/testfile.c:1:10: fatal error: 'sys/vfs.h' file not found
#include <sys/vfs.h>
         ^~~~~~~~~~~
1 error generated.

Checking whether type "struct statfs" has member "f_fstypename" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpz9mgh828/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpz9mgh828/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <sys/stat.h>

        void bar(void) {
            struct stat foo;
            (void) ( foo.st_mtim );

            (void) foo;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpz9mgh828/testfile.c:5:26: error: no member named 'st_mtim' in 'struct stat'
            (void) ( foo.st_mtim );
                     ~~~ ^
1 error generated.

Checking whether type "struct stat" has member "st_mtim" : NO (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpgp39e96z/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpgp39e96z/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <sys/types.h>
#include <dirent.h>

        void bar(void) {
            struct dirent foo;
            (void) ( foo.d_type );

            (void) foo;
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking whether type "struct dirent" has member "d_type" : YES (cached)
Using cached run result:
Code:
 
        #include<stddef.h>
        #include<stdio.h>
        int main(void) {
            printf("%ld\n", (long)(sizeof(void *)));
            return 0;
        }
Args:
 functools.partial(<bound method CompilerHolder._determine_args of <[CompilerHolder] holds [AppleClangCCompiler]: <AppleClangCCompiler: v14.0.0 `cc`>>>, {'args': [], 'dependencies': [], 'include_directories': [], 'prefix': '', 'no_builtin_args': False})
Cached run returncode:
 0
Cached run stdout:
 8

Cached run stderr:
 
Checking for size of "void *" : 8 (cached)
Using cached run result:
Code:
 
        #include <stdio.h>
        #include <stddef.h>
        struct tmp {
            char c;
            void * target;
        };
        int main(void) {
            printf("%d", (int)offsetof(struct tmp, target));
            return 0;
        }
Args:
 []
Cached run returncode:
 0
Cached run stdout:
 8
Cached run stderr:
 
Checking for alignment of "void *" : 8 (cached)
Using cached run result:
Code:
 
        #include <stdio.h>
        #include <stddef.h>
        struct tmp {
            char c;
            double target;
        };
        int main(void) {
            printf("%d", (int)offsetof(struct tmp, target));
            return 0;
        }
Args:
 []
Cached run returncode:
 0
Cached run stdout:
 8
Cached run stderr:
 
Checking for alignment of "double" : 8 (cached)
Running compile:
Working directory:  /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp3w4epi6m
Source file: /Users/<USER>/work/fontconfig/meson-cc-tests/flexible-array-member-test.c
-----------
Command line: `cc /Users/<USER>/work/fontconfig/meson-cc-tests/flexible-array-member-test.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp3w4epi6m/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11` -> 0
Running compile:
Working directory:  /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpj_pwdwug
Source file: /Users/<USER>/work/fontconfig/meson-cc-tests/pthread-prio-inherit-test.c
-----------
Command line: `cc /Users/<USER>/work/fontconfig/meson-cc-tests/pthread-prio-inherit-test.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpj_pwdwug/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11` -> 0
Running compile:
Working directory:  /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpq2ittxjq
Source file: /Users/<USER>/work/fontconfig/meson-cc-tests/stdatomic-primitives-test.c
-----------
Command line: `cc /Users/<USER>/work/fontconfig/meson-cc-tests/stdatomic-primitives-test.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpq2ittxjq/output.exe -O0 -Werror=implicit-function-declaration -std=c11` -> 0
Checking if "stdatomic.h atomics" links: YES 
Running compile:
Working directory:  /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpvj68dig_
Source file: /Users/<USER>/work/fontconfig/meson-cc-tests/intel-atomic-primitives-test.c
-----------
Command line: `cc /Users/<USER>/work/fontconfig/meson-cc-tests/intel-atomic-primitives-test.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpvj68dig_/output.exe -O0 -Werror=implicit-function-declaration -std=c11` -> 0
Checking if "Intel atomics" links: YES 
Running compile:
Working directory:  /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp1n8tt_ye
Source file: /Users/<USER>/work/fontconfig/meson-cc-tests/solaris-atomic-operations.c
-----------
Command line: `cc /Users/<USER>/work/fontconfig/meson-cc-tests/solaris-atomic-operations.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp1n8tt_ye/output.exe -O0 -Werror=implicit-function-declaration -std=c11` -> 1
stderr:
/Users/<USER>/work/fontconfig/meson-cc-tests/solaris-atomic-operations.c:1:10: fatal error: 'atomic.h' file not found
#include <atomic.h>
         ^~~~~~~~~~
1 error generated.
-----------
Checking if "Solaris atomic ops" links: NO 
Dependency threads found: YES unknown (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpewr1o7dx/testfile.c -E -P -P -O0 -Werror=implicit-function-declaration -std=c11 

Code:
 
        #ifdef __has_include
         #if !__has_include("pthread.h")
          #error "Header 'pthread.h' could not be found"
         #endif
        #else
         #include <pthread.h>
        #endif
Cached compiler stdout:
 

Cached compiler stderr:
 
Has header "pthread.h" : YES (cached)
Program gperf found: YES (/usr/bin/gperf)
Running command: /usr/bin/gperf -L ANSI-C /Users/<USER>/work/fontconfig/meson-cc-tests/gperf.txt
--- stdout ---
/* ANSI-C code produced by gperf version 3.0.3 */
/* Command-line: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/gperf -L ANSI-C /Users/<USER>/work/fontconfig/meson-cc-tests/gperf.txt  */
/* Computed positions: -k'' */


#define TOTAL_KEYWORDS 1
#define MIN_WORD_LENGTH 3
#define MAX_WORD_LENGTH 3
#define MIN_HASH_VALUE 3
#define MAX_HASH_VALUE 3
/* maximum key range = 1, duplicates = 0 */

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
/*ARGSUSED*/
static unsigned int
hash (register const char *str, register unsigned int len)
{
  return len;
}

const char *
in_word_set (register const char *str, register unsigned int len)
{
  static const char * wordlist[] =
    {
      "", "", "",
      "foo"
    };

  if (len <= MAX_WORD_LENGTH && len >= MIN_WORD_LENGTH)
    {
      unsigned int key = hash (str, len);

      if (key <= MAX_HASH_VALUE)
        {
          register const char *s = wordlist[key];

          if (*str == *s && !strcmp (str + 1, s + 1))
            return s;
        }
    }
  return 0;
}

--- stderr ---


Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp5c8ek9ky/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp5c8ek9ky/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 
  #include <string.h>
  const char * in_word_set(const char *, size_t);
  /* ANSI-C code produced by gperf version 3.0.3 */
/* Command-line: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/gperf -L ANSI-C /Users/<USER>/work/fontconfig/meson-cc-tests/gperf.txt  */
/* Computed positions: -k'' */


#define TOTAL_KEYWORDS 1
#define MIN_WORD_LENGTH 3
#define MAX_WORD_LENGTH 3
#define MIN_HASH_VALUE 3
#define MAX_HASH_VALUE 3
/* maximum key range = 1, duplicates = 0 */

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
/*ARGSUSED*/
static unsigned int
hash (register const char *str, register unsigned int len)
{
  return len;
}

const char *
in_word_set (register const char *str, register unsigned int len)
{
  static const char * wordlist[] =
    {
      "", "", "",
      "foo"
    };

  if (len <= MAX_WORD_LENGTH && len >= MIN_WORD_LENGTH)
    {
      unsigned int key = hash (str, len);

      if (key <= MAX_HASH_VALUE)
        {
          register const char *s = wordlist[key];

          if (*str == *s && !strcmp (str + 1, s + 1))
            return s;
        }
    }
  return 0;
}

  
Cached compiler stdout:
 
Cached compiler stderr:
 /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp5c8ek9ky/testfile.c:31:1: error: conflicting types for 'in_word_set'
in_word_set (register const char *str, register unsigned int len)
^
/Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp5c8ek9ky/testfile.c:3:16: note: previous declaration is here
  const char * in_word_set(const char *, size_t);
               ^
1 error generated.

Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpekgdb98r/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpekgdb98r/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 
  #include <string.h>
  const char * in_word_set(const char *, unsigned);
  /* ANSI-C code produced by gperf version 3.0.3 */
/* Command-line: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/gperf -L ANSI-C /Users/<USER>/work/fontconfig/meson-cc-tests/gperf.txt  */
/* Computed positions: -k'' */


#define TOTAL_KEYWORDS 1
#define MIN_WORD_LENGTH 3
#define MAX_WORD_LENGTH 3
#define MIN_HASH_VALUE 3
#define MAX_HASH_VALUE 3
/* maximum key range = 1, duplicates = 0 */

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
/*ARGSUSED*/
static unsigned int
hash (register const char *str, register unsigned int len)
{
  return len;
}

const char *
in_word_set (register const char *str, register unsigned int len)
{
  static const char * wordlist[] =
    {
      "", "", "",
      "foo"
    };

  if (len <= MAX_WORD_LENGTH && len >= MIN_WORD_LENGTH)
    {
      unsigned int key = hash (str, len);

      if (key <= MAX_HASH_VALUE)
        {
          register const char *s = wordlist[key];

          if (*str == *s && !strcmp (str + 1, s + 1))
            return s;
        }
    }
  return 0;
}

  
Cached compiler stdout:
 
Cached compiler stderr:
 
Message: gperf len type is unsigned
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp1d3qon3u/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp1d3qon3u/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <stdint.h>
        void bar(void) {
            (void) sizeof(uint64_t);
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for type "uint64_t" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp4ps1uo03/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp4ps1uo03/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <stdint.h>
        void bar(void) {
            (void) sizeof(int32_t);
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for type "int32_t" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpe8a8u5x_/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpe8a8u5x_/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <stdint.h>
        void bar(void) {
            (void) sizeof(uintptr_t);
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for type "uintptr_t" : YES (cached)
Using cached compile:
Cached command line:  cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp2i23m6xc/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmp2i23m6xc/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument -std=c11 

Code:
 #include <stdint.h>
        void bar(void) {
            (void) sizeof(intptr_t);
        }
Cached compiler stdout:
 
Cached compiler stderr:
 
Checking for type "intptr_t" : YES (cached)
Configuring fontconfig.h using configuration
Program fc-case.py found: YES (/Users/<USER>/work/fontconfig/fc-case/fc-case.py)
Program fc-lang.py found: YES (/Users/<USER>/work/fontconfig/fc-lang/fc-lang.py)
Dependency threads found: YES unknown (cached)
Dependency threads found: YES unknown (cached)
Adding test "test_bz89617"
Adding test "test_bz131804"
Adding test "test_bz96676"
Adding test "test_name_parse"
Adding test "test_bz106618"
Adding test "test_bz1744377"
Adding test "test_issue180"
Adding test "test_family_matching"
Adding test "test_ptrlist"
Adding test "test_bz106632"
Adding test "test_issue107"
Adding test "test_crbug1004254"
Adding test "test_mt_fccfg"
Program run-test.sh found: YES (/bin/bash /Users/<USER>/work/fontconfig/test/run-test.sh)
Adding test "run_test_sh"
Program write-35-lang-normalize-conf.py found: YES (/Users/<USER>/work/fontconfig/conf.d/write-35-lang-normalize-conf.py)
Configuring README using configuration
Program xgettext skipped: feature nls disabled
Program check-whitespace-in-args.py found: YES (/Users/<USER>/work/fontconfig/doc/check-whitespace-in-args.py)
Program docbook2man found: NO
Program docbook2txt found: NO
Program docbook2pdf found: NO
Program docbook2html found: NO
Program run-quiet.py found: YES (/Users/<USER>/work/fontconfig/doc/run-quiet.py)
Program extract-man-list.py found: YES (/Users/<USER>/work/fontconfig/doc/extract-man-list.py)
Running command: /Users/<USER>/work/fontconfig/doc/extract-man-list.py /Users/<USER>/work/fontconfig/doc/fcatomic.fncs /Users/<USER>/work/fontconfig/doc/fcblanks.fncs /Users/<USER>/work/fontconfig/doc/fccache.fncs /Users/<USER>/work/fontconfig/doc/fccharset.fncs /Users/<USER>/work/fontconfig/doc/fcconfig.fncs /Users/<USER>/work/fontconfig/doc/fcconstant.fncs /Users/<USER>/work/fontconfig/doc/fcdircache.fncs /Users/<USER>/work/fontconfig/doc/fcfile.fncs /Users/<USER>/work/fontconfig/doc/fcfontset.fncs /Users/<USER>/work/fontconfig/doc/fcfontations.fncs /Users/<USER>/work/fontconfig/doc/fcformat.fncs /Users/<USER>/work/fontconfig/doc/fcfreetype.fncs /Users/<USER>/work/fontconfig/doc/fcinit.fncs /Users/<USER>/work/fontconfig/doc/fclangset.fncs /Users/<USER>/work/fontconfig/doc/fcmatrix.fncs /Users/<USER>/work/fontconfig/doc/fcobjectset.fncs /Users/<USER>/work/fontconfig/doc/fcobjecttype.fncs /Users/<USER>/work/fontconfig/doc/fcpattern.fncs /Users/<USER>/work/fontconfig/doc/fcrange.fncs /Users/<USER>/work/fontconfig/doc/fcstring.fncs /Users/<USER>/work/fontconfig/doc/fcstrset.fncs /Users/<USER>/work/fontconfig/doc/fcvalue.fncs /Users/<USER>/work/fontconfig/doc/fcweight.fncs
--- stdout ---
FcAtomicCreate
FcAtomicLock
FcAtomicNewFile
FcAtomicOrigFile
FcAtomicReplaceOrig
FcAtomicDeleteNew
FcAtomicUnlock
FcAtomicDestroy
FcBlanksCreate
FcBlanksDestroy
FcBlanksAdd
FcBlanksIsMember
FcCacheDir
FcCacheCopySet
FcCacheSubdir
FcCacheNumSubdir
FcCacheNumFont
FcDirCacheClean
FcCacheCreateTagFile
FcDirCacheCreateUUID
FcDirCacheDeleteUUID
FcCharSetCreate
FcCharSetDestroy
FcCharSetAddChar
FcCharSetDelChar
FcCharSetCopy
FcCharSetEqual
FcCharSetIntersect
FcCharSetUnion
FcCharSetSubtract
FcCharSetMerge
FcCharSetHasChar
FcCharSetCount
FcCharSetIntersectCount
FcCharSetSubtractCount
FcCharSetIsSubset
FcCharSetFirstPage
FcCharSetNextPage
FcCharSetCoverage
FcCharSetNew
FcConfigCreate
FcConfigReference
FcConfigDestroy
FcConfigSetCurrent
FcConfigGetCurrent
FcConfigSetFontSetFilter
FcConfigAcceptFilter
FcConfigUptoDate
FcConfigHome
FcConfigEnableHome
FcConfigBuildFonts
FcConfigGetConfigDirs
FcConfigGetFontDirs
FcConfigGetConfigFiles
FcConfigGetCache
FcConfigGetCacheDirs
FcConfigGetFonts
FcConfigGetBlanks
FcConfigGetRescanInterval
FcConfigSetRescanInterval
FcConfigAppFontAddFile
FcConfigAppFontAddDir
FcConfigAppFontClear
FcConfigSubstituteWithPat
FcConfigSubstitute
FcFontMatch
FcFontSort
FcFontRenderPrepare
FcFontList
FcConfigFilename
FcConfigGetFilename
FcConfigParseAndLoad
FcConfigParseAndLoadFromMemory
FcConfigGetSysRoot
FcConfigSetSysRoot
FcConfigFileInfoIterInit
FcConfigFileInfoIterNext
FcConfigFileInfoIterGet
FcConfigAcceptFont
FcConfigPreferAppFont
FcNameRegisterConstants
FcNameUnregisterConstants
FcNameGetConstant
FcNameGetConstantFor
FcNameConstant
FcDirCacheUnlink
FcDirCacheValid
FcDirCacheLoad
FcDirCacheRescan
FcDirCacheRead
FcDirCacheLoadFile
FcDirCacheUnload
FcFileScan
FcFileIsDir
FcDirScan
FcDirSave
FcFontSetCreate
FcFontSetDestroy
FcFontSetAdd
FcFontSetList
FcFontSetMatch
FcFontSetPrint
FcFontSetSort
FcFontSetSortDestroy
FcFontationsQueryAll
FcPatternFormat
FcFreeTypeCharIndex
FcFreeTypeCharSet
FcFreeTypeCharSetAndSpacing
FcFreeTypeQuery
FcFreeTypeQueryAll
FcFreeTypeQueryFace
FcInitLoadConfig
FcInitLoadConfigAndFonts
FcInit
FcFini
FcGetVersion
FcInitReinitialize
FcInitBringUptoDate
FcLangSetCreate
FcLangSetDestroy
FcLangSetCopy
FcLangSetAdd
FcLangSetDel
FcLangSetUnion
FcLangSetSubtract
FcLangSetCompare
FcLangSetContains
FcLangSetEqual
FcLangSetHash
FcLangSetHasLang
FcGetDefaultLangs
FcConfigGetDefaultLangs
FcLangSetGetLangs
FcGetLangs
FcLangNormalize
FcLangGetCharSet
FcMatrixInit
FcMatrixCopy
FcMatrixEqual
FcMatrixMultiply
FcMatrixRotate
FcMatrixScale
FcMatrixShear
FcObjectSetCreate
FcObjectSetAdd
FcObjectSetDestroy
FcObjectSetBuild
FcNameRegisterObjectTypes
FcNameUnregisterObjectTypes
FcNameGetObjectType
FcPatternCreate
FcPatternDuplicate
FcPatternReference
FcPatternDestroy
FcPatternObjectCount
FcPatternEqual
FcPatternEqualSubset
FcPatternFilter
FcPatternHash
FcPatternAdd
FcPatternAddWeak
FcPatternAdd-Type
FcPatternGetWithBinding
FcPatternGet
FcPatternGet-Type
FcPatternBuild
FcPatternDel
FcPatternRemove
FcPatternIterStart
FcPatternIterNext
FcPatternIterEqual
FcPatternFindIter
FcPatternIterIsValid
FcPatternIterGetObject
FcPatternIterValueCount
FcPatternIterGetValue
FcPatternPrint
FcDefaultSubstitute
FcConfigSetDefaultSubstitute
FcNameParse
FcNameUnparse
FcRangeCopy
FcRangeCreateDouble
FcRangeCreateInteger
FcRangeDestroy
FcRangeGetDouble
FcUtf8ToUcs4
FcUcs4ToUtf8
FcUtf8Len
FcUtf16ToUcs4
FcUtf16Len
FcIsLower
FcIsUpper
FcToLower
FcStrCopy
FcStrDowncase
FcStrCopyFilename
FcStrCmp
FcStrCmpIgnoreCase
FcStrStr
FcStrStrIgnoreCase
FcStrPlus
FcStrFree
FcStrBuildFilename
FcStrDirname
FcStrBasename
FcStrSetCreate
FcStrSetMember
FcStrSetEqual
FcStrSetAdd
FcStrSetAddFilename
FcStrSetDel
FcStrSetDestroy
FcStrListCreate
FcStrListFirst
FcStrListNext
FcStrListDone
FcValueDestroy
FcValueSave
FcValuePrint
FcValueEqual
FcWeightFromOpenTypeDouble
FcWeightToOpenTypeDouble
FcWeightFromOpenType
FcWeightToOpenType

--- stderr ---


Program edit-sgml.py found: YES (/Users/<USER>/work/fontconfig/doc/edit-sgml.py)
Configuring cache-version.sgml using configuration
Configuring version.sgml using configuration
Configuring confdir.sgml using configuration
Program check-missing-doc.py found: YES (/Users/<USER>/work/fontconfig/doc/check-missing-doc.py)
Adding test "check-missing-doc.py"
Configuring meson-config.h using configuration
Configuring fonts.conf using configuration
Build targets in project: 61

fontconfig 2.17.1

  General
    Documentation              : NO
    NLS                        : NO
    Tests                      : YES
    Pytest                     : NO
    Tools                      : YES
    iconv                      : NO
    XML backend                : expat
    Fontations support         : disabled

  Defaults
    Hinting                    : slight
    Sub Pixel Rendering        : none
    Bitmap                     : no-except-emoji
    Font directories           : /System/Library/Fonts, /Library/Fonts,
                                 ~/Library/Fonts,
                                 /System/Library/Assets/com_apple_MobileAsset_Font3,
                                 /System/Library/Assets/com_apple_MobileAsset_Font4
    Additional font directories:

  Paths
    Cache directory            : /usr/local/var/cache/fontconfig
    Template directory         : /usr/local/share/fontconfig/conf.avail
    Base config directory      : /usr/local/etc/fonts
    Config directory           : /usr/local/etc/fonts/conf.d
    XML directory              : /usr/local/share/xml/fontconfig

  Subprojects
    libintl                    : YES

  User defined options
    buildtype                  : debug

Found ninja-1.12.1 at /usr/local/bin/ninja
Running compile:
Working directory:  /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpfircl4ld
Code:

-----------
Command line: `cc /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpfircl4ld/testfile.c -o /Users/<USER>/work/fontconfig/build_meson_dll/meson-private/tmpfircl4ld/output.obj -c -O0 -Werror=implicit-function-declaration -Werror=unknown-warning-option -Werror=unused-command-line-argument -Werror=ignored-optimization-argument --print-search-dirs` -> 0
stdout:
programs: =/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
libraries: =/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0
-----------
WARNING: Running the setup command as `meson [options]` instead of `meson setup [options]` is ambiguous and deprecated.
