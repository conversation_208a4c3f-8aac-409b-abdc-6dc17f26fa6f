/*
 * fontconfig/doc/fcconfig.fncs
 *
 * Copyright © 2003 <PERSON>
 *
 * Permission to use, copy, modify, distribute, and sell this software and its
 * documentation for any purpose is hereby granted without fee, provided that
 * the above copyright notice appear in all copies and that both that
 * copyright notice and this permission notice appear in supporting
 * documentation, and that the name of the author(s) not be used in
 * advertising or publicity pertaining to distribution of the software without
 * specific, written prior permission.  The authors make no
 * representations about the suitability of this software for any purpose.  It
 * is provided "as is" without express or implied warranty.
 *
 * THE AUTHOR(S) DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
 * INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
 * EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
 * CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
 * DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
 * TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */
@RET@           FcConfig *
@FUNC@          FcConfigCreate
@TYPE1@         void
@PURPOSE@       Create a configuration
@DESC@
Creates an empty configuration.
@@

@RET@           FcConfig *
@FUNC@          FcConfigReference
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Increment config reference count
@DESC@
Add another reference to <parameter>config</parameter>. Configs are freed only
when the reference count reaches zero.
If <parameter>config</parameter> is NULL, the current configuration is used.
In that case this function will be similar to FcConfigGetCurrent() except that
it increments the reference count before returning and the user is responsible
for destroying the configuration when not needed anymore.
@@

@RET@           void
@FUNC@          FcConfigDestroy
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Destroy a configuration
@DESC@
Decrements the config reference count. If all references are gone, destroys
the configuration and any data associated with it.
Note that calling this function with the return from FcConfigGetCurrent will
cause a new configuration to be created for use as current configuration.
@@

@RET@           FcBool
@FUNC@          FcConfigSetCurrent
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Set configuration as default
@DESC@
Sets the current default configuration to <parameter>config</parameter>.  Implicitly calls
FcConfigBuildFonts if necessary, and FcConfigReference() to inrease the reference count
in <parameter>config</parameter> since 2.12.0, returning FcFalse if that call fails.
@@

@RET@           FcConfig *
@FUNC@          FcConfigGetCurrent
@TYPE1@         void
@PURPOSE@       Return current configuration
@DESC@
Returns the current default configuration.
@@

@RET@           FcConfig *
@FUNC@          FcConfigSetFontSetFilter
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcFilterFontSetFunc%            @ARG2@          filter_func
@TYPE3@         FcDestroyFunc%                  @ARG3@          destroy_data_func
@TYPE4@         void *                          @ARG4@          user_data
@PURPOSE@       Set a predicate function to filter fontsets
@DESC@
Sets <parameter>filter_func</parameter> as a predicate function and filter out
fontsets in <parameter>config</parameter> as desired.
<parameter>filter_func</parameter> will be called with a font pattern and
<parameter>user_data</parameter> only when loading caches.
When <parameter>config</parameter> is going to be destroyed,
<parameter>user_data</parameter> will be destroyed through
<parameter>destroy_data_func</parameter> if it is set.
@SINCE@         2.16.0
@@

@RET@           FcBool
@FUNC@          FcConfigAcceptFilter
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         const FcPattern *               @ARG2@          font
@PURPOSE@       Test whether the given pattern matches filter
@DESC@
This triggers a predicate function set by <function>FcConfigSetFontSetFilter</function>
and return FcTrue if <parameter>font</parameter> matches something they expect.
otherwise FcFalse.
@SINCE@         2.16.0
@@

@RET@           FcBool
@FUNC@          FcConfigUptoDate
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Check timestamps on config files
@DESC@
Checks all of the files related to <parameter>config</parameter> and returns
whether any of them has been modified since the configuration was created.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcChar8 *
@FUNC@          FcConfigHome
@TYPE1@         void
@PURPOSE@       return the current home directory.
@DESC@
Return the current user's home directory, if it is available, and if using it
is enabled, and NULL otherwise.
See also <function>FcConfigEnableHome</function>).
@@

@RET@           FcBool
@FUNC@          FcConfigEnableHome
@TYPE1@         FcBool%                         @ARG1@          enable
@PURPOSE@       controls use of the home directory.
@DESC@
If <parameter>enable</parameter> is FcTrue, then Fontconfig will use various
files which are specified relative to the user's home directory (using the ~
notation in the configuration). When <parameter>enable</parameter> is
FcFalse, then all use of the home directory in these contexts will be
disabled. The previous setting of the value is returned.
@@

@RET@           FcBool
@FUNC@          FcConfigBuildFonts
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Build font database
@DESC@
Builds the set of available fonts for the given configuration.  Note that
any changes to the configuration after this call (through
<function>FcConfigParseAndLoad</function> or
<function>FcConfigParseAndLoadFromMemory</function>) have indeterminate
effects. (On the other hand, application fonts can still be modified
through <function>FcConfigAppFontAddFile</function>,
<function>FcConfigAppFontAddDir</function> and
<function>FcConfigAppFontClear</function>). Returns FcFalse if this operation
runs out of memory. If <parameter>config</parameter> is NULL, the current
configuration is used.
@@

@RET@           FcStrList *
@FUNC@          FcConfigGetConfigDirs
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Get config directories
@DESC@
Returns the list of font directories specified in the configuration files
for <parameter>config</parameter>.  Does not include any subdirectories.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcStrList *
@FUNC@          FcConfigGetFontDirs
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Get font directories
@DESC@
Returns the list of font directories in <parameter>config</parameter>. This includes the
configured font directories along with any directories below those in the
filesystem.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcStrList *
@FUNC@          FcConfigGetConfigFiles
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Get config files
@DESC@
Returns the list of known configuration files used to generate <parameter>config</parameter>.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcChar8 *
@FUNC@          FcConfigGetCache
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       DEPRECATED used to return per-user cache filename
@DESC@
With fontconfig no longer using per-user cache files, this function now
simply returns NULL to indicate that no per-user file exists.
@@

@RET@           FcStrList *
@FUNC@          FcConfigGetCacheDirs
@TYPE1@         const FcConfig *                @ARG1@          config
@PURPOSE@       return the list of directories searched for cache files
@DESC@
<function>FcConfigGetCacheDirs</function> returns a string list containing
all of the directories that fontconfig will search when attempting to load a
cache file for a font directory.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcFontSet *
@FUNC@          FcConfigGetFonts
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcSetName%                      @ARG2@          set
@PURPOSE@       Get config font set
@DESC@
Returns one of the two sets of fonts from the configuration as specified
by <parameter>set</parameter>. This font set is owned by the library and must
not be modified or freed.
If <parameter>config</parameter> is NULL, the current configuration is used.
    </para><para>
This function isn't MT-safe. <function>FcConfigReference</function> must be called
before using this and then <function>FcConfigDestroy</function> when
the return value is no longer referenced.
@@

@RET@           FcBlanks *
@FUNC@          FcConfigGetBlanks
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Get config blanks
@DESC@
FcBlanks is deprecated.
This function always returns NULL.
@@

@RET@           int
@FUNC@          FcConfigGetRescanInterval
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Get config rescan interval
@DESC@
Returns the interval between automatic checks of the configuration (in
seconds) specified in <parameter>config</parameter>.  The configuration is checked during
a call to FcFontList when this interval has passed since the last check.
An interval setting of zero disables automatic checks.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcBool
@FUNC@          FcConfigSetRescanInterval
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         int%                            @ARG2@          rescanInterval
@PURPOSE@       Set config rescan interval
@DESC@
Sets the rescan interval. Returns FcFalse if the interval cannot be set (due
to allocation failure). Otherwise returns FcTrue.
An interval setting of zero disables automatic checks.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcBool
@FUNC@          FcConfigAppFontAddFile
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         const FcChar8 *                 @ARG2@          file
@PURPOSE@       Add font file to font database
@DESC@
Adds an application-specific font to the configuration. Returns FcFalse
if the fonts cannot be added (due to allocation failure or no fonts found).
Otherwise returns FcTrue. If <parameter>config</parameter> is NULL,
the current configuration is used.
@@

@RET@           FcBool
@FUNC@          FcConfigAppFontAddDir
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         const FcChar8 *                 @ARG2@          dir
@PURPOSE@       Add fonts from directory to font database
@DESC@
Scans the specified directory for fonts, adding each one found to the
application-specific set of fonts. Returns FcFalse
if the fonts cannot be added (due to allocation failure).
Otherwise returns FcTrue. If <parameter>config</parameter> is NULL,
the current configuration is used.
@@

@RET@           void
@FUNC@          FcConfigAppFontClear
@TYPE1@         FcConfig *                      @ARG1@          config
@PURPOSE@       Remove all app fonts from font database
@DESC@
Clears the set of application-specific fonts.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcBool
@FUNC@          FcConfigSubstituteWithPat
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcPattern *                     @ARG2@          p
@TYPE3@         FcPattern *                     @ARG3@          p_pat
@TYPE4@         FcMatchKind%                     @ARG4@          kind
@PURPOSE@       Execute substitutions
@DESC@
Performs the sequence of pattern modification operations, if <parameter>kind</parameter> is
FcMatchPattern, then those tagged as pattern operations are applied, else
if <parameter>kind</parameter> is FcMatchFont, those tagged as font operations are applied and
p_pat is used for &lt;test&gt; elements with target=pattern. Returns FcFalse
if the substitution cannot be performed (due to allocation failure). Otherwise returns FcTrue.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcBool
@FUNC@          FcConfigSubstitute
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcPattern *                     @ARG2@          p
@TYPE3@         FcMatchKind%                    @ARG3@          kind
@PURPOSE@       Execute substitutions
@DESC@
Calls FcConfigSubstituteWithPat setting p_pat to NULL. Returns FcFalse
if the substitution cannot be performed (due to allocation failure). Otherwise returns FcTrue.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcPattern *
@FUNC@          FcFontMatch
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcPattern *                     @ARG2@          p
@TYPE3@         FcResult *                      @ARG3@          result
@PURPOSE@       Return best font
@DESC@
Finds the font in <parameter>sets</parameter> most closely matching
<parameter>pattern</parameter> and returns the result of
<function>FcFontRenderPrepare</function> for that font and the provided
pattern. This function should be called only after
<function>FcConfigSubstitute</function> and
<function>FcDefaultSubstitute</function> have been called for
<parameter>p</parameter>; otherwise the results will not be correct.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcFontSet *
@FUNC@          FcFontSort
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcPattern *                     @ARG2@          p
@TYPE3@         FcBool%                         @ARG3@          trim
@TYPE4@         FcCharSet **                    @ARG4@          csp
@TYPE5@         FcResult *                      @ARG5@          result
@PURPOSE@       Return list of matching fonts
@DESC@
Returns the list of fonts sorted by closeness to <parameter>p</parameter>.  If <parameter>trim</parameter> is FcTrue,
elements in the list which don't include Unicode coverage not provided by
earlier elements in the list are elided.  The union of Unicode coverage of
all of the fonts is returned in <parameter>csp</parameter>, if <parameter>csp</parameter> is not NULL.  This function
should be called only after FcConfigSubstitute and FcDefaultSubstitute have
been called for <parameter>p</parameter>; otherwise the results will not be correct.
    </para><para>
The returned FcFontSet references FcPattern structures which may be shared
by the return value from multiple FcFontSort calls, applications must not
modify these patterns.  Instead, they should be passed, along with <parameter>p</parameter> to
<function>FcFontRenderPrepare</function> which combines them into a complete pattern.
    </para><para>
The FcFontSet returned by FcFontSort is destroyed by calling FcFontSetDestroy.
If <parameter>config</parameter> is NULL, the current configuration is used.
@@

@RET@           FcPattern *
@FUNC@          FcFontRenderPrepare
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcPattern *                     @ARG2@          pat
@TYPE3@         FcPattern *                     @ARG3@          font
@PURPOSE@       Prepare pattern for loading font file
@DESC@
Creates a new pattern consisting of elements of <parameter>font</parameter> not appearing
in <parameter>pat</parameter>, elements of <parameter>pat</parameter> not appearing in <parameter>font</parameter> and the best matching
value from <parameter>pat</parameter> for elements appearing in both.  The result is passed to
FcConfigSubstituteWithPat with <parameter>kind</parameter> FcMatchFont and then returned.
@@

@RET@           FcFontSet *
@FUNC@          FcFontList
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcPattern *                     @ARG2@          p
@TYPE3@         FcObjectSet *                   @ARG3@          os
@PURPOSE@       List fonts
@DESC@
Selects fonts matching <parameter>p</parameter>, creates patterns from those fonts containing
only the objects in <parameter>os</parameter> and returns the set of unique such patterns.
If <parameter>config</parameter> is NULL, the default configuration is checked
to be up to date, and used.
@@

@RET@           FcChar8 *
@FUNC@          FcConfigFilename
@TYPE1@         const FcChar8 *                 @ARG1@          name
@PURPOSE@       Find a config file
@DESC@
This function is deprecated and is replaced by <function>FcConfigGetFilename</function>.
@@

@RET@           FcChar8 *
@FUNC@          FcConfigGetFilename
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         const FcChar8 *                 @ARG2@          name
@PURPOSE@       Find a config file
@DESC@
Given the specified external entity name, return the associated filename.
This provides applications a way to convert various configuration file
references into filename form.
    </para><para>
A null or empty <parameter>name</parameter> indicates that the default configuration file should
be used; which file this references can be overridden with the
FONTCONFIG_FILE environment variable.  Next, if the name starts with <parameter>~</parameter>, it
refers to a file in the current users home directory.  Otherwise if the name
doesn't start with '/', it refers to a file in the default configuration
directory; the built-in default directory can be overridden with the
FONTCONFIG_PATH environment variable.
    </para><para>
The result of this function is affected by the FONTCONFIG_SYSROOT environment variable or equivalent functionality.
@@

@RET@           FcBool
@FUNC@          FcConfigParseAndLoad
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         const FcChar8 *                 @ARG2@          file
@TYPE3@         FcBool%                         @ARG3@          complain
@PURPOSE@       load a configuration file
@DESC@
Walks the configuration in 'file' and constructs the internal representation
in 'config'.  Any include files referenced from within 'file' will be loaded
and parsed.  If 'complain' is FcFalse, no warning will be displayed if
'file' does not exist. Error and warning messages will be output to stderr.
Returns FcFalse if some error occurred while loading the file, either a
parse error, semantic error or allocation failure. Otherwise returns FcTrue.

After all configuration files / strings have been loaded, with
<function>FcConfigParseAndLoad</function> and/or
<function>FcConfigParseAndLoadFromMemory</function>, call
<function>FcConfigBuildFonts</function> to build the font
database.
@@

@RET@           FcBool
@FUNC@          FcConfigParseAndLoadFromMemory
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         const FcChar8 *                 @ARG2@          buffer
@TYPE3@         FcBool%                         @ARG3@          complain
@PURPOSE@       load a configuration from memory
@DESC@
Walks the configuration in 'memory' and constructs the internal representation
in 'config'.  Any includes files referenced from within 'memory' will be loaded
and dparsed.  If 'complain' is FcFalse, no warning will be displayed if
'file' does not exist. Error and warning messages will be output to stderr.
Returns FcFalse if fsome error occurred while loading the file, either a
parse error, semantic error or allocation failure. Otherwise returns FcTrue.

After all configuration files / strings have been loaded, with
<function>FcConfigParseAndLoad</function> and/or
<function>FcConfigParseAndLoadFromMemory</function>, call
<function>FcConfigBuildFonts</function> to build the font
database.
@SINCE@         2.12.5
@@

@RET@           const FcChar8 *
@FUNC@          FcConfigGetSysRoot
@TYPE1@         const FcConfig *                @ARG1@          config
@PURPOSE@       Obtain the system root directory
@DESC@
Obtains the system root directory in 'config' if available. All files
(including file properties in patterns) obtained from this 'config' are
relative to this system root directory.
    </para><para>
This function isn't MT-safe. <function>FcConfigReference</function> must be called
before using this and then <function>FcConfigDestroy</function> when
the return value is no longer referenced.
@SINCE@         2.10.92
@@

@RET@           void
@FUNC@          FcConfigSetSysRoot
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         const FcChar8 *                 @ARG2@          sysroot
@PURPOSE@       Set the system root directory
@DESC@
Set 'sysroot' as the system root directory. All file paths used or created with
this 'config' (including file properties in patterns) will be considered or
made relative to this 'sysroot'. This allows a host to generate caches for
targets at build time. This also allows a cache to be re-targeted to a
different base directory if 'FcConfigGetSysRoot' is used to resolve file paths.
When setting this on the current config this causes changing current config
(calls FcConfigSetCurrent()).
@SINCE@         2.10.92
@@

@RET@           void
@FUNC@          FcConfigFileInfoIterInit
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcConfigFileInfoIter *          @ARG2@          iter
@PURPOSE@       Initialize the iterator
@DESC@
Initialize 'iter' with the first iterator in the config file information list.
    </para><para>
The config file information list is stored in numerical order for filenames
i.e. how fontconfig actually read them.
    </para><para>
This function isn't MT-safe. <function>FcConfigReference</function> must be called
before using this and then <function>FcConfigDestroy</function> when the relevant
values are no longer referenced.
@SINCE@         2.12.91
@@

@RET@           FcBool
@FUNC@          FcConfigFileInfoIterNext
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcConfigFileInfoIter *          @ARG2@          iter
@PURPOSE@       Set the iterator to point to the next list
@DESC@
Set 'iter' to point to the next node in the config file information list.
If there is no next node, FcFalse is returned.
    </para><para>
This function isn't MT-safe. <function>FcConfigReference</function> must be called
before using <function>FcConfigFileInfoIterInit</function> and then
<function>FcConfigDestroy</function> when the relevant values are no longer referenced.
@SINCE@         2.12.91
@@

@RET@           FcBool
@FUNC@          FcConfigFileInfoIterGet
@TYPE1@         FcConfig *                      @ARG1@          config
@TYPE2@         FcConfigFileInfoIter *          @ARG2@          iter
@TYPE3@         FcChar8 **                      @ARG3@          name
@TYPE4@         FcChar8 **                      @ARG4@          description
@TYPE5@         FcBool *                        @ARG5@          enabled
@PURPOSE@       Obtain the configuration file information
@DESC@
Obtain the filename, the description and the flag whether it is enabled or not
for 'iter' where points to current configuration file information.
If the iterator is invalid, FcFalse is returned.
    </para><para>
This function isn't MT-safe. <function>FcConfigReference</function> must be called
before using <function>FcConfigFileInfoIterInit</function> and then
<function>FcConfigDestroy</function> when the relevant values are no longer referenced.
@SINCE@         2.12.91
@@

@RET@		FcBool
@FUNC@		FcConfigAcceptFont
@TYPE1@		FcConfig *			@ARG1@		config
@TYPE2@		FcPattern *			@ARG2@		pat
@PURPOSE@	Test whether the given pattern matches deny list
@DESC@
fontconfig has the deny list which is built against &lt;rejectfont&gt; directive
in configuration file. This function tries to match 'pat' with them and
return FcFalse if 'pat' is rejected, otherwise FcTrue.
@SINCE@		2.15.1
@@

@RET@       void
@FUNC@      FcConfigPreferAppFont
@TYPE1@     FcConfig *          @ARG1@      config
@TYPE2@     FcBool%             @ARG2@      flag
@DESC@
Set 'flag' to change the priority of Application fonts. default behavior is turned off
for backward compatibility.
@SINCE@     2.17.0
@@
