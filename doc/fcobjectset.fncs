/*
 * fontconfig/doc/fcobjectset.fncs
 *
 * Copyright © 2003 <PERSON>
 *
 * Permission to use, copy, modify, distribute, and sell this software and its
 * documentation for any purpose is hereby granted without fee, provided that
 * the above copyright notice appear in all copies and that both that
 * copyright notice and this permission notice appear in supporting
 * documentation, and that the name of the author(s) not be used in
 * advertising or publicity pertaining to distribution of the software without
 * specific, written prior permission.  The authors make no
 * representations about the suitability of this software for any purpose.  It
 * is provided "as is" without express or implied warranty.
 *
 * THE AUTHOR(S) DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
 * INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
 * EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
 * CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
 * DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
 * TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */
@RET@           FcObjectSet *
@FUNC@          FcObjectSetCreate
@TYPE1@         void
@PURPOSE@       Create an object set
@DESC@
Creates an empty set.
@@

@RET@           FcBool
@FUNC@          FcObjectSetAdd
@TYPE1@         FcObjectSet *                   @ARG1@          os
@TYPE2@         const char *                    @ARG2@          object
@PURPOSE@       Add to an object set
@DESC@
Adds a property name to the set. Returns FcFalse if the property name cannot be
inserted into the set (due to allocation failure). Otherwise returns FcTrue.
@@

@RET@           void
@FUNC@          FcObjectSetDestroy
@TYPE1@         FcObjectSet *                   @ARG1@          os
@PURPOSE@       Destroy an object set
@DESC@
Destroys an object set.
@@

@RET@           FcObjectSet *
@FUNC@          FcObjectSetBuild
@TYPE1@         const char *                    @ARG1@          first
@TYPE2@         ...

@PROTOTYPE+@
@RET+@          FcObjectSet *
@FUNC+@         FcObjectSetVaBuild
@TYPE1+@        const char *                    @ARG1+@         first
@TYPE2+@        va_list%                        @ARG2+@         va

@PROTOTYPE++@
@RET++@         void
@FUNC++@        FcObjectSetVapBuild
@TYPE1++@       FcObjectSet *                   @ARG1++@        result
@TYPE2++@       const char *                    @ARG2++@        first
@TYPE3++@       va_list%                        @ARG3++@        va

@PURPOSE@       Build object set from args
@DESC@
These build an object set from a null-terminated list of property names.
FcObjectSetVapBuild is a macro version of FcObjectSetVaBuild which returns
the result in the <parameter>result</parameter> variable directly.
@@
