/*
 * fontconfig/doc/fcstring.fncs
 *
 * Copyright © 2003 <PERSON>
 *
 * Permission to use, copy, modify, distribute, and sell this software and its
 * documentation for any purpose is hereby granted without fee, provided that
 * the above copyright notice appear in all copies and that both that
 * copyright notice and this permission notice appear in supporting
 * documentation, and that the name of the author(s) not be used in
 * advertising or publicity pertaining to distribution of the software without
 * specific, written prior permission.  The authors make no
 * representations about the suitability of this software for any purpose.  It
 * is provided "as is" without express or implied warranty.
 *
 * THE AUTHOR(S) DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
 * INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
 * EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
 * CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
 * DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
 * TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */
    <variablelist>

@RET@           int
@FUNC@          FcUtf8ToUcs4
@TYPE1@         FcChar8 *                       @ARG1@          src
@TYPE2@         FcChar32 *                      @ARG2@          dst
@TYPE3@         int%                            @ARG3@          len
@PURPOSE@       convert UTF-8 to UCS4
@DESC@
Converts the next Unicode char from <parameter>src</parameter> into
<parameter>dst</parameter> and returns the number of bytes containing the
char.  <parameter>src</parameter> must be at least
<parameter>len</parameter> bytes long.
@@

@RET@           int
@FUNC@          FcUcs4ToUtf8
@TYPE1@         FcChar32%                       @ARG1@          src
@TYPE2@         FcChar8%                        @ARG2@          dst[FC_UTF8_MAX_LEN]
@PURPOSE@       convert UCS4 to UTF-8
@DESC@
Converts the Unicode char from <parameter>src</parameter> into
<parameter>dst</parameter> and returns the number of bytes needed to encode
the char.
@@

@RET@           FcBool
@FUNC@          FcUtf8Len
@TYPE1@         FcChar8 *                       @ARG1@          src
@TYPE2@         int%                            @ARG2@          len
@TYPE3@         int *                           @ARG3@          nchar
@TYPE4@         int *                           @ARG4@          wchar
@PURPOSE@       count UTF-8 encoded chars
@DESC@
Counts the number of Unicode chars in <parameter>len</parameter> bytes of
<parameter>src</parameter>.  Places that count in
<parameter>nchar</parameter>.  <parameter>wchar</parameter> contains 1, 2 or
4 depending on the number of bytes needed to hold the largest Unicode char
counted.  The return value indicates whether <parameter>src</parameter> is a
well-formed UTF8 string.
@@

@RET@           int
@FUNC@          FcUtf16ToUcs4
@TYPE1@         FcChar8 *                       @ARG1@          src
@TYPE2@         FcEndian%                       @ARG2@          endian
@TYPE3@         FcChar32 *                      @ARG3@          dst
@TYPE4@         int%                            @ARG4@          len
@PURPOSE@       convert UTF-16 to UCS4
@DESC@
Converts the next Unicode char from <parameter>src</parameter> into
<parameter>dst</parameter> and returns the number of bytes containing the
char. <parameter>src</parameter> must be at least <parameter>len</parameter>
bytes long.  Bytes of <parameter>src</parameter> are combined into 16-bit
units according to <parameter>endian</parameter>.
@@

@RET@           FcBool
@FUNC@          FcUtf16Len
@TYPE1@         FcChar8 *                       @ARG1@          src
@TYPE2@         FcEndian%                       @ARG2@          endian
@TYPE3@         int%                            @ARG3@          len
@TYPE4@         int *                           @ARG4@          nchar
@TYPE5@         int *                           @ARG5@          wchar
@PURPOSE@       count UTF-16 encoded chars
@DESC@
Counts the number of Unicode chars in <parameter>len</parameter> bytes of
<parameter>src</parameter>.  Bytes of <parameter>src</parameter> are
combined into 16-bit units according to <parameter>endian</parameter>.
Places that count in <parameter>nchar</parameter>.
<parameter>wchar</parameter> contains 1, 2 or 4 depending on the number of
bytes needed to hold the largest Unicode char counted.  The return value
indicates whether <parameter>string</parameter> is a well-formed UTF16
string.
@@

@RET@           FcBool
@FUNC@          FcIsLower
@TYPE1@         FcChar8%                        @ARG1@          c
@PURPOSE@       check for lower case ASCII character
@DESC@
This macro checks whether <parameter>c</parameter> is an lower case ASCII
letter.
@@

@RET@           FcBool
@FUNC@          FcIsUpper
@TYPE1@         FcChar8%                        @ARG1@          c
@PURPOSE@       check for upper case ASCII character
@DESC@
This macro checks whether <parameter>c</parameter> is a upper case ASCII
letter.
@@

@RET@           FcChar8
@FUNC@          FcToLower
@TYPE1@         FcChar8%                        @ARG1@          c
@PURPOSE@       convert upper case ASCII to lower case
@DESC@
This macro converts upper case ASCII <parameter>c</parameter> to the
equivalent lower case letter.
@@

@RET@           FcChar8 *
@FUNC@          FcStrCopy
@TYPE1@         const FcChar8 *                 @ARG1@          s
@PURPOSE@       duplicate a string
@DESC@
Allocates memory, copies <parameter>s</parameter> and returns the resulting
buffer.  Yes, this is <function>strdup</function>, but that function isn't
available on every platform.
@@

@RET@           FcChar8 *
@FUNC@          FcStrDowncase
@TYPE1@         const FcChar8 *                 @ARG1@          s
@PURPOSE@       create a lower case translation of a string
@DESC@
Allocates memory, copies <parameter>s</parameter>, converting upper case
letters to lower case and returns the allocated buffer.
@@

@RET@           FcChar8 *
@FUNC@          FcStrCopyFilename
@TYPE1@         const FcChar8 *                 @ARG1@          s
@PURPOSE@       create a complete path from a filename
@DESC@
<function>FcStrCopyFilename</function> constructs an absolute pathname from
<parameter>s</parameter>. It converts any leading '~' characters in
to the value of the HOME environment variable, and any relative paths are
converted to absolute paths using the current working directory. Sequences
of '/' characters are converted to a single '/', and names containing the
current directory '.' or parent directory '..' are correctly reconstructed.
Returns NULL if '~' is the leading character and HOME is unset or disabled
(see <function>FcConfigEnableHome</function>).
@@

@RET@           int
@FUNC@          FcStrCmp
@TYPE1@         const FcChar8 *                 @ARG1@          s1
@TYPE2@         const FcChar8 *                 @ARG2@          s2
@PURPOSE@       compare UTF-8 strings
@DESC@
Returns the usual &lt;0, 0, &gt;0 result of comparing
<parameter>s1</parameter> and <parameter>s2</parameter>.
@@

@RET@           int
@FUNC@          FcStrCmpIgnoreCase
@TYPE1@         const FcChar8 *                 @ARG1@          s1
@TYPE2@         const FcChar8 *                 @ARG2@          s2
@PURPOSE@       compare UTF-8 strings ignoring case
@DESC@
Returns the usual &lt;0, 0, &gt;0 result of comparing
<parameter>s1</parameter> and <parameter>s2</parameter>. This test is
case-insensitive for all proper UTF-8 encoded strings.
@@

@RET@           FcChar8 *
@FUNC@          FcStrStr
@TYPE1@         const FcChar8 *                 @ARG1@          s1
@TYPE2@         const FcChar8 *                 @ARG2@          s2
@PURPOSE@       locate UTF-8 substring
@DESC@
Returns the location of <parameter>s2</parameter> in
<parameter>s1</parameter>.  Returns NULL if <parameter>s2</parameter>
is not present in <parameter>s1</parameter>. This test will operate properly
with UTF8 encoded strings.
@@

@RET@           FcChar8 *
@FUNC@          FcStrStrIgnoreCase
@TYPE1@         const FcChar8 *                 @ARG1@          s1
@TYPE2@         const FcChar8 *                 @ARG2@          s2
@PURPOSE@       locate UTF-8 substring ignoring case
@DESC@
Returns the location of <parameter>s2</parameter> in
<parameter>s1</parameter>, ignoring case.  Returns NULL if
<parameter>s2</parameter> is not present in <parameter>s1</parameter>.
This test is case-insensitive for all proper UTF-8 encoded strings.
@@

@RET@           FcChar8 *
@FUNC@          FcStrPlus
@TYPE1@         const FcChar8 *                 @ARG1@          s1
@TYPE2@         const FcChar8 *                 @ARG2@          s2
@PURPOSE@       concatenate two strings
@DESC@
This function allocates new storage and places the concatenation of
<parameter>s1</parameter> and <parameter>s2</parameter> there, returning the
new string.
@@

@RET@           void
@FUNC@          FcStrFree
@TYPE1@         FcChar8 *                       @ARG1@          s
@PURPOSE@       free a string
@DESC@
This is just a wrapper around free(3) which helps track memory usage of
strings within the fontconfig library.
@@

@RET@           FcChar8 *
@FUNC@          FcStrBuildFilename
@TYPE1@         const FcChar8 *                 @ARG1@          path
@TYPE2@         ...
@PURPOSE@       Concatenate strings as a file path
@DESC@
Creates a filename from the given elements of strings as file paths
and concatenate them with the appropriate file separator.
Arguments must be null-terminated.
This returns a newly-allocated memory which should be freed when no longer needed.
@@

@RET@           FcChar8 *
@FUNC@          FcStrDirname
@TYPE1@         const FcChar8 *                 @ARG1@          file
@PURPOSE@       directory part of filename
@DESC@
Returns the directory containing <parameter>file</parameter>.  This
is returned in newly allocated storage which should be freed when no longer
needed.
@@

@RET@           FcChar8 *
@FUNC@          FcStrBasename
@TYPE1@         const FcChar8 *                 @ARG1@          file
@PURPOSE@       last component of filename
@DESC@
Returns the filename of <parameter>file</parameter> stripped of any leading
directory names.  This is returned in newly allocated storage which should
be freed when no longer needed.
@@
