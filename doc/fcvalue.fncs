/*
 * fontconfig/doc/fcvalue.fncs
 *
 * Copyright © 2003 <PERSON>
 *
 * Permission to use, copy, modify, distribute, and sell this software and its
 * documentation for any purpose is hereby granted without fee, provided that
 * the above copyright notice appear in all copies and that both that
 * copyright notice and this permission notice appear in supporting
 * documentation, and that the name of the author(s) not be used in
 * advertising or publicity pertaining to distribution of the software without
 * specific, written prior permission.  The authors make no
 * representations about the suitability of this software for any purpose.  It
 * is provided "as is" without express or implied warranty.
 *
 * THE AUTHOR(S) DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
 * INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
 * EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
 * CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
 * DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
 * TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 */
@RET@           void
@FUNC@          FcValueDestroy
@TYPE1@         FcValue%                @ARG1@          v
@PURPOSE@       Free a value
@DESC@
Frees any memory referenced by <parameter>v</parameter>.  Values of type FcTypeString,
FcTypeMatrix and FcTypeCharSet reference memory, the other types do not.
@@

@RET@           FcValue
@FUNC@          FcValueSave
@TYPE1@         FcValue%                @ARG1@          v
@PURPOSE@       Copy a value
@DESC@
Returns a copy of <parameter>v</parameter> duplicating any object referenced by it so that <parameter>v</parameter>
may be safely destroyed without harming the new value.
@@

@RET@           void
@FUNC@          FcValuePrint
@TYPE1@         FcValue%                @ARG1@          v
@PURPOSE@       Print a value to stdout
@DESC@
Prints a human-readable representation of <parameter>v</parameter> to
stdout. The format should not be considered part of the library
specification as it may change in the future.
@@

@RET@           FcBool
@FUNC@          FcValueEqual
@TYPE1@         FcValue%                @ARG1@          v_a
@TYPE2@         FcValue%                @ARG2@          v_b
@PURPOSE@       Test two values for equality
@DESC@
Compares two values. Integers and Doubles are compared as numbers; otherwise
the two values have to be the same type to be considered equal. Strings are
compared ignoring case.
@@
