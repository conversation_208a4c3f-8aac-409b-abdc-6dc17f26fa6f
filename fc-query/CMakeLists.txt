# fc-query tool

add_executable(fc-query
    fc-query.c
    ${CMAKE_CURRENT_BINARY_DIR}/../src/fcstdint.h
    ${CMAKE_CURRENT_BINARY_DIR}/../src/fcalias.h
    ${CMAKE_CURRENT_BINARY_DIR}/../src/fcaliastail.h
    ${CMAKE_CURRENT_BINARY_DIR}/../src/fcftalias.h
    ${CMAKE_CURRENT_BINARY_DIR}/../src/fcftaliastail.h
)

target_include_directories(fc-query PRIVATE
    ${INCBASE}
    ${INCSRC}
    ${CMAKE_CURRENT_BINARY_DIR}/../src
)

target_link_libraries(fc-query PRIVATE fontconfig_internal)

if(Intl_FOUND)
    target_link_libraries(fc-query PRIVATE ${Intl_LIBRARIES})
    target_include_directories(fc-query PRIVATE ${Intl_INCLUDE_DIRS})
endif()

# Add dependencies on generated files
add_dependencies(fc-query fccase_h fclang_h)

install(TARGETS fc-query
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    COMPONENT Tools
)